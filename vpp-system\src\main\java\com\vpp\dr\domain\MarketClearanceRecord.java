package com.vpp.dr.domain;

import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import java.util.Date;

/**
 * 市场出清记录实体类（对应市场出清记录页面数据）
 */
@Data
public class MarketClearanceRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 发布时间（页面显示：2025-03-24 15:30:60） */
    private Date publishTime;

    /** 发送聚合用户类型ID（关联vpp_user_type表id） */
    private Long sendVppUserTypeId;

    /** 发送聚合用户类型名称（冗余字段，页面展示，如：全部聚合用户） */
    private String sendVppUserTypeName;

    /** 权限状态ID（关联vpp_permission_status表id） */
    private Long vppPermissionStatusId;

    /** 权限状态名称（冗余字段，页面展示，如：开启） */
    private String vppPermissionStatusName;

    /** 记录类型（区分发布/历史记录，如：1-发布，2-历史记录） */
    private String recordType;

    /** 发布时间（数据库存储格式，用于查询） */
    private Date createTime;

    /** 更新时间（数据库自动生成） */
    private Date updateTime;

    /** 删除标志（0-未删除，2-已删除） */
    private String delFlag;
}
