package com.vpp.aggregator.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 虚拟电厂聚合商对象 vpp_base
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public class VppBase extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long vppId;

    /**
     * 虚拟电厂名称
     */
    @Excel(name = "虚拟电厂名称")
    private String vppName;

    /**
     * 公司主体代码，对应corporation表
     */
    @Excel(name = "公司主体代码，对应corporation表")
    private String vppCorporationCode;

    /**
     * 状态，0正常，其他自定
     */
    @Excel(name = "状态，0正常，其他自定")
    private Long vppStatus;

    /**
     * 虚拟电厂主类型，0：源，1：储，2：荷，3：综合
     */
    @Excel(name = "虚拟电厂主类型，0：源，1：储，2：荷，3：综合")
    private Long vppType;

    public void setVppId(Long vppId) {
        this.vppId = vppId;
    }

    public Long getVppId() {
        return vppId;
    }

    public void setVppName(String vppName) {
        this.vppName = vppName;
    }

    public String getVppName() {
        return vppName;
    }

    public void setVppCorporationCode(String vppCorporationCode) {
        this.vppCorporationCode = vppCorporationCode;
    }

    public String getVppCorporationCode() {
        return vppCorporationCode;
    }

    public void setVppStatus(Long vppStatus) {
        this.vppStatus = vppStatus;
    }

    public Long getVppStatus() {
        return vppStatus;
    }

    public void setVppType(Long vppType) {
        this.vppType = vppType;
    }

    public Long getVppType() {
        return vppType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("vppId", getVppId())
                .append("vppName", getVppName())
                .append("vppCorporationCode", getVppCorporationCode())
                .append("vppStatus", getVppStatus())
                .append("vppType", getVppType())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}