package com.vpp.sm.mapper;

import com.vpp.sm.domain.VppSettlementRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 结算记录Mapper接口（MyBatis）
 */
@Mapper
public interface VppSettlementRecordMapper {

    /**
     * 根据邀约计划ID查询结算记录列表
     * @param invitationId 邀约计划ID
     * @return 结算记录列表
     */
    List<VppSettlementRecord> selectByInvitationId(Long invitationId);

    /**
     * 插入结算记录（含主键自增）
     * @param record 结算记录实体
     * @return 影响行数
     */
    int insert(VppSettlementRecord record);

    /**
     * 查询所有结算记录（用于统计概览）
     * @return 结算记录列表
     */
    List<VppSettlementRecord> selectAll();
}