package com.vpp.execute.service.impl;

import com.vpp.execute.domain.ExecuteDeviceStatus;
import com.vpp.execute.mapper.ExecuteDeviceStatusMapper;
import com.vpp.execute.service.IExecuteDeviceStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 设备实时检测状态Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class ExecuteDeviceStatusServiceImpl implements IExecuteDeviceStatusService {
    @Autowired
    private ExecuteDeviceStatusMapper executeDeviceStatusMapper;

    /**
     * 查询设备实时检测状态
     *
     * @param timestamp 设备实时检测状态主键
     * @return 设备实时检测状态
     */
    @Override
    public ExecuteDeviceStatus selectExecuteDeviceStatusByTimestamp(Date timestamp) {
        return executeDeviceStatusMapper.selectExecuteDeviceStatusByTimestamp(timestamp);
    }

    /**
     * 查询设备实时检测状态列表
     *
     * @param executeDeviceStatus 设备实时检测状态
     * @return 设备实时检测状态
     */
    @Override
    public List<ExecuteDeviceStatus> selectExecuteDeviceStatusList(ExecuteDeviceStatus executeDeviceStatus) {
        return executeDeviceStatusMapper.selectExecuteDeviceStatusList(executeDeviceStatus);
    }

    /**
     * 新增设备实时检测状态
     *
     * @param executeDeviceStatus 设备实时检测状态
     * @return 结果
     */
    @Override
    public int insertExecuteDeviceStatus(ExecuteDeviceStatus executeDeviceStatus) {
        return executeDeviceStatusMapper.insertExecuteDeviceStatus(executeDeviceStatus);
    }

    /**
     * 修改设备实时检测状态
     *
     * @param executeDeviceStatus 设备实时检测状态
     * @return 结果
     */
    @Override
    public int updateExecuteDeviceStatus(ExecuteDeviceStatus executeDeviceStatus) {
        return executeDeviceStatusMapper.updateExecuteDeviceStatus(executeDeviceStatus);
    }

    /**
     * 批量删除设备实时检测状态
     *
     * @param timestamps 需要删除的设备实时检测状态主键
     * @return 结果
     */
    @Override
    public int deleteExecuteDeviceStatusByTimestamps(Date[] timestamps) {
        return executeDeviceStatusMapper.deleteExecuteDeviceStatusByTimestamps(timestamps);
    }

    /**
     * 删除设备实时检测状态信息
     *
     * @param timestamp 设备实时检测状态主键
     * @return 结果
     */
    @Override
    public int deleteExecuteDeviceStatusByTimestamp(Date timestamp) {
        return executeDeviceStatusMapper.deleteExecuteDeviceStatusByTimestamp(timestamp);
    }
}