package com.vpp.aggregator.service.impl;

import com.vpp.aggregator.domain.VppBaseDevice;
import com.vpp.aggregator.mapper.VppBaseDeviceMapper;
import com.vpp.aggregator.service.IVppBaseDeviceService;
import com.vpp.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 聚合用户设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class VppBaseDeviceServiceImpl implements IVppBaseDeviceService {
    @Autowired
    private VppBaseDeviceMapper vppBaseDeviceMapper;

    /**
     * 查询聚合用户设备
     *
     * @param deviceId 聚合用户设备主键
     * @return 聚合用户设备
     */
    @Override
    public VppBaseDevice selectVppBaseDeviceByDeviceId(Long deviceId) {
        return vppBaseDeviceMapper.selectVppBaseDeviceByDeviceId(deviceId);
    }

    /**
     * 查询聚合用户设备列表
     *
     * @param vppBaseDevice 聚合用户设备
     * @return 聚合用户设备
     */
    @Override
    public List<VppBaseDevice> selectVppBaseDeviceList(VppBaseDevice vppBaseDevice) {
        return vppBaseDeviceMapper.selectVppBaseDeviceList(vppBaseDevice);
    }

    /**
     * 新增聚合用户设备
     *
     * @param vppBaseDevice 聚合用户设备
     * @return 结果
     */
    @Override
    public int insertVppBaseDevice(VppBaseDevice vppBaseDevice) {
        vppBaseDevice.setCreateTime(DateUtils.getNowDate());
        return vppBaseDeviceMapper.insertVppBaseDevice(vppBaseDevice);
    }

    /**
     * 修改聚合用户设备
     *
     * @param vppBaseDevice 聚合用户设备
     * @return 结果
     */
    @Override
    public int updateVppBaseDevice(VppBaseDevice vppBaseDevice) {
        vppBaseDevice.setUpdateTime(DateUtils.getNowDate());
        return vppBaseDeviceMapper.updateVppBaseDevice(vppBaseDevice);
    }

    /**
     * 批量删除聚合用户设备
     *
     * @param deviceIds 需要删除的聚合用户设备主键
     * @return 结果
     */
    @Override
    public int deleteVppBaseDeviceByDeviceIds(Long[] deviceIds) {
        return vppBaseDeviceMapper.deleteVppBaseDeviceByDeviceIds(deviceIds);
    }

    /**
     * 删除聚合用户设备信息
     *
     * @param deviceId 聚合用户设备主键
     * @return 结果
     */
    @Override
    public int deleteVppBaseDeviceByDeviceId(Long deviceId) {
        return vppBaseDeviceMapper.deleteVppBaseDeviceByDeviceId(deviceId);
    }
}