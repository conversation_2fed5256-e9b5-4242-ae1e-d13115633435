package com.vpp.aggregator.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 公司主体对象 vpp_base_corporation
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public class VppBaseCorporation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long corporationId;

    /**
     * 企业主体
     */
    @Excel(name = "企业主体")
    private String corporationName;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    private String corporationCode;

    /**
     * 法人姓名
     */
    @Excel(name = "法人姓名")
    private String corporationLegal;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String corporationPhone;

    /**
     * 固定电话
     */
    @Excel(name = "固定电话")
    private String corporationLandline;

    /**
     * 省
     */
    @Excel(name = "省")
    private Long corporationProvince;

    /**
     * 市
     */
    @Excel(name = "市")
    private Long corporationMunicipality;

    /**
     * 区
     */
    @Excel(name = "区")
    private Long corporationRegional;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String corporationAddre;

    /**
     * 部门(机构)ID
     */
    @Excel(name = "部门(机构)ID")
    private Long deptId;

    public void setCorporationId(Long corporationId) {
        this.corporationId = corporationId;
    }

    public Long getCorporationId() {
        return corporationId;
    }

    public void setCorporationName(String corporationName) {
        this.corporationName = corporationName;
    }

    public String getCorporationName() {
        return corporationName;
    }

    public void setCorporationCode(String corporationCode) {
        this.corporationCode = corporationCode;
    }

    public String getCorporationCode() {
        return corporationCode;
    }

    public void setCorporationLegal(String corporationLegal) {
        this.corporationLegal = corporationLegal;
    }

    public String getCorporationLegal() {
        return corporationLegal;
    }

    public void setCorporationPhone(String corporationPhone) {
        this.corporationPhone = corporationPhone;
    }

    public String getCorporationPhone() {
        return corporationPhone;
    }

    public void setCorporationLandline(String corporationLandline) {
        this.corporationLandline = corporationLandline;
    }

    public String getCorporationLandline() {
        return corporationLandline;
    }

    public void setCorporationProvince(Long corporationProvince) {
        this.corporationProvince = corporationProvince;
    }

    public Long getCorporationProvince() {
        return corporationProvince;
    }

    public void setCorporationMunicipality(Long corporationMunicipality) {
        this.corporationMunicipality = corporationMunicipality;
    }

    public Long getCorporationMunicipality() {
        return corporationMunicipality;
    }

    public void setCorporationRegional(Long corporationRegional) {
        this.corporationRegional = corporationRegional;
    }

    public Long getCorporationRegional() {
        return corporationRegional;
    }

    public void setCorporationAddre(String corporationAddre) {
        this.corporationAddre = corporationAddre;
    }

    public String getCorporationAddre() {
        return corporationAddre;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getDeptId() {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("corporationId", getCorporationId())
                .append("corporationName", getCorporationName())
                .append("corporationCode", getCorporationCode())
                .append("corporationLegal", getCorporationLegal())
                .append("corporationPhone", getCorporationPhone())
                .append("corporationLandline", getCorporationLandline())
                .append("corporationProvince", getCorporationProvince())
                .append("corporationMunicipality", getCorporationMunicipality())
                .append("corporationRegional", getCorporationRegional())
                .append("corporationAddre", getCorporationAddre())
                .append("deptId", getDeptId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}