package com.vpp.plan.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.plan.domain.PlanGenDetails;
import com.vpp.plan.mapper.PlanGenDetailsMapper;
import com.vpp.plan.service.IPlanGenDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 生成计划的详细信息，key-value形式Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class PlanGenDetailsServiceImpl implements IPlanGenDetailsService {
    @Autowired
    private PlanGenDetailsMapper planGenDetailsMapper;

    /**
     * 查询生成计划的详细信息，key-value形式
     *
     * @param planGenDetailsId 生成计划的详细信息，key-value形式主键
     * @return 生成计划的详细信息，key-value形式
     */
    @Override
    public PlanGenDetails selectPlanGenDetailsByPlanGenDetailsId(Long planGenDetailsId) {
        return planGenDetailsMapper.selectPlanGenDetailsByPlanGenDetailsId(planGenDetailsId);
    }

    /**
     * 查询生成计划的详细信息，key-value形式列表
     *
     * @param planGenDetails 生成计划的详细信息，key-value形式
     * @return 生成计划的详细信息，key-value形式
     */
    @Override
    public List<PlanGenDetails> selectPlanGenDetailsList(PlanGenDetails planGenDetails) {
        return planGenDetailsMapper.selectPlanGenDetailsList(planGenDetails);
    }

    /**
     * 新增生成计划的详细信息，key-value形式
     *
     * @param planGenDetails 生成计划的详细信息，key-value形式
     * @return 结果
     */
    @Override
    public int insertPlanGenDetails(PlanGenDetails planGenDetails) {
        planGenDetails.setCreateTime(DateUtils.getNowDate());
        return planGenDetailsMapper.insertPlanGenDetails(planGenDetails);
    }

    /**
     * 修改生成计划的详细信息，key-value形式
     *
     * @param planGenDetails 生成计划的详细信息，key-value形式
     * @return 结果
     */
    @Override
    public int updatePlanGenDetails(PlanGenDetails planGenDetails) {
        planGenDetails.setUpdateTime(DateUtils.getNowDate());
        return planGenDetailsMapper.updatePlanGenDetails(planGenDetails);
    }

    /**
     * 批量删除生成计划的详细信息，key-value形式
     *
     * @param planGenDetailsIds 需要删除的生成计划的详细信息，key-value形式主键
     * @return 结果
     */
    @Override
    public int deletePlanGenDetailsByPlanGenDetailsIds(Long[] planGenDetailsIds) {
        return planGenDetailsMapper.deletePlanGenDetailsByPlanGenDetailsIds(planGenDetailsIds);
    }

    /**
     * 删除生成计划的详细信息，key-value形式信息
     *
     * @param planGenDetailsId 生成计划的详细信息，key-value形式主键
     * @return 结果
     */
    @Override
    public int deletePlanGenDetailsByPlanGenDetailsId(Long planGenDetailsId) {
        return planGenDetailsMapper.deletePlanGenDetailsByPlanGenDetailsId(planGenDetailsId);
    }
}