package com.vpp.web.controller.td;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.td.domain.VppInvitationWinFeedback;
import com.vpp.td.service.IVppInvitationWinFeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dr/invitation/win-feedback")
@Api(tags = "邀约中标反馈管理")
public class VppInvitationWinFeedbackController extends BaseController {

    @Autowired
    private IVppInvitationWinFeedbackService winFeedbackService;

    /**
     * 查询中标反馈列表
     */
    @GetMapping("/list")
    @ApiOperation("查询中标反馈列表")
    public TableDataInfo list(
            @RequestParam(required = false) Long invitationId,
            @RequestParam(required = false) Integer sendStatus) {
        startPage();
        Map<String, Object> params = new HashMap<>();
        if (invitationId != null) params.put("invitationId", invitationId);
        if (sendStatus != null) params.put("sendStatus", sendStatus);
        List<VppInvitationWinFeedback> list = winFeedbackService.selectList(params);
        return getDataTable(list);
    }

    /**
     * 获取中标反馈详情
     */
    @GetMapping("/{winFeedbackId}")
    @ApiOperation("获取中标反馈详情")
    public AjaxResult getInfo(@PathVariable Long winFeedbackId) {
        return AjaxResult.success(winFeedbackService.selectById(winFeedbackId));
    }

    /**
     * 发送中标通知（更新发送状态）
     */
    @Log(title = "邀约中标反馈", businessType = BusinessType.UPDATE)
    @PutMapping("/{winFeedbackId}/send")
    @ApiOperation("发送中标通知")
    public AjaxResult send(@PathVariable Long winFeedbackId) {
        VppInvitationWinFeedback winFeedback = winFeedbackService.selectById(winFeedbackId);
        winFeedback.setSendStatus(1); // 设为已发送
        return toAjax(winFeedbackService.update(winFeedback));
    }
}