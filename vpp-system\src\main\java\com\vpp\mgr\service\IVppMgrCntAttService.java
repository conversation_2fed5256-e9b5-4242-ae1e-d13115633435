package com.vpp.mgr.service;

import com.vpp.mgr.domain.VppMgrCntAtt;

import java.util.List;

/**
 * 合同附件(聚合商-聚合用户)Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IVppMgrCntAttService {
    /**
     * 查询合同附件(聚合商-聚合用户)
     *
     * @param uagCntId 合同附件(聚合商-聚合用户)主键
     * @return 合同附件(聚合商 - 聚合用户)
     */
    public VppMgrCntAtt selectVppMgrCntAttByUagCntId(Long uagCntId);

    /**
     * 查询合同附件(聚合商-聚合用户)列表
     *
     * @param vppMgrCntAtt 合同附件(聚合商-聚合用户)
     * @return 合同附件(聚合商 - 聚合用户)集合
     */
    public List<VppMgrCntAtt> selectVppMgrCntAttList(VppMgrCntAtt vppMgrCntAtt);

    /**
     * 新增合同附件(聚合商-聚合用户)
     *
     * @param vppMgrCntAtt 合同附件(聚合商-聚合用户)
     * @return 结果
     */
    public int insertVppMgrCntAtt(VppMgrCntAtt vppMgrCntAtt);

    /**
     * 修改合同附件(聚合商-聚合用户)
     *
     * @param vppMgrCntAtt 合同附件(聚合商-聚合用户)
     * @return 结果
     */
    public int updateVppMgrCntAtt(VppMgrCntAtt vppMgrCntAtt);

    /**
     * 批量删除合同附件(聚合商-聚合用户)
     *
     * @param uagCntIds 需要删除的合同附件(聚合商-聚合用户)主键集合
     * @return 结果
     */
    public int deleteVppMgrCntAttByUagCntIds(Long[] uagCntIds);

    /**
     * 删除合同附件(聚合商-聚合用户)信息
     *
     * @param uagCntId 合同附件(聚合商-聚合用户)主键
     * @return 结果
     */
    public int deleteVppMgrCntAttByUagCntId(Long uagCntId);
}
