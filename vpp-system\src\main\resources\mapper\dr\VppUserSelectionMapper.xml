<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.dr.mapper.VppUserSelectionMapper">

    <resultMap id="SelectionMap" type="com.vpp.dr.domain.VppUserSelection">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="publish_id" property="publishId" jdbcType="BIGINT"/>
        <result column="user_type_code" property="userTypeCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="is_all" property="isAll" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectByPublishId" parameterType="Long" resultMap="SelectionMap">
        SELECT * FROM vpp_user_selection WHERE publish_id = #{publishId}
    </select>

    <insert id="insert" parameterType="VppUserSelection" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO vpp_user_selection
        (publish_id, user_type_code, user_id, is_all)
        VALUES
            (#{publishId}, #{userTypeCode}, #{userId}, #{isAll})
    </insert>

    <delete id="deleteByPublishId" parameterType="Long">
        DELETE FROM vpp_user_selection WHERE publish_id = #{publishId}
    </delete>

</mapper>