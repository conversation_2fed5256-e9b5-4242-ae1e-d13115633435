package com.vpp.mgr.mapper;

import com.vpp.mgr.domain.VppMgrCnt;

import java.util.List;

/**
 * 合同(聚合商-聚合用户)Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface VppMgrCntMapper {
    /**
     * 查询合同(聚合商-聚合用户)
     *
     * @param cntId 合同(聚合商-聚合用户)主键
     * @return 合同(聚合商 - 聚合用户)
     */
    public VppMgrCnt selectVppMgrCntByCntId(Long cntId);

    /**
     * 查询合同(聚合商-聚合用户)列表
     *
     * @param vppMgrCnt 合同(聚合商-聚合用户)
     * @return 合同(聚合商 - 聚合用户)集合
     */
    public List<VppMgrCnt> selectVppMgrCntList(VppMgrCnt vppMgrCnt);

    /**
     * 新增合同(聚合商-聚合用户)
     *
     * @param vppMgrCnt 合同(聚合商-聚合用户)
     * @return 结果
     */
    public int insertVppMgrCnt(VppMgrCnt vppMgrCnt);

    /**
     * 修改合同(聚合商-聚合用户)
     *
     * @param vppMgrCnt 合同(聚合商-聚合用户)
     * @return 结果
     */
    public int updateVppMgrCnt(VppMgrCnt vppMgrCnt);

    /**
     * 删除合同(聚合商-聚合用户)
     *
     * @param cntId 合同(聚合商-聚合用户)主键
     * @return 结果
     */
    public int deleteVppMgrCntByCntId(Long cntId);

    /**
     * 批量删除合同(聚合商-聚合用户)
     *
     * @param cntIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVppMgrCntByCntIds(Long[] cntIds);

    List<VppMgrCnt> listByUserId(Long userId);

    List<VppMgrCnt> listByDeptId(Long deptId);

    VppMgrCnt selectVppMgrCntByUagId(Long uagid);
}
