package com.vpp.web.controller.mgr;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.enums.BusinessType;
import com.vpp.mgr.domain.VppMgrAggPara;
import com.vpp.mgr.service.IVppMgrAggParaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 聚合商-技术参数Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/mgr/agg/para")
@Api(tags = "聚合商-技术参数")
public class VppMgrAggParaController extends BaseController {
    @Autowired
    private IVppMgrAggParaService vppMgrAggParaService;

    /**
     * 查询聚合商-技术参数列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:para:list')")
    // @GetMapping("/list")
    // public TableDataInfo list(VppMgrAggPara vppMgrAggPara) {
    //     startPage();
    //     List<VppMgrAggPara> list = vppMgrAggParaService.selectVppMgrAggParaList(vppMgrAggPara);
    //     return getDataTable(list);
    // }

    /**
     * 导出聚合商-技术参数列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:para:export')")
    // @Log(title = "聚合商-技术参数", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, VppMgrAggPara vppMgrAggPara) {
    //     List<VppMgrAggPara> list = vppMgrAggParaService.selectVppMgrAggParaList(vppMgrAggPara);
    //     ExcelUtil<VppMgrAggPara> util = new ExcelUtil<VppMgrAggPara>(VppMgrAggPara.class);
    //     util.exportExcel(response, list, "聚合商-技术参数数据");
    // }

    /**
     * 获取聚合商-技术参数详细信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:para:query')")
    @GetMapping(value = "/{userId}")
    @ApiOperation("获取聚合商-技术参数详细信息")
    public AjaxResult getInfo(@PathVariable("userId") Long userId) {
        return success(vppMgrAggParaService.selectVppMgrAggParaByAggParaId(userId));
    }

    /**
     * 新增聚合商-技术参数
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:para:add')")
    @Log(title = "聚合商-技术参数", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增聚合商-技术参数")
    public AjaxResult add(@RequestBody VppMgrAggPara vppMgrAggPara) {
        return toAjax(vppMgrAggParaService.insertVppMgrAggPara(vppMgrAggPara));
    }

    /**
     * 修改聚合商-技术参数
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:para:edit')")
    @Log(title = "聚合商-技术参数", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改聚合商-技术参数")
    public AjaxResult edit(@RequestBody VppMgrAggPara vppMgrAggPara) {
        return toAjax(vppMgrAggParaService.updateVppMgrAggPara(vppMgrAggPara));
    }

    /**
     * 删除聚合商-技术参数
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:para:remove')")
    // @Log(title = "聚合商-技术参数", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{aggParaIds}")
    // public AjaxResult remove(@PathVariable Long[] aggParaIds) {
    //     return toAjax(vppMgrAggParaService.deleteVppMgrAggParaByAggParaIds(aggParaIds));
    // }
}