package com.vpp.td.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VppInvitationScheme implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long schemeId;              // 方案主键ID
    private String schemeName;          // 方案名称（如：最优响应方案）
    private Long invitationId;          // 关联邀约计划ID
    private String userTypeDistribution;// 用户类型分布（JSON格式，如：{"PV_USER":20,"WIND_USER":20}）
    private BigDecimal totalResponseQuantity; // 总响应量（kWh，如：60000.00）
    private Map<String, BigDecimal> userTypeDetail; // 用户类型明细（动态扩展字段）
    private String isOptimal;       //是否是优

    @Override
    public String toString() {
        return "VppInvitationScheme{" +
                "schemeId=" + schemeId +
                ", schemeName='" + schemeName + '\'' +
                ", invitationId=" + invitationId +
                ", userTypeDistribution='" + userTypeDistribution + '\'' +
                ", totalResponseQuantity=" + totalResponseQuantity +
                ", userTypeDetail=" + userTypeDetail +
                ", isOptimal=" + isOptimal +
                '}';
    }
}