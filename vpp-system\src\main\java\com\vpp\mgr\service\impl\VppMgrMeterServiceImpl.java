package com.vpp.mgr.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.mgr.domain.VppMgrMeter;
import com.vpp.mgr.mapper.VppMgrMeterMapper;
import com.vpp.mgr.service.IVppMgrMeterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class VppMgrMeterServiceImpl implements IVppMgrMeterService {
    @Autowired
    private VppMgrMeterMapper vppMgrMeterMapper;

    /**
     * 查询设备
     *
     * @param meterId 设备主键
     * @return 设备
     */
    @Override
    public VppMgrMeter selectVppMgrMeterByMeterId(Long meterId) {
        return vppMgrMeterMapper.selectVppMgrMeterByMeterId(meterId);
    }

    /**
     * 查询设备列表
     *
     * @param vppMgrMeter 设备
     * @return 设备
     */
    @Override
    public List<VppMgrMeter> selectVppMgrMeterList(VppMgrMeter vppMgrMeter) {
        return vppMgrMeterMapper.selectVppMgrMeterList(vppMgrMeter);
    }

    /**
     * 新增设备
     *
     * @param vppMgrMeter 设备
     * @return 结果
     */
    @Override
    public int insertVppMgrMeter(VppMgrMeter vppMgrMeter) {
        vppMgrMeter.setCreateTime(DateUtils.getNowDate());
        return vppMgrMeterMapper.insertVppMgrMeter(vppMgrMeter);
    }

    /**
     * 修改设备
     *
     * @param vppMgrMeter 设备
     * @return 结果
     */
    @Override
    public int updateVppMgrMeter(VppMgrMeter vppMgrMeter) {
        vppMgrMeter.setUpdateTime(DateUtils.getNowDate());
        return vppMgrMeterMapper.updateVppMgrMeter(vppMgrMeter);
    }

    /**
     * 批量删除设备
     *
     * @param meterIds 需要删除的设备主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrMeterByMeterIds(Long[] meterIds) {
        return vppMgrMeterMapper.deleteVppMgrMeterByMeterIds(meterIds);
    }

    /**
     * 删除设备信息
     *
     * @param meterId 设备主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrMeterByMeterId(Long meterId) {
        return vppMgrMeterMapper.deleteVppMgrMeterByMeterId(meterId);
    }
}