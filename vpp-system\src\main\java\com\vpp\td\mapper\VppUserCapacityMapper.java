package com.vpp.td.mapper;

import com.vpp.td.domain.VppUserCapacity;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface VppUserCapacityMapper {
    /**
     * 查询用户调节能力列表（带筛选条件）
     */
    List<VppUserCapacity> selectList(VppUserCapacity capacity);

    /**
     * 根据ID查询调节能力详情
     */
    VppUserCapacity selectById(Long capacityId);

    /**
     * 新增调节能力数据
     */
    int insert(VppUserCapacity capacity);

    /**
     * 修改调节能力数据
     */
    int update(VppUserCapacity capacity);

    /**
     * 逻辑删除调节能力数据
     */
    int deleteById(Long capacityId);
}
