package com.vpp.sm.service;

import com.vpp.sm.domain.VppSettlementRecord;

import java.util.List;

/**
 * 结算记录服务接口
 */
public interface IVppSettlementRecordService {

    /**
     * 根据邀约计划ID查询结算记录列表
     * @param invitationId 邀约计划ID
     * @return 结算记录列表
     */
    List<VppSettlementRecord> getRecordsByInvitationId(Long invitationId);

    /**
     * 插入新的结算记录
     * @param record 结算记录实体
     * @return 成功插入的记录数
     */
    int insertRecord(VppSettlementRecord record);

    /**
     * 获取所有结算记录（用于统计概览）
     * @return 结算记录列表
     */
    List<VppSettlementRecord> getAllRecords();
}