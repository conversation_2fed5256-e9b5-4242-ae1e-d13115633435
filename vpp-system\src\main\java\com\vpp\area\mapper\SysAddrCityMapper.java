package com.vpp.area.mapper;

import com.vpp.area.domain.SysAddrCity;

import java.util.List;

/**
 * 城市设置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface SysAddrCityMapper {
    /**
     * 查询城市设置
     *
     * @param id 城市设置主键
     * @return 城市设置
     */
    public SysAddrCity selectSysAddrCityById(String id);

    /**
     * 查询城市设置列表
     *
     * @param sysAddrCity 城市设置
     * @return 城市设置集合
     */
    public List<SysAddrCity> selectSysAddrCityList(SysAddrCity sysAddrCity);

    /**
     * 新增城市设置
     *
     * @param sysAddrCity 城市设置
     * @return 结果
     */
    public int insertSysAddrCity(SysAddrCity sysAddrCity);

    /**
     * 修改城市设置
     *
     * @param sysAddrCity 城市设置
     * @return 结果
     */
    public int updateSysAddrCity(SysAddrCity sysAddrCity);

    /**
     * 删除城市设置
     *
     * @param id 城市设置主键
     * @return 结果
     */
    public int deleteSysAddrCityById(String id);

    /**
     * 批量删除城市设置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAddrCityByIds(String[] ids);
}
