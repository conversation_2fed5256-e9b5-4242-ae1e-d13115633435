package com.vpp.execute.service;

import com.vpp.execute.domain.ExecutePlanDevices;

import java.util.List;

/**
 * 执行计划关联的设备Service接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface IExecutePlanDevicesService {
    /**
     * 查询执行计划关联的设备
     *
     * @param executePlanDevicesId 执行计划关联的设备主键
     * @return 执行计划关联的设备
     */
    public ExecutePlanDevices selectExecutePlanDevicesByExecutePlanDevicesId(Long executePlanDevicesId);

    /**
     * 查询执行计划关联的设备列表
     *
     * @param executePlanDevices 执行计划关联的设备
     * @return 执行计划关联的设备集合
     */
    public List<ExecutePlanDevices> selectExecutePlanDevicesList(ExecutePlanDevices executePlanDevices);

    /**
     * 新增执行计划关联的设备
     *
     * @param executePlanDevices 执行计划关联的设备
     * @return 结果
     */
    public int insertExecutePlanDevices(ExecutePlanDevices executePlanDevices);

    /**
     * 修改执行计划关联的设备
     *
     * @param executePlanDevices 执行计划关联的设备
     * @return 结果
     */
    public int updateExecutePlanDevices(ExecutePlanDevices executePlanDevices);

    /**
     * 批量删除执行计划关联的设备
     *
     * @param executePlanDevicesIds 需要删除的执行计划关联的设备主键集合
     * @return 结果
     */
    public int deleteExecutePlanDevicesByExecutePlanDevicesIds(Long[] executePlanDevicesIds);

    /**
     * 删除执行计划关联的设备信息
     *
     * @param executePlanDevicesId 执行计划关联的设备主键
     * @return 结果
     */
    public int deleteExecutePlanDevicesByExecutePlanDevicesId(Long executePlanDevicesId);
}
