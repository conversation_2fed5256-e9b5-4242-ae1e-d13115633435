package com.vpp.history.service;

import com.vpp.common.core.domain.AjaxResult;
import com.vpp.history.mapper.HistoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class HistoryEventService {
    @Autowired
    HistoryMapper mapper;
    public AjaxResult HistoryEventService(Long invitation_id,Long deptid){
        System.out.println(invitation_id+","+deptid);
        List<Map<String,Object>> moreHistory = mapper.findMoreHistory(invitation_id, deptid);
        System.out.println(moreHistory);
        AjaxResult result=new AjaxResult();
        result.put("code",200);
        result.put("msg","查询成功");
        result.put("data",moreHistory);
        return result;
    }

    public AjaxResult HistoryStatus(Long invitationId, Long deptid) {
        Map<String, Object> historyByStatus = mapper.findHistory(invitationId, deptid);
        System.out.println(historyByStatus);
        AjaxResult result=new AjaxResult();
        result.put("code",200);
        result.put("msg","查询成功");
        result.put("data",historyByStatus);
        return result;
    }
}
