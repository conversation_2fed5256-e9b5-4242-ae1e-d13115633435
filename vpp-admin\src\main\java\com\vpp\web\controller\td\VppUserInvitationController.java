package com.vpp.web.controller.td;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.td.domain.VppUserInvitation;
import com.vpp.td.service.IVppUserInvitationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/exchange/user/invitation")
@Api(tags = "聚合用户邀约管理")
public class VppUserInvitationController extends BaseController {

    @Autowired
    private IVppUserInvitationService userInvitationService;

    /**
     * 获取聚合用户邀约列表
     */
    @GetMapping("/list")
    @ApiOperation("获取聚合用户邀约列表")
    public TableDataInfo list(VppUserInvitation invitation) {
        startPage(); // 若依分页方法
        List<VppUserInvitation> list = userInvitationService.selectList(invitation);
        return getDataTable(list);
    }

    /**
     * 获取聚合用户邀约详情
     */
    @GetMapping("/{userInvitationId}")
    @ApiOperation("获取聚合用户邀约详情")
    public AjaxResult getInfo(
            @ApiParam(name = "userInvitationId", value = "用户邀约主键ID", required = true)
            @PathVariable Long userInvitationId
    ) {
        return AjaxResult.success(userInvitationService.selectById(userInvitationId));
    }

    /**
     * 新增聚合用户邀约
     */
    @Log(title = "聚合用户邀约", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增聚合用户邀约")
    public AjaxResult add(
            @Valid
            @RequestBody
            @ApiParam(name = "聚合用户邀约对象", value = "新增聚合用户邀约对象", required = true)
            VppUserInvitation invitation
    ) {
        return toAjax(userInvitationService.insert(invitation));
    }

    /**
     * 修改聚合用户邀约
     */
    @Log(title = "聚合用户邀约", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改聚合用户邀约")
    public AjaxResult edit(
            @Valid
            @RequestBody
            @ApiParam(name = "聚合用户邀约对象", value = "修改聚合用户邀约对象", required = true)
            VppUserInvitation invitation
    ) {
        return toAjax(userInvitationService.update(invitation));
    }

    /**
     * 删除聚合用户邀约
     */
    @Log(title = "聚合用户邀约", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userInvitationId}")
    @ApiOperation("删除聚合用户邀约")
    public AjaxResult remove(
            @ApiParam(name = "userInvitationId", value = "用户邀约主键ID", required = true)
            @PathVariable Long userInvitationId
    ) {
        return toAjax(userInvitationService.deleteById(userInvitationId));
    }
}
