package com.vpp.plan.mapper;

import com.vpp.plan.domain.PlanGen;

import java.util.List;

/**
 * 自动生成的计划Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface PlanGenMapper {
    /**
     * 查询自动生成的计划
     *
     * @param planGenId 自动生成的计划主键
     * @return 自动生成的计划
     */
    public PlanGen selectPlanGenByPlanGenId(Long planGenId);

    /**
     * 查询自动生成的计划列表
     *
     * @param planGen 自动生成的计划
     * @return 自动生成的计划集合
     */
    public List<PlanGen> selectPlanGenList(PlanGen planGen);

    /**
     * 新增自动生成的计划
     *
     * @param planGen 自动生成的计划
     * @return 结果
     */
    public int insertPlanGen(PlanGen planGen);

    /**
     * 修改自动生成的计划
     *
     * @param planGen 自动生成的计划
     * @return 结果
     */
    public int updatePlanGen(PlanGen planGen);

    /**
     * 删除自动生成的计划
     *
     * @param planGenId 自动生成的计划主键
     * @return 结果
     */
    public int deletePlanGenByPlanGenId(Long planGenId);

    /**
     * 批量删除自动生成的计划
     *
     * @param planGenIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanGenByPlanGenIds(Long[] planGenIds);
}