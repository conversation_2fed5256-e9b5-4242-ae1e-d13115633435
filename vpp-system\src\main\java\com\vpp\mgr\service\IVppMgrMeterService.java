package com.vpp.mgr.service;

import com.vpp.mgr.domain.VppMgrMeter;

import java.util.List;

/**
 * 设备Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IVppMgrMeterService {
    /**
     * 查询设备
     *
     * @param meterId 设备主键
     * @return 设备
     */
    public VppMgrMeter selectVppMgrMeterByMeterId(Long meterId);

    /**
     * 查询设备列表
     *
     * @param vppMgrMeter 设备
     * @return 设备集合
     */
    public List<VppMgrMeter> selectVppMgrMeterList(VppMgrMeter vppMgrMeter);

    /**
     * 新增设备
     *
     * @param vppMgrMeter 设备
     * @return 结果
     */
    public int insertVppMgrMeter(VppMgrMeter vppMgrMeter);

    /**
     * 修改设备
     *
     * @param vppMgrMeter 设备
     * @return 结果
     */
    public int updateVppMgrMeter(VppMgrMeter vppMgrMeter);

    /**
     * 批量删除设备
     *
     * @param meterIds 需要删除的设备主键集合
     * @return 结果
     */
    public int deleteVppMgrMeterByMeterIds(Long[] meterIds);

    /**
     * 删除设备信息
     *
     * @param meterId 设备主键
     * @return 结果
     */
    public int deleteVppMgrMeterByMeterId(Long meterId);
}
