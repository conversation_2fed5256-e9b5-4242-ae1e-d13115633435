package com.vpp.web.controller.aggregator;

import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.utils.GenerateOrderNo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * packageName com.vpp.web.controller.tool
 *
 * <AUTHOR>
 * @version JDK 8
 * @className GenerateOrderController
 * @date 2025/6/27
 * @description TODO
 */
@RestController
@RequestMapping("/generateOrderNum")
@Api(tags = "单号生成")
public class GenerateOrderController extends BaseController {

    @Autowired
    private GenerateOrderNo generateOrderNo;

    @GetMapping("/generate/{prefix}")
    @ApiOperation(value = "生成订单号")
    public AjaxResult generateCustomOrderNo(@PathVariable("prefix") String prefix) {
        return success(generateOrderNo.generateCustomOrderNo(prefix));
    }
}