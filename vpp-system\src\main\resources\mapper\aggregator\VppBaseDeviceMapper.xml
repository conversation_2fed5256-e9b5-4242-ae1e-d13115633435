<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.aggregator.mapper.VppBaseDeviceMapper">

    <resultMap type="VppBaseDevice" id="VppBaseDeviceResult">
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceRatedVoltage"    column="device_rated_voltage"    />
        <result property="deviceRatedCurrent"    column="device_rated_current"    />
        <result property="deviceRatedPower"    column="device_rated_power"    />
        <result property="deviceMaxVoltage"    column="device_max_voltage"    />
        <result property="deviceMaxCurrent"    column="device_max_current"    />
        <result property="deviceMaxPower"    column="device_max_power"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceStatus"    column="device_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVppBaseDeviceVo">
        select device_id, device_name, device_rated_voltage, device_rated_current, device_rated_power, device_max_voltage, device_max_current, device_max_power, device_type, device_status, create_by, create_time, update_by, update_time, remark from vpp_base_device
    </sql>

    <select id="selectVppBaseDeviceList" parameterType="VppBaseDevice" resultMap="VppBaseDeviceResult">
        <include refid="selectVppBaseDeviceVo"/>
        <where>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceRatedVoltage != null "> and device_rated_voltage = #{deviceRatedVoltage}</if>
            <if test="deviceRatedCurrent != null "> and device_rated_current = #{deviceRatedCurrent}</if>
            <if test="deviceRatedPower != null "> and device_rated_power = #{deviceRatedPower}</if>
            <if test="deviceMaxVoltage != null "> and device_max_voltage = #{deviceMaxVoltage}</if>
            <if test="deviceMaxCurrent != null "> and device_max_current = #{deviceMaxCurrent}</if>
            <if test="deviceMaxPower != null "> and device_max_power = #{deviceMaxPower}</if>
            <if test="deviceType != null "> and device_type = #{deviceType}</if>
            <if test="deviceStatus != null "> and device_status = #{deviceStatus}</if>
        </where>
    </select>

    <select id="selectVppBaseDeviceByDeviceId" parameterType="Long" resultMap="VppBaseDeviceResult">
        <include refid="selectVppBaseDeviceVo"/>
        where device_id = #{deviceId}
    </select>

    <insert id="insertVppBaseDevice" parameterType="VppBaseDevice" useGeneratedKeys="true" keyProperty="deviceId">
        insert into vpp_base_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">device_name,</if>
            <if test="deviceRatedVoltage != null">device_rated_voltage,</if>
            <if test="deviceRatedCurrent != null">device_rated_current,</if>
            <if test="deviceRatedPower != null">device_rated_power,</if>
            <if test="deviceMaxVoltage != null">device_max_voltage,</if>
            <if test="deviceMaxCurrent != null">device_max_current,</if>
            <if test="deviceMaxPower != null">device_max_power,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="deviceStatus != null">device_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">#{deviceName},</if>
            <if test="deviceRatedVoltage != null">#{deviceRatedVoltage},</if>
            <if test="deviceRatedCurrent != null">#{deviceRatedCurrent},</if>
            <if test="deviceRatedPower != null">#{deviceRatedPower},</if>
            <if test="deviceMaxVoltage != null">#{deviceMaxVoltage},</if>
            <if test="deviceMaxCurrent != null">#{deviceMaxCurrent},</if>
            <if test="deviceMaxPower != null">#{deviceMaxPower},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="deviceStatus != null">#{deviceStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVppBaseDevice" parameterType="VppBaseDevice">
        update vpp_base_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceRatedVoltage != null">device_rated_voltage = #{deviceRatedVoltage},</if>
            <if test="deviceRatedCurrent != null">device_rated_current = #{deviceRatedCurrent},</if>
            <if test="deviceRatedPower != null">device_rated_power = #{deviceRatedPower},</if>
            <if test="deviceMaxVoltage != null">device_max_voltage = #{deviceMaxVoltage},</if>
            <if test="deviceMaxCurrent != null">device_max_current = #{deviceMaxCurrent},</if>
            <if test="deviceMaxPower != null">device_max_power = #{deviceMaxPower},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where device_id = #{deviceId}
    </update>

    <delete id="deleteVppBaseDeviceByDeviceId" parameterType="Long">
        delete from vpp_base_device where device_id = #{deviceId}
    </delete>

    <delete id="deleteVppBaseDeviceByDeviceIds" parameterType="String">
        delete from vpp_base_device where device_id in
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>
</mapper>