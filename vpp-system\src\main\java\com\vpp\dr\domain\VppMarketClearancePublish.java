package com.vpp.dr.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 市场出清发布配置实体（对应界面"市场出清发布"编辑页）
 */
@Data
public class VppMarketClearancePublish implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /** 发送用户类型编码（关联vpp_user_type.type_code，如：ALL_AGGREGATE_USER） */
    private String sendUserTypeCode;

    /** 权限状态（0=关闭，1=开启） */
    private Integer permissionStatus;

    /** 删除标志（0=正常 1=删除） */
    private String isDeleted;

    /** 创建人ID（关联系统用户表sys_user.id） */
    private Long createBy;

    /** 记录创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 记录更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 关联的用户选择列表（非数据库字段，用于前端展示） */
    private List<VppUserSelection> userSelections;
}