package com.vpp.dr.service.impl;

import com.vpp.dr.domain.VppMarketClearanceRecord;
import com.vpp.dr.mapper.VppMarketClearanceRecordMapper;
import com.vpp.dr.service.IVppMarketClearanceRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class VppMarketClearanceRecordServiceImpl implements IVppMarketClearanceRecordService {

    @Autowired
    private VppMarketClearanceRecordMapper recordMapper;

    @Override
    public List<VppMarketClearanceRecord> getRecordList(Map<String, Object> params) {
        return recordMapper.selectRecordList(params);
    }
}