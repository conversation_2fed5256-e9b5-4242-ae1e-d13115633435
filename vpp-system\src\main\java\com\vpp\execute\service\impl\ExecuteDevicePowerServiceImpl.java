package com.vpp.execute.service.impl;

import com.vpp.execute.domain.ExecuteDevicePower;
import com.vpp.execute.mapper.ExecuteDevicePowerMapper;
import com.vpp.execute.service.IExecuteDevicePowerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 设备实时检测功率Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class ExecuteDevicePowerServiceImpl implements IExecuteDevicePowerService {
    @Autowired
    private ExecuteDevicePowerMapper executeDevicePowerMapper;

    /**
     * 查询设备实时检测功率
     *
     * @param timestamp 设备实时检测功率主键
     * @return 设备实时检测功率
     */
    @Override
    public ExecuteDevicePower selectExecuteDevicePowerByTimestamp(Date timestamp) {
        return executeDevicePowerMapper.selectExecuteDevicePowerByTimestamp(timestamp);
    }

    /**
     * 查询设备实时检测功率列表
     *
     * @param executeDevicePower 设备实时检测功率
     * @return 设备实时检测功率
     */
    @Override
    public List<ExecuteDevicePower> selectExecuteDevicePowerList(ExecuteDevicePower executeDevicePower) {
        return executeDevicePowerMapper.selectExecuteDevicePowerList(executeDevicePower);
    }

    /**
     * 新增设备实时检测功率
     *
     * @param executeDevicePower 设备实时检测功率
     * @return 结果
     */
    @Override
    public int insertExecuteDevicePower(ExecuteDevicePower executeDevicePower) {
        return executeDevicePowerMapper.insertExecuteDevicePower(executeDevicePower);
    }

    /**
     * 修改设备实时检测功率
     *
     * @param executeDevicePower 设备实时检测功率
     * @return 结果
     */
    @Override
    public int updateExecuteDevicePower(ExecuteDevicePower executeDevicePower) {
        return executeDevicePowerMapper.updateExecuteDevicePower(executeDevicePower);
    }

    /**
     * 批量删除设备实时检测功率
     *
     * @param timestamps 需要删除的设备实时检测功率主键
     * @return 结果
     */
    @Override
    public int deleteExecuteDevicePowerByTimestamps(Date[] timestamps) {
        return executeDevicePowerMapper.deleteExecuteDevicePowerByTimestamps(timestamps);
    }

    /**
     * 删除设备实时检测功率信息
     *
     * @param timestamp 设备实时检测功率主键
     * @return 结果
     */
    @Override
    public int deleteExecuteDevicePowerByTimestamp(Date timestamp) {
        return executeDevicePowerMapper.deleteExecuteDevicePowerByTimestamp(timestamp);
    }
}