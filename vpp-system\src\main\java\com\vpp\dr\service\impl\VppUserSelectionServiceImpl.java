package com.vpp.dr.service.impl;

import com.vpp.dr.domain.VppUserSelection;
import com.vpp.dr.mapper.VppUserSelectionMapper;
import com.vpp.dr.service.IVppUserSelectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VppUserSelectionServiceImpl implements IVppUserSelectionService {

    @Autowired
    private VppUserSelectionMapper userSelectionMapper;

    @Override
    public List<VppUserSelection> selectByPublishId(Long publishId) {
        return userSelectionMapper.selectByPublishId(publishId);
    }

    @Override
    public int insert(VppUserSelection selection) {
        return userSelectionMapper.insert(selection);
    }

    @Override
    public int deleteByPublishId(Long publishId) {
        return userSelectionMapper.deleteByPublishId(publishId);
    }
}