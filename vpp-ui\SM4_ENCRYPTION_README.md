# SM4密码加密实现说明

## 概述
本项目已将登录密码加密方式从RSA改为SM4加密，符合国密标准要求。

## 实现内容

### 1. 依赖安装
- 安装了 `sm-crypto` 库用于SM4加密解密

### 2. 修改的文件

#### 2.1 加密工具文件 (`src/utils/jsencrypt.js`)
- 替换了原有的RSA加密实现
- 使用SM4对称加密算法
- 提供 `encrypt()` 和 `decrypt()` 函数

#### 2.2 登录相关API (`src/api/login.js`)
- `login()` 函数：登录时对密码进行SM4加密
- `register()` 函数：注册时对密码进行SM4加密

#### 2.3 用户管理API (`src/api/system/user.js`)
- `addUser()` 函数：新增用户时对密码进行SM4加密
- `resetUserPwd()` 函数：重置密码时对新密码进行SM4加密
- `updateUserPwd()` 函数：修改密码时对新旧密码都进行SM4加密

### 3. 加密场景覆盖
- ✅ 用户登录
- ✅ 用户注册
- ✅ 新增用户
- ✅ 管理员重置用户密码
- ✅ 用户修改个人密码
- ✅ 记住密码功能（Cookie存储）

### 4. SM4密钥配置
当前使用密钥：`0103338b0b62198a7b957a011020f03e`（与后端一致）
加密模式：CBC模式
初始化向量：`0103338b0b62198a7b957a011020f03e`（与后端一致）

**⚠️ 生产环境建议：**
- 将密钥配置在环境变量或配置文件中
- 定期更换密钥
- 使用更安全的密钥管理方案
- 确保前后端密钥和IV完全一致

### 5. 测试验证
提供了测试文件 `src/utils/sm4-test.js`，可在浏览器控制台运行：
```javascript
testSM4()
```

## 使用方法

### 前端加密
```javascript
import { encrypt, decrypt } from '@/utils/jsencrypt'

// 加密密码
const encryptedPassword = encrypt('原始密码')

// 解密密码（主要用于记住密码功能）
const originalPassword = decrypt('加密后的密码')
```

### 后端配置
后端需要使用相同的SM4密钥和算法来解密前端传来的密码。

## 注意事项

1. **密钥安全**：确保SM4密钥的安全性，不要在代码中硬编码
2. **前后端一致**：前后端必须使用相同的SM4密钥和加密模式
3. **错误处理**：加密解密过程中的异常已进行捕获和日志记录
4. **兼容性**：SM4是国密算法，确保后端系统支持SM4解密

## 常见问题解决

### 1. "pad block corrupted" 错误
如果遇到此错误，请检查以下几点：

1. **前后端密钥一致性**
   - 确保前端 `src/config/sm4.js` 中的密钥与后端一致
   - 密钥必须是32位十六进制字符串（128位）

2. **加密模式一致性**
   - 前端使用CBC模式
   - 确保后端也使用相同的CBC模式
   - 确保前后端使用相同的初始化向量(IV)

3. **填充方式一致性**
   - 前端使用PKCS#7填充
   - 确保后端也使用PKCS#7填充

4. **编码格式一致性**
   - 前端输出十六进制字符串
   - 确保后端接收十六进制格式

### 2. 调试方法
1. 打开浏览器开发者工具
2. 查看控制台中的SM4加密日志
3. 运行测试函数：`testSM4()`
4. 检查加密输出格式是否正确

### 3. 环境变量配置
在 `.env` 文件中设置自定义密钥：
```
VUE_APP_SM4_KEY=your_32_character_hex_key_here
```

## 后续优化建议

1. ✅ 将SM4密钥配置化，支持从环境变量读取
2. 考虑添加密钥版本管理，支持密钥轮换
3. 可考虑添加加密时间戳，防止重放攻击
4. 添加更完善的单元测试
5. 添加CBC模式支持（如果后端需要）
