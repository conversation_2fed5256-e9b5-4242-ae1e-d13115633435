package com.vpp.dr.service.impl;

import com.vpp.dr.domain.VppMarketClearancePublish;
import com.vpp.dr.domain.VppUserSelection;
import com.vpp.dr.mapper.VppMarketClearancePublishMapper;
import com.vpp.dr.mapper.VppUserSelectionMapper;
import com.vpp.dr.service.IVppMarketClearancePublishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class VppMarketClearancePublishServiceImpl implements IVppMarketClearancePublishService {

    @Autowired
    private VppMarketClearancePublishMapper publishMapper;

    @Autowired
    private VppUserSelectionMapper userSelectionMapper;

    @Override
    public List<VppMarketClearancePublish> getPublishList(Map<String, Object> params) {
        return publishMapper.selectPublishList(params);
    }

    @Override
    public VppMarketClearancePublish getPublishDetail(Long id) {
        return publishMapper.selectPublishWithUserSelection(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPublish(VppMarketClearancePublish publish, List<Long> selectedUserIds) {
        // 插入主表
        int mainResult = publishMapper.insertPublish(publish);
        if (mainResult == 0) return false;

        // 插入关联用户选择（示例：假设selectedUserIds是用户类型ID列表）
        for (Long userId : selectedUserIds) {
            VppUserSelection userSelection = new VppUserSelection();
            userSelection.setPublishId(publish.getId());
            userSelection.setUserId(userId);
            userSelection.setCreateTime(new Date());
            userSelection.setUpdateTime(new Date());
            userSelectionMapper.insert(userSelection); // 需确保VppUserSelectionMapper有insert方法
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePublish(VppMarketClearancePublish publish, List<Long> selectedUserIds) {
        // 更新主表
        int mainResult = publishMapper.updatePublish(publish);
        if (mainResult == 0) return false;

        // 删除旧关联
        userSelectionMapper.deleteByPublishId(publish.getId());

        // 插入新关联
        for (Long userId : selectedUserIds) {
            VppUserSelection userSelection = new VppUserSelection();
            userSelection.setPublishId(publish.getId());
            userSelection.setUserId(userId);
            userSelection.setCreateTime(new Date());
            userSelection.setUpdateTime(new Date());
            userSelectionMapper.insert(userSelection);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePublish(Long id) {
        // 逻辑删除主表（假设添加了is_deleted字段）
        int mainResult = publishMapper.deletePublish(id);
        if (mainResult == 0) return false;

        // 删除关联用户选择
        userSelectionMapper.deleteByPublishId(id);
        return true;
    }
}