package com.vpp.aggregator.service.impl;

import com.vpp.aggregator.domain.VppVendor;
import com.vpp.aggregator.mapper.VppVendorMapper;
import com.vpp.aggregator.service.IVppVendorService;
import com.vpp.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 虚拟电厂供应商，是vpp_base虚拟电厂的子关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class VppVendorServiceImpl implements IVppVendorService {
    @Autowired
    private VppVendorMapper vppVendorMapper;

    /**
     * 查询虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     *
     * @param vendorId 虚拟电厂供应商，是vpp_base虚拟电厂的子关系主键
     * @return 虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     */
    @Override
    public VppVendor selectVppVendorByVendorId(Long vendorId) {
        return vppVendorMapper.selectVppVendorByVendorId(vendorId);
    }

    /**
     * 查询虚拟电厂供应商，是vpp_base虚拟电厂的子关系列表
     *
     * @param vppVendor 虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     * @return 虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     */
    @Override
    public List<VppVendor> selectVppVendorList(VppVendor vppVendor) {
        return vppVendorMapper.selectVppVendorList(vppVendor);
    }

    /**
     * 新增虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     *
     * @param vppVendor 虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     * @return 结果
     */
    @Override
    public int insertVppVendor(VppVendor vppVendor) {
        vppVendor.setCreateTime(DateUtils.getNowDate());
        return vppVendorMapper.insertVppVendor(vppVendor);
    }

    /**
     * 修改虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     *
     * @param vppVendor 虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     * @return 结果
     */
    @Override
    public int updateVppVendor(VppVendor vppVendor) {
        vppVendor.setUpdateTime(DateUtils.getNowDate());
        return vppVendorMapper.updateVppVendor(vppVendor);
    }

    /**
     * 批量删除虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     *
     * @param vendorIds 需要删除的虚拟电厂供应商，是vpp_base虚拟电厂的子关系主键
     * @return 结果
     */
    @Override
    public int deleteVppVendorByVendorIds(Long[] vendorIds) {
        return vppVendorMapper.deleteVppVendorByVendorIds(vendorIds);
    }

    /**
     * 删除虚拟电厂供应商，是vpp_base虚拟电厂的子关系信息
     *
     * @param vendorId 虚拟电厂供应商，是vpp_base虚拟电厂的子关系主键
     * @return 结果
     */
    @Override
    public int deleteVppVendorByVendorId(Long vendorId) {
        return vppVendorMapper.deleteVppVendorByVendorId(vendorId);
    }
}