package com.vpp.web.controller.td;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.td.domain.VppUserCapacity;
import com.vpp.td.service.IVppUserCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/exchange/user/capacity")
@Api(tags = "用户调节能力管理")
public class VppUserCapacityController extends BaseController {

    @Autowired
    private IVppUserCapacityService userCapacityService;

    /**
     * 获取用户调节能力列表
     */
    @GetMapping("/list")
    @ApiOperation("获取用户调节能力列表")
    public TableDataInfo list(VppUserCapacity capacity) {
        startPage(); // 若依分页方法
        List<VppUserCapacity> list = userCapacityService.selectList(capacity);
        return getDataTable(list);
    }

    /**
     * 获取用户调节能力详情
     */
    @GetMapping("/{capacityId}")
    @ApiOperation("获取用户调节能力详情")
    public AjaxResult getInfo(
            @ApiParam(name = "capacityId", value = "调节能力主键ID", required = true)
            @PathVariable Long capacityId
    ) {
        return AjaxResult.success(userCapacityService.selectById(capacityId));
    }

    /**
     * 新增用户调节能力
     */
    @Log(title = "用户调节能力", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增用户调节能力")
    public AjaxResult add(
            @Valid
            @RequestBody
            @ApiParam(name = "用户调节能力对象", value = "新增用户调节能力对象", required = true)
            VppUserCapacity capacity
    ) {
        return toAjax(userCapacityService.insert(capacity));
    }

    /**
     * 修改用户调节能力
     */
    @Log(title = "用户调节能力", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改用户调节能力")
    public AjaxResult edit(
            @Valid
            @RequestBody
            @ApiParam(name = "用户调节能力对象", value = "修改用户调节能力对象", required = true)
            VppUserCapacity capacity
    ) {
        return toAjax(userCapacityService.update(capacity));
    }

    /**
     * 删除用户调节能力
     */
    @Log(title = "用户调节能力", businessType = BusinessType.DELETE)
    @DeleteMapping("/{capacityId}")
    @ApiOperation("删除用户调节能力")
    public AjaxResult remove(
            @ApiParam(name = "capacityId", value = "调节能力主键ID", required = true)
            @PathVariable Long capacityId
    ) {
        return toAjax(userCapacityService.deleteById(capacityId));
    }
}
