package com.vpp.execute.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 设备实时检测电流对象 execute_device_current
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public class ExecuteDeviceCurrent extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    private Long deviceId;

    /**
     * $column.columnComment
     */
    private Date timestamp;

    /**
     * 设备实时功率,数据不连续，计算功率数据，设备会因天气原因断电或停机
     */
    @Excel(name = "设备实时功率,数据不连续，计算功率数据，设备会因天气原因断电或停机")
    private Long deviceCurrent;

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceCurrent(Long deviceCurrent) {
        this.deviceCurrent = deviceCurrent;
    }

    public Long getDeviceCurrent() {
        return deviceCurrent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("timestamp", getTimestamp())
                .append("deviceId", getDeviceId())
                .append("deviceCurrent", getDeviceCurrent())
                .toString();
    }
}