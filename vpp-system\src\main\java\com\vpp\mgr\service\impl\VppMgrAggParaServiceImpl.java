package com.vpp.mgr.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.mgr.domain.VppMgrAggPara;
import com.vpp.mgr.mapper.VppMgrAggParaMapper;
import com.vpp.mgr.service.IVppMgrAggParaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 聚合商-技术参数Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class VppMgrAggParaServiceImpl implements IVppMgrAggParaService {
    @Autowired
    private VppMgrAggParaMapper vppMgrAggParaMapper;

    /**
     * 查询聚合商-技术参数
     *
     * @param userId 聚合商-技术参数主键
     * @return 聚合商-技术参数
     */
    @Override
    public VppMgrAggPara selectVppMgrAggParaByAggParaId(Long userId) {
        return vppMgrAggParaMapper.selectVppMgrAggParaByAggParaId(userId);
    }

    /**
     * 查询聚合商-技术参数列表
     *
     * @param vppMgrAggPara 聚合商-技术参数
     * @return 聚合商-技术参数
     */
    @Override
    public List<VppMgrAggPara> selectVppMgrAggParaList(VppMgrAggPara vppMgrAggPara) {
        return vppMgrAggParaMapper.selectVppMgrAggParaList(vppMgrAggPara);
    }

    /**
     * 新增聚合商-技术参数
     *
     * @param vppMgrAggPara 聚合商-技术参数
     * @return 结果
     */
    @Override
    public int insertVppMgrAggPara(VppMgrAggPara vppMgrAggPara) {
        vppMgrAggPara.setCreateTime(DateUtils.getNowDate());
        return vppMgrAggParaMapper.insertVppMgrAggPara(vppMgrAggPara);
    }

    /**
     * 修改聚合商-技术参数
     *
     * @param vppMgrAggPara 聚合商-技术参数
     * @return 结果
     */
    @Override
    public int updateVppMgrAggPara(VppMgrAggPara vppMgrAggPara) {
        vppMgrAggPara.setUpdateTime(DateUtils.getNowDate());
        return vppMgrAggParaMapper.updateVppMgrAggPara(vppMgrAggPara);
    }

    /**
     * 批量删除聚合商-技术参数
     *
     * @param aggParaIds 需要删除的聚合商-技术参数主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrAggParaByAggParaIds(Long[] aggParaIds) {
        return vppMgrAggParaMapper.deleteVppMgrAggParaByAggParaIds(aggParaIds);
    }

    /**
     * 删除聚合商-技术参数信息
     *
     * @param aggParaId 聚合商-技术参数主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrAggParaByAggParaId(Long aggParaId) {
        return vppMgrAggParaMapper.deleteVppMgrAggParaByAggParaId(aggParaId);
    }
}