<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.aggregator.mapper.VppBaseCorporationMapper">

    <resultMap type="VppBaseCorporation" id="VppBaseCorporationResult">
        <result property="corporationId"    column="corporation_id"    />
        <result property="corporationName"    column="corporation_name"    />
        <result property="corporationCode"    column="corporation_code"    />
        <result property="corporationLegal"    column="corporation_legal"    />
        <result property="corporationPhone"    column="corporation_phone"    />
        <result property="corporationLandline"    column="corporation_landline"    />
        <result property="corporationProvince"    column="corporation_province"    />
        <result property="corporationMunicipality"    column="corporation_municipality"    />
        <result property="corporationRegional"    column="corporation_regional"    />
        <result property="corporationAddre"    column="corporation_addre"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVppBaseCorporationVo">
        select corporation_id, corporation_name, corporation_code, corporation_legal, corporation_phone, corporation_landline, corporation_province, corporation_municipality, corporation_regional, corporation_addre, dept_id, create_by, create_time, update_by, update_time, remark from vpp_base_corporation
    </sql>

    <select id="selectVppBaseCorporationList" parameterType="VppBaseCorporation" resultMap="VppBaseCorporationResult">
        <include refid="selectVppBaseCorporationVo"/>
        <where>
            <if test="corporationName != null  and corporationName != ''"> and corporation_name like concat('%', #{corporationName}, '%')</if>
            <if test="corporationCode != null  and corporationCode != ''"> and corporation_code = #{corporationCode}</if>
            <if test="corporationLegal != null  and corporationLegal != ''"> and corporation_legal = #{corporationLegal}</if>
            <if test="corporationPhone != null  and corporationPhone != ''"> and corporation_phone = #{corporationPhone}</if>
            <if test="corporationLandline != null  and corporationLandline != ''"> and corporation_landline = #{corporationLandline}</if>
            <if test="corporationProvince != null "> and corporation_province = #{corporationProvince}</if>
            <if test="corporationMunicipality != null "> and corporation_municipality = #{corporationMunicipality}</if>
            <if test="corporationRegional != null "> and corporation_regional = #{corporationRegional}</if>
            <if test="corporationAddre != null  and corporationAddre != ''"> and corporation_addre = #{corporationAddre}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>

    <select id="selectVppBaseCorporationByCorporationId" parameterType="Long" resultMap="VppBaseCorporationResult">
        <include refid="selectVppBaseCorporationVo"/>
        where corporation_id = #{corporationId}
    </select>

    <insert id="insertVppBaseCorporation" parameterType="VppBaseCorporation" useGeneratedKeys="true" keyProperty="corporationId">
        insert into vpp_base_corporation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="corporationName != null">corporation_name,</if>
            <if test="corporationCode != null">corporation_code,</if>
            <if test="corporationLegal != null">corporation_legal,</if>
            <if test="corporationPhone != null">corporation_phone,</if>
            <if test="corporationLandline != null">corporation_landline,</if>
            <if test="corporationProvince != null">corporation_province,</if>
            <if test="corporationMunicipality != null">corporation_municipality,</if>
            <if test="corporationRegional != null">corporation_regional,</if>
            <if test="corporationAddre != null">corporation_addre,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="corporationName != null">#{corporationName},</if>
            <if test="corporationCode != null">#{corporationCode},</if>
            <if test="corporationLegal != null">#{corporationLegal},</if>
            <if test="corporationPhone != null">#{corporationPhone},</if>
            <if test="corporationLandline != null">#{corporationLandline},</if>
            <if test="corporationProvince != null">#{corporationProvince},</if>
            <if test="corporationMunicipality != null">#{corporationMunicipality},</if>
            <if test="corporationRegional != null">#{corporationRegional},</if>
            <if test="corporationAddre != null">#{corporationAddre},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVppBaseCorporation" parameterType="VppBaseCorporation">
        update vpp_base_corporation
        <trim prefix="SET" suffixOverrides=",">
            <if test="corporationName != null">corporation_name = #{corporationName},</if>
            <if test="corporationCode != null">corporation_code = #{corporationCode},</if>
            <if test="corporationLegal != null">corporation_legal = #{corporationLegal},</if>
            <if test="corporationPhone != null">corporation_phone = #{corporationPhone},</if>
            <if test="corporationLandline != null">corporation_landline = #{corporationLandline},</if>
            <if test="corporationProvince != null">corporation_province = #{corporationProvince},</if>
            <if test="corporationMunicipality != null">corporation_municipality = #{corporationMunicipality},</if>
            <if test="corporationRegional != null">corporation_regional = #{corporationRegional},</if>
            <if test="corporationAddre != null">corporation_addre = #{corporationAddre},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where corporation_id = #{corporationId}
    </update>

    <delete id="deleteVppBaseCorporationByCorporationId" parameterType="Long">
        delete from vpp_base_corporation where corporation_id = #{corporationId}
    </delete>

    <delete id="deleteVppBaseCorporationByCorporationIds" parameterType="String">
        delete from vpp_base_corporation where corporation_id in
        <foreach item="corporationId" collection="array" open="(" separator="," close=")">
            #{corporationId}
        </foreach>
    </delete>
</mapper>