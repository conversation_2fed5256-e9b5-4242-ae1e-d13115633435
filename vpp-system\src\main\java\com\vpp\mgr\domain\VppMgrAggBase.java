package com.vpp.mgr.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 聚合商-基础信息对象 vpp_mgr_agg_base
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VppMgrAggBase extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long aggBaseId;

    /**
     * 聚合商名称
     */
    @Excel(name = "聚合商名称")
    private String aggName;

    /**
     * 聚合商编码，唯一标识
     */
    @Excel(name = "聚合商编码，唯一标识")
    private String aggCode;

    /**
     * 运营方
     */
    @Excel(name = "运营方")
    private String operator;

    /**
     * 聚合商用户量
     */
    @Excel(name = "聚合商用户量")
    private Long userCount;

    /**
     * 社会信用代码
     */
    @Excel(name = "社会信用代码")
    private String creditCode;

    /**
     * 所属行政地区
     */
    @Excel(name = "所属行政地区")
    private String region;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contact;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String phone;

    /**
     * 图片地址
     */
    @Excel(name = "图片地址")
    private String imgUrl;

    /**
     * 部门(机构)ID
     */
    @Excel(name = "部门(机构)ID")
    private Long deptId;

    /**
     * 用户ID(sys_user)
     */
    @Excel(name = "用户ID(sys_user)")
    private Long userId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}