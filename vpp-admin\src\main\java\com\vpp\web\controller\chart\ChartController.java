package com.vpp.web.controller.chart;

import com.vpp.chart.service.ChartService;
import com.vpp.chart.service.LargeScreenService;
import com.vpp.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/chart")
@ApiOperation(value = "图表数据集",tags = {"图表数据集"})
public class ChartController {
    /**
     * 储能设备数
     * 不可调控设备数
     * 可调控设备数
     * 在线设备数
     * 用电资源类型占比 --------- 未实现
     * 资源分布统计
     * 资源可用率统计
     * 资源分区信息
     */
    @Autowired
    ChartService chartService;

    @GetMapping("/devices/resourceprovince/{deptid}")
    @ApiOperation(value = "资源分区信息",tags = {"图表数据集"})
    public AjaxResult countResourceProvince(@PathVariable("deptid")String deptid){
        return chartService.devicesCount(ChartService.ChartType.RESOURCE_PROVINCE,deptid);
    }
    @ApiOperation(value = "资源可用率统计",tags = {"图表数据集"})
    @GetMapping("/devices/available/{deptid}")
    public AjaxResult countAvailable(@PathVariable String deptid){
        return chartService.devicesCount(ChartService.ChartType.AVAILABLE,deptid);
    }

    @ApiOperation(value = "在线设备数",tags = {"图表数据集"})
    @GetMapping("/devices/online/{deptid}")
    public AjaxResult countOnlineDevices(@PathVariable String deptid){
        return chartService.devicesCount(ChartService.ChartType.ONLINE,deptid);
    }

    @ApiOperation(value = "资源分布统计",tags = {"图表数据集"})
    @GetMapping("/devices/resourcesstatics/{deptid}")
    public AjaxResult countResourceDevices(@PathVariable String deptid){
        return chartService.devicesCount(ChartService.ChartType.RESOURCE_STATICS,deptid);
    }



    /**
     * 获取储能设备数
     * @param deptid
     * @return
     */
    @GetMapping("/devices/chuneng/{deptid}")
    @ApiOperation(value = "储能设备数",tags = {"图表数据集"})
    public AjaxResult devicesCountChuneng(@PathVariable("deptid") String deptid){
        return chartService.devicesCount(ChartService.ChartType.CHUNENG,deptid);
    }
    @ApiOperation(value = "不可调控设备数",tags = {"图表数据集"})
    @GetMapping("/devices/notcontrol/{deptid}")
    public AjaxResult devicesCountNotControl(@PathVariable("deptid")String deptid){
        return chartService.devicesCount(ChartService.ChartType.NOT_CONTROL,deptid);
    }
    @ApiOperation(value="可调控设备数",tags = {"图表数据集"})
    @GetMapping("/devices/control/{deptid}")
    public AjaxResult devicesCountControl(@PathVariable("deptid")String deptid){
        return chartService.devicesCount(ChartService.ChartType.CONTROL,deptid);
    }
}
