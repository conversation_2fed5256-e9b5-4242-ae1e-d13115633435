package com.vpp.mgr.service;

import com.vpp.mgr.domain.VppMgrUagAccountNum;

import java.util.List;

/**
 * 聚合商用户-户号 关联Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IVppMgrUagAccountNumService {
    /**
     * 查询聚合商用户-户号 关联
     *
     * @param userId 聚合商用户-户号 关联主键
     * @return 聚合商用户-户号 关联
     */
    public VppMgrUagAccountNum selectVppMgrUagAccountNumByUserId(Long userId);

    /**
     * 查询聚合商用户-户号 关联列表
     *
     * @param vppMgrUagAccountNum 聚合商用户-户号 关联
     * @return 聚合商用户-户号 关联集合
     */
    public List<VppMgrUagAccountNum> selectVppMgrUagAccountNumList(VppMgrUagAccountNum vppMgrUagAccountNum);

    /**
     * 新增聚合商用户-户号 关联
     *
     * @param vppMgrUagAccountNum 聚合商用户-户号 关联
     * @return 结果
     */
    public int insertVppMgrUagAccountNum(VppMgrUagAccountNum vppMgrUagAccountNum);

    /**
     * 修改聚合商用户-户号 关联
     *
     * @param vppMgrUagAccountNum 聚合商用户-户号 关联
     * @return 结果
     */
    public int updateVppMgrUagAccountNum(VppMgrUagAccountNum vppMgrUagAccountNum);

    /**
     * 批量删除聚合商用户-户号 关联
     *
     * @param userIds 需要删除的聚合商用户-户号 关联主键集合
     * @return 结果
     */
    public int deleteVppMgrUagAccountNumByUserIds(Long[] userIds);

    /**
     * 删除聚合商用户-户号 关联信息
     *
     * @param userId 聚合商用户-户号 关联主键
     * @return 结果
     */
    public int deleteVppMgrUagAccountNumByUserId(Long userId);

    List<VppMgrUagAccountNum> selectByDeptId(Long deptId);
}
