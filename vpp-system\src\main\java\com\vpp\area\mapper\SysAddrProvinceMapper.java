package com.vpp.area.mapper;

import com.vpp.area.domain.SysAddrProvince;

import java.util.List;

/**
 * 省份Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface SysAddrProvinceMapper {
    /**
     * 查询省份
     *
     * @param id 省份主键
     * @return 省份
     */
    public SysAddrProvince selectSysAddrProvinceById(String id);

    /**
     * 查询省份列表
     *
     * @param sysAddrProvince 省份
     * @return 省份集合
     */
    public List<SysAddrProvince> selectSysAddrProvinceList(SysAddrProvince sysAddrProvince);

    /**
     * 新增省份
     *
     * @param sysAddrProvince 省份
     * @return 结果
     */
    public int insertSysAddrProvince(SysAddrProvince sysAddrProvince);

    /**
     * 修改省份
     *
     * @param sysAddrProvince 省份
     * @return 结果
     */
    public int updateSysAddrProvince(SysAddrProvince sysAddrProvince);

    /**
     * 删除省份
     *
     * @param id 省份主键
     * @return 结果
     */
    public int deleteSysAddrProvinceById(String id);

    /**
     * 批量删除省份
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAddrProvinceByIds(String[] ids);
}