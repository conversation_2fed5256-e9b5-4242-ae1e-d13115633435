package com.vpp.web.controller.mgr;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.mgr.domain.VppMgrMeter;
import com.vpp.mgr.service.IVppMgrMeterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/mgr/meter")
@Api(tags = "设备管理")
public class VppMgrMeterController extends BaseController {
    @Autowired
    private IVppMgrMeterService vppMgrMeterService;

    /**
     * 查询设备列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:meter:list')")
    @GetMapping("/list")
    @ApiOperation("查询设备列表")
    public TableDataInfo list(VppMgrMeter vppMgrMeter) {
        startPage();
        List<VppMgrMeter> list = vppMgrMeterService.selectVppMgrMeterList(vppMgrMeter);
        return getDataTable(list);
    }

    /**
     * 导出设备列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:meter:export')")
    // @Log(title = "设备", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, VppMgrMeter vppMgrMeter) {
    //     List<VppMgrMeter> list = vppMgrMeterService.selectVppMgrMeterList(vppMgrMeter);
    //     ExcelUtil<VppMgrMeter> util = new ExcelUtil<VppMgrMeter>(VppMgrMeter.class);
    //     util.exportExcel(response, list, "设备数据");
    // }

    /**
     * 获取设备详细信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:meter:query')")
    @GetMapping(value = "/{meterId}")
    @ApiOperation("获取设备详细信息")
    public AjaxResult getInfo(@PathVariable("meterId") Long meterId) {
        return success(vppMgrMeterService.selectVppMgrMeterByMeterId(meterId));
    }

    /**
     * 新增设备
     */
    // @PreAuthorize("@ss.hasPermi('mgr:meter:add')")
    @Log(title = "设备", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增设备")
    public AjaxResult add(@RequestBody VppMgrMeter vppMgrMeter) {
        return toAjax(vppMgrMeterService.insertVppMgrMeter(vppMgrMeter));
    }

    /**
     * 修改设备
     */
    // @PreAuthorize("@ss.hasPermi('mgr:meter:edit')")
    @Log(title = "设备", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改设备")
    public AjaxResult edit(@RequestBody VppMgrMeter vppMgrMeter) {
        return toAjax(vppMgrMeterService.updateVppMgrMeter(vppMgrMeter));
    }

    /**
     * 删除设备
     */
    // @PreAuthorize("@ss.hasPermi('mgr:meter:remove')")
    @Log(title = "设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{meterIds}")
    @ApiOperation("删除设备")
    public AjaxResult remove(@PathVariable Long[] meterIds) {
        return toAjax(vppMgrMeterService.deleteVppMgrMeterByMeterIds(meterIds));
    }
}