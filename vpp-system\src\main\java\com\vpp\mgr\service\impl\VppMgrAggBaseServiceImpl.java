package com.vpp.mgr.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.mgr.domain.VppMgrAggBase;
import com.vpp.mgr.domain.VppMgrCnt;
import com.vpp.mgr.mapper.VppMgrAggBaseMapper;
import com.vpp.mgr.mapper.VppMgrCntMapper;
import com.vpp.mgr.service.IVppMgrAggBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 聚合商-基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class VppMgrAggBaseServiceImpl implements IVppMgrAggBaseService {
    @Autowired
    private VppMgrAggBaseMapper vppMgrAggBaseMapper;
    @Autowired
    private VppMgrCntMapper cntMapper;
    /**
     * 查询聚合商-基础信息
     *
     * @param userId 聚合商-基础信息主键
     * @return 聚合商-基础信息
     */
    @Override
    public VppMgrAggBase selectVppMgrAggBaseByAggBaseId(Long userId) {
        return vppMgrAggBaseMapper.selectVppMgrAggBaseByAggBaseId(userId);
    }

    /**
     * 查询聚合商-基础信息列表
     *
     * @param vppMgrAggBase 聚合商-基础信息
     * @return 聚合商-基础信息
     */
    @Override
    public List<VppMgrAggBase> selectVppMgrAggBaseList(VppMgrAggBase vppMgrAggBase) {
        return vppMgrAggBaseMapper.selectVppMgrAggBaseList(vppMgrAggBase);
    }

    /**
     * 新增聚合商-基础信息
     *
     * @param vppMgrAggBase 聚合商-基础信息
     * @return 结果
     */
    @Override
    public int insertVppMgrAggBase(VppMgrAggBase vppMgrAggBase) {
        vppMgrAggBase.setCreateTime(DateUtils.getNowDate());
        return vppMgrAggBaseMapper.insertVppMgrAggBase(vppMgrAggBase);
    }

    /**
     * 修改聚合商-基础信息
     *
     * @param vppMgrAggBase 聚合商-基础信息
     * @return 结果
     */
    @Override
    public int updateVppMgrAggBase(VppMgrAggBase vppMgrAggBase) {
        vppMgrAggBase.setUpdateTime(DateUtils.getNowDate());
        return vppMgrAggBaseMapper.updateVppMgrAggBase(vppMgrAggBase);
    }

    /**
     * 批量删除聚合商-基础信息
     *
     * @param aggBaseIds 需要删除的聚合商-基础信息主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrAggBaseByAggBaseIds(Long[] aggBaseIds) {
        return vppMgrAggBaseMapper.deleteVppMgrAggBaseByAggBaseIds(aggBaseIds);
    }

    /**
     * 删除聚合商-基础信息信息
     *
     * @param aggBaseId 聚合商-基础信息主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrAggBaseByAggBaseId(Long aggBaseId) {
        return vppMgrAggBaseMapper.deleteVppMgrAggBaseByAggBaseId(aggBaseId);
    }

    @Override
    public VppMgrAggBase selectVppMgrAggbaseByUagid(Long uagid) {
        //根据uagid查找合同,查找关联的聚合商信息
        VppMgrCnt vppMgrCnt = cntMapper.selectVppMgrCntByUagId(uagid);
        if(vppMgrCnt==null)
        {
            return null;
        }
        VppMgrAggBase vppMgrAggBase = vppMgrAggBaseMapper.selectVppMgrAggBaseByAggBaseId(vppMgrCnt.getUserId());
        System.out.println(vppMgrAggBase);
        return vppMgrAggBase;
    }
}