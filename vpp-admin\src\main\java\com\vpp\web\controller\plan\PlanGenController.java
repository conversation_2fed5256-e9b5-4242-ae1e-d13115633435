package com.vpp.web.controller.plan;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.plan.domain.PlanGen;
import com.vpp.plan.service.IPlanGenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 自动生成的计划Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/vpp/gen")
public class PlanGenController extends BaseController {
    @Autowired
    private IPlanGenService planGenService;

    /**
     * 查询自动生成的计划列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:gen:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanGen planGen) {
        startPage();
        List<PlanGen> list = planGenService.selectPlanGenList(planGen);
        return getDataTable(list);
    }

    /**
     * 导出自动生成的计划列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:gen:export')")
    @Log(title = "自动生成的计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanGen planGen) {
        List<PlanGen> list = planGenService.selectPlanGenList(planGen);
        ExcelUtil<PlanGen> util = new ExcelUtil<PlanGen>(PlanGen.class);
        util.exportExcel(response, list, "自动生成的计划数据");
    }

    /**
     * 获取自动生成的计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('vpp:gen:query')")
    @GetMapping(value = "/{planGenId}")
    public AjaxResult getInfo(@PathVariable("planGenId") Long planGenId) {
        return success(planGenService.selectPlanGenByPlanGenId(planGenId));
    }

    /**
     * 新增自动生成的计划
     */
    @PreAuthorize("@ss.hasPermi('vpp:gen:add')")
    @Log(title = "自动生成的计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanGen planGen) {
        return toAjax(planGenService.insertPlanGen(planGen));
    }

    /**
     * 修改自动生成的计划
     */
    @PreAuthorize("@ss.hasPermi('vpp:gen:edit')")
    @Log(title = "自动生成的计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanGen planGen) {
        return toAjax(planGenService.updatePlanGen(planGen));
    }

    /**
     * 删除自动生成的计划
     */
    @PreAuthorize("@ss.hasPermi('vpp:gen:remove')")
    @Log(title = "自动生成的计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planGenIds}")
    public AjaxResult remove(@PathVariable Long[] planGenIds) {
        return toAjax(planGenService.deletePlanGenByPlanGenIds(planGenIds));
    }
}