-- -- 省市区菜单生成语句
--
--
-- -- 省份表菜单生成于语句
-- -- 菜单 SQL
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('省份', '3', '1', 'province', 'area/province/index', 1, 0, 'C', '0', '0', 'system:province:list', '#', 'admin', sysdate(), '', null, '省份菜单');
--
-- -- 按钮父菜单ID
-- SELECT @parentId := LAST_INSERT_ID();
--
-- -- 按钮 SQL
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('省份查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:province:query',        '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('省份新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:province:add',          '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('省份修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:province:edit',         '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('省份删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:province:remove',       '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('省份导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:province:export',       '#', 'admin', sysdate(), '', null, '');
--
--
-- --  城市表菜单生成于语句
--
-- -- 菜单 SQL
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('城市设置', '3', '1', 'city', 'area/city/index', 1, 0, 'C', '0', '0', 'system:city:list', '#', 'admin', sysdate(), '', null, '城市设置菜单');
--
-- -- 按钮父菜单ID
-- SELECT @parentId := LAST_INSERT_ID();
--
-- -- 按钮 SQL
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('城市设置查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:city:query',        '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('城市设置新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:city:add',          '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('城市设置修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:city:edit',         '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('城市设置删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:city:remove',       '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('城市设置导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:city:export',       '#', 'admin', sysdate(), '', null, '');
--
--
--
-- -- 区域表菜单生成于语句
-- -- 菜单 SQL
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('地区设置', '3', '1', 'area', 'area/area/index', 1, 0, 'C', '0', '0', 'system:area:list', '#', 'admin', sysdate(), '', null, '地区设置菜单');
--
-- -- 按钮父菜单ID
-- SELECT @parentId := LAST_INSERT_ID();
--
-- -- 按钮 SQL
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('地区设置查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:area:query',        '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('地区设置新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:area:add',          '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('地区设置修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:area:edit',         '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('地区设置删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:area:remove',       '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('地区设置导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:area:export',       '#', 'admin', sysdate(), '', null, '');
--
--
-- -- 街道表菜单生成于语句
--
-- -- 菜单 SQL
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('街道设置', '3', '1', 'street', 'area/street/index', 1, 0, 'C', '0', '0', 'system:street:list', '#', 'admin', sysdate(), '', null, '街道设置菜单');
--
-- -- 按钮父菜单ID
-- SELECT @parentId := LAST_INSERT_ID();
--
-- -- 按钮 SQL
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('街道设置查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:street:query',        '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('街道设置新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:street:add',          '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('街道设置修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:street:edit',         '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('街道设置删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:street:remove',       '#', 'admin', sysdate(), '', null, '');
--
-- insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- values('街道设置导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:street:export',       '#', 'admin', sysdate(), '', null, '');