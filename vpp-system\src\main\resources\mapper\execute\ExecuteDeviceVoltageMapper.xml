<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.execute.mapper.ExecuteDeviceVoltageMapper">

    <resultMap type="ExecuteDeviceVoltage" id="ExecuteDeviceVoltageResult">
        <result property="timestamp"    column="timestamp"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceVoltage"    column="device_voltage"    />
    </resultMap>

    <sql id="selectExecuteDeviceVoltageVo">
        select timestamp, device_id, device_voltage from execute_device_voltage
    </sql>

    <select id="selectExecuteDeviceVoltageList" parameterType="ExecuteDeviceVoltage" resultMap="ExecuteDeviceVoltageResult">
        <include refid="selectExecuteDeviceVoltageVo"/>
        <where>
            <if test="deviceVoltage != null "> and device_voltage = #{deviceVoltage}</if>
        </where>
    </select>

    <select id="selectExecuteDeviceVoltageByTimestamp" parameterType="Date" resultMap="ExecuteDeviceVoltageResult">
        <include refid="selectExecuteDeviceVoltageVo"/>
        where timestamp = #{timestamp}
    </select>

    <insert id="insertExecuteDeviceVoltage" parameterType="ExecuteDeviceVoltage">
        insert into execute_device_voltage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">timestamp,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="deviceVoltage != null">device_voltage,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">#{timestamp},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceVoltage != null">#{deviceVoltage},</if>
        </trim>
    </insert>

    <update id="updateExecuteDeviceVoltage" parameterType="ExecuteDeviceVoltage">
        update execute_device_voltage
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceVoltage != null">device_voltage = #{deviceVoltage},</if>
        </trim>
        where timestamp = #{timestamp}
    </update>

    <delete id="deleteExecuteDeviceVoltageByTimestamp" parameterType="Date">
        delete from execute_device_voltage where timestamp = #{timestamp}
    </delete>

    <delete id="deleteExecuteDeviceVoltageByTimestamps" parameterType="String">
        delete from execute_device_voltage where timestamp in
        <foreach item="timestamp" collection="array" open="(" separator="," close=")">
            #{timestamp}
        </foreach>
    </delete>
</mapper>