package com.vpp.re.mapper;

import com.vpp.re.domain.VppBasicInformation;
import com.vpp.re.domain.VppExchangeOperationOverview;
import com.vpp.re.domain.VppOperationMonitoring;
import org.apache.ibatis.annotations.Mapper;

import java.util.Map;

/**
 * 运行概览表 Mapper 接口
 */
@Mapper
public interface VppAggregateResponseExecutionTrackingMapper {

    /**
     * 根据邀约计划ID查询运行概览列表（支持分页）
     * @param params 查询参数（invitationId）
     * @return 运行概览列表（分页）
     */
    VppExchangeOperationOverview selectByInvitationId(Map<String, Object> params);

    /**
     * 根据邀约计划ID查询基本信息
     * @param params 查询参数（invitationId）
     * @return 基本信息
     */
    VppBasicInformation selectBasicInfoByInvitationId(Map<String, Object> params);

    /**
     * 根据邀约计划ID查询运行监控表
     * @param params 查询参数（invitationId）
     * @return 基本信息
     */
    VppOperationMonitoring selectOperationByInvitationId(Map<String, Object> params);
}
