package com.vpp.re.service;

import com.vpp.re.domain.VppBasicInformation;
import com.vpp.re.domain.VppExchangeOperationOverview;
import com.vpp.re.domain.VppOperationMonitoring;

public interface IVppAggregateResponseExecutionTrackingService {

    /**
     * 根据邀约计划ID查询运行概览列表（分页）
     * @param invitationId 关联的邀约计划ID
     * @return 分页后的运行概览列表
     */
    VppExchangeOperationOverview getByInvitationId(Long invitationId);

    /**
     * 根据邀约计划ID查询基本信息
     * @param invitationId 关联的邀约计划ID
     * @return 分页后的运行概览列表
     */
    VppBasicInformation selectBasicInfoByInvitationId(Long invitationId);

    /**
     * 根据邀约计划ID查询运行监控表
     * @param invitationId 关联的邀约计划ID
     * @return 分页后的运行概览列表
     */
    VppOperationMonitoring selectOperationByInvitationId(Long invitationId);
}
