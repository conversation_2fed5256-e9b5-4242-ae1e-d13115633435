package com.vpp.mgr.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 聚合商-技术参数对象 vpp_mgr_agg_para
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VppMgrAggPara extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long aggParaId;

    /**
     * 聚合容量(kW)
     */
    @Excel(name = "聚合容量(kW)")
    private BigDecimal aggCap;

    /**
     * 调节容量(kW)
     */
    @Excel(name = "调节容量(kW)")
    private BigDecimal regCap;

    /**
     * 最大上调容量(kW)
     */
    @Excel(name = "最大上调容量(kW)")
    private BigDecimal maxUp;

    /**
     * 最大下调容量(kW)
     */
    @Excel(name = "最大下调容量(kW)")
    private BigDecimal maxDown;

    /**
     * 调节持续时长(min)
     */
    @Excel(name = "调节持续时长(min)")
    private Long regDur;

    /**
     * 最大上调容量持续时长(min)
     */
    @Excel(name = "最大上调容量持续时长(min)")
    private Long maxUpDur;

    /**
     * 最大下调容量持续时长(min)
     */
    @Excel(name = "最大下调容量持续时长(min)")
    private Long maxDownDur;

    /**
     * 最大上调容量调节可靠性(%)
     */
    @Excel(name = "最大上调容量调节可靠性(%)")
    private BigDecimal maxUpRel;

    /**
     * 最大下调容量调节可靠性(%)
     */
    @Excel(name = "最大下调容量调节可靠性(%)")
    private BigDecimal maxDownRel;

    /**
     * 调节速率(kW/min)
     */
    @Excel(name = "调节速率(kW/min)")
    private BigDecimal regRate;

    /**
     * 上调速率(kW/min)
     */
    @Excel(name = "上调速率(kW/min)")
    private BigDecimal upRate;

    /**
     * 下调速率(kW/min)
     */
    @Excel(name = "下调速率(kW/min)")
    private BigDecimal downRate;

    /**
     * 调节精度(%)
     */
    @Excel(name = "调节精度(%)")
    private BigDecimal regPrec;

    /**
     * 爬坡速度(kW/min)
     */
    @Excel(name = "爬坡速度(kW/min)")
    private BigDecimal rampspd;

    /**
     * 爬坡时长(min)
     */
    @Excel(name = "爬坡时长(min)")
    private Long rampdur;

    /**
     * 上调爬坡速度(kW/min)
     */
    @Excel(name = "上调爬坡速度(kW/min)")
    private BigDecimal upRampspd;

    /**
     * 上调爬坡时长(min)
     */
    @Excel(name = "上调爬坡时长(min)")
    private Long upRampdur;

    /**
     * 下调爬坡速度(kW/min)
     */
    @Excel(name = "下调爬坡速度(kW/min)")
    private BigDecimal downRampspd;

    /**
     * 下调爬坡时长(min)
     */
    @Excel(name = "下调爬坡时长(min)")
    private Long downRampdur;

    /**
     * 机构(部门)ID
     */
    @Excel(name = "机构(部门)ID")
    private Long deptId;

    /**
     * 用户ID(sys_user)
     */
    @Excel(name = "用户ID(sys_user)")
    private Long userId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

}