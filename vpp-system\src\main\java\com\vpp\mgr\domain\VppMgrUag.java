package com.vpp.mgr.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Transient;

import java.math.BigDecimal;
import java.util.List;

/**
 * 聚合商用户-信息对象 vpp_mgr_uag
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VppMgrUag extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long uagId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String uagName;

    /**
     * 用户编码，自动生成
     */
    @Excel(name = "用户编码，自动生成")
    private String uagCode;

    /**
     * 用户户号
     */
    // @Excel(name = "用户户号")
    // private String uagAccount;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contact;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String phone;

    /**
     * 设备数量
     */
    @Excel(name = "设备数量")
    private Long deviceCount;

    /**
     * 行政区域
     */
    @Excel(name = "行政区域")
    private String region;

    /**
     * 参与日前响应
     */
    @Excel(name = "参与日前响应")
    private Integer dayBefore;

    /**
     * 参与日内响应
     */
    @Excel(name = "参与日内响应")
    private Integer dayIn;

    /**
     * 用户类型(单选) 01-光伏发电用户,02-风电发电用户,03-充电桩负荷用户,04-负荷类用户,05-储电类用户
     */
    @Excel(name = "用户类型(单选) 01-光伏发电用户,02-风电发电用户,03-充电桩负荷用户,04-负荷类用户,05-储电类用户")
    private String uagType;

    /**
     * 设备类型(多选) 01-负荷设备,02-发电设备,03-储电设备
     */
    @Excel(name = "设备类型(多选) 01-负荷设备,02-发电设备,03-储电设备")
    private String deviceType;

    /**
     * 最大上调容量
     */
    @Excel(name = "最大上调容量")
    private BigDecimal maxUp;

    /**
     * 最大下调容量
     */
    @Excel(name = "最大下调容量")
    private BigDecimal maxDown;

    /**
     * 图片地址
     */
    @Excel(name = "图片地址")
    private String imgUrl;

    /**
     * 所属机构ID
     */
    @Excel(name = "所属机构ID")
    private Long deptId;

    /**
     * 聚合商id(sys_user用户ID)
     */
    @Excel(name = "聚合商用户用户id(sys_user用户ID)")
    private Long userId;

    @Transient
    private List<VppMgrUagAccountNum> accountNumList;

}