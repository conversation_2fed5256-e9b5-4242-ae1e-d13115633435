package com.vpp.tracking.mapper;

import com.vpp.tracking.domain.TrackingEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TrackingMapper {
    @Select("select invitation_id id,invitation_name name," +
            "invitation_no no,response_date requirementDate,demand_time_slots requirementTime,demand_region province," +
            "load_direction directive,response_type type,source_release as `from` from vpp_exchange_invitation where publish_status='已发布' and (activity_status!='已结束' and activity_status!='已取消')")
    List<TrackingEvent> listTrackEvent();
    /**
     * 获取爬坡率,向下
     */
    @Select("select max(actual_load)/(case max(create_time)-min(create_time) when null then 1 when 0 then 1 else (max(unix_timestamp(create_time))-min(unix_timestamp(create_time)))/60 end) from  vpp_operation_monitoring\n" +
            "where invitation_id=#{invitation_id} and load_direction='填谷方向'")
    Float ClimbDownhill(@Param("invitation_id")Long invitation_id);

    /**
     * 向上爬坡
     */
    @Select("select max(actual_load)/(case max(create_time)-min(create_time) when null then 1 when 0 then 1 else (max(unix_timestamp(create_time))-min(unix_timestamp(create_time)))/60 end) from  vpp_operation_monitoring\n" +
            "where invitation_id=#{invitation_id} ")
    Float ClimbUphill(@Param("invitation_id")Long invitation_id);
    @Select("SELECT (max(actual_load))/(select bid_response_capacity \n" +
            "from vpp_exchange_invitation where invitation_id=#{invitation_id})*100\n" +
            " FROM fiotcp_vpp.vpp_operation_monitoring where invitation_id=#{invitation_id};")
    Float completionRate(@Param("invitation_id")Long eventId);
}
