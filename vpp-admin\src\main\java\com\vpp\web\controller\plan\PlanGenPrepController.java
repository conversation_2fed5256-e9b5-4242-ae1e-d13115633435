package com.vpp.web.controller.plan;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.plan.domain.PlanGenPrep;
import com.vpp.plan.service.IPlanGenPrepService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 预备设备(当出现异常时，使用预备设备)Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/vpp/genPrep")
public class PlanGenPrepController extends BaseController {
    @Autowired
    private IPlanGenPrepService planGenPrepService;

    /**
     * 查询预备设备列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:genPrep:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanGenPrep planGenPrep) {
        startPage();
        List<PlanGenPrep> list = planGenPrepService.selectPlanGenPrepList(planGenPrep);
        return getDataTable(list);
    }

    /**
     * 导出预备设备列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:genPrep:export')")
    @Log(title = "预备设备，当出现异常时，使用预备设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanGenPrep planGenPrep) {
        List<PlanGenPrep> list = planGenPrepService.selectPlanGenPrepList(planGenPrep);
        ExcelUtil<PlanGenPrep> util = new ExcelUtil<PlanGenPrep>(PlanGenPrep.class);
        util.exportExcel(response, list, "预备设备，当出现异常时，使用预备设备数据");
    }

    /**
     * 获取预备设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('vpp:genPrep:query')")
    @GetMapping(value = "/{planGenPrepId}")
    public AjaxResult getInfo(@PathVariable("planGenPrepId") Long planGenPrepId) {
        return success(planGenPrepService.selectPlanGenPrepByPlanGenPrepId(planGenPrepId));
    }

    /**
     * 新增预备设备
     */
    @PreAuthorize("@ss.hasPermi('vpp:genPrep:add')")
    @Log(title = "预备设备，当出现异常时，使用预备设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanGenPrep planGenPrep) {
        return toAjax(planGenPrepService.insertPlanGenPrep(planGenPrep));
    }

    /**
     * 修改预备设备
     */
    @PreAuthorize("@ss.hasPermi('vpp:genPrep:edit')")
    @Log(title = "预备设备，当出现异常时，使用预备设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanGenPrep planGenPrep) {
        return toAjax(planGenPrepService.updatePlanGenPrep(planGenPrep));
    }

    /**
     * 删除预备设备
     */
    @PreAuthorize("@ss.hasPermi('vpp:genPrep:remove')")
    @Log(title = "预备设备，当出现异常时，使用预备设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planGenPrepIds}")
    public AjaxResult remove(@PathVariable Long[] planGenPrepIds) {
        return toAjax(planGenPrepService.deletePlanGenPrepByPlanGenPrepIds(planGenPrepIds));
    }
}