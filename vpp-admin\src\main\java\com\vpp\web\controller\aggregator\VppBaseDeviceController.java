package com.vpp.web.controller.aggregator;

import com.vpp.aggregator.domain.VppBaseDevice;
import com.vpp.aggregator.service.IVppBaseDeviceService;
import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 聚合用户设备Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/vpp/baseDevice")
// @Api(tags="聚合用户设备")
public class VppBaseDeviceController extends BaseController {
    @Autowired
    private IVppBaseDeviceService vppBaseDeviceService;

    /**
     * 查询聚合用户设备列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseDevice:list')")
    @GetMapping("/list")
    // @ApiOperation(value = "获取聚合用户设备列表")
    public TableDataInfo list(VppBaseDevice vppBaseDevice) {
        startPage();
        List<VppBaseDevice> list = vppBaseDeviceService.selectVppBaseDeviceList(vppBaseDevice);
        return getDataTable(list);
    }

    /**
     * 导出聚合用户设备列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseDevice:export')")
    @Log(title = "聚合用户设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    // @ApiOperation(value = "导出聚合用户设备列表")
    public void export(HttpServletResponse response, VppBaseDevice vppBaseDevice) {
        List<VppBaseDevice> list = vppBaseDeviceService.selectVppBaseDeviceList(vppBaseDevice);
        ExcelUtil<VppBaseDevice> util = new ExcelUtil<VppBaseDevice>(VppBaseDevice.class);
        util.exportExcel(response, list, "聚合用户设备数据");
    }

    /**
     * 获取聚合用户设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseDevice:query')")
    @GetMapping(value = "/{deviceId}")
    // @ApiOperation(value = "获取聚合用户设备详细信息")
    public AjaxResult getInfo(@PathVariable("deviceId") Long deviceId) {
        return success(vppBaseDeviceService.selectVppBaseDeviceByDeviceId(deviceId));
    }

    /**
     * 新增聚合用户设备
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseDevice:add')")
    @Log(title = "聚合用户设备", businessType = BusinessType.INSERT)
    @PostMapping
    // @ApiOperation(value = "新增聚合用户设备")
    public AjaxResult add(@RequestBody VppBaseDevice vppBaseDevice) {
        return toAjax(vppBaseDeviceService.insertVppBaseDevice(vppBaseDevice));
    }

    /**
     * 修改聚合用户设备
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseDevice:edit')")
    @Log(title = "聚合用户设备", businessType = BusinessType.UPDATE)
    @PutMapping
    // @ApiOperation(value = "修改聚合用户设备")
    public AjaxResult edit(@RequestBody VppBaseDevice vppBaseDevice) {
        return toAjax(vppBaseDeviceService.updateVppBaseDevice(vppBaseDevice));
    }

    /**
     * 删除聚合用户设备
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseDevice:remove')")
    @Log(title = "聚合用户设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    // @ApiOperation(value = "删除聚合用户设备")
    public AjaxResult remove(@PathVariable Long[] deviceIds) {
        return toAjax(vppBaseDeviceService.deleteVppBaseDeviceByDeviceIds(deviceIds));
    }
}