package com.vpp.sm.service;

import com.vpp.common.core.domain.AjaxResult;
import com.vpp.sm.domain.VppSettlementUag;
import com.vpp.sm.domain.VppUagSettlement;
import com.vpp.sm.mapper.VppSettlementUserRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class VppSettlementUserService {
    @Autowired
    VppSettlementUserRecordMapper mapper;
    public AjaxResult allSettlementUserRecords(Long uagid) {
        List<VppUagSettlement> vppUagSettlements = mapper.allVppSettlementUserRecord(uagid);
        AjaxResult result=new AjaxResult();
        result.put("code",200);
        result.put("msg","查询成功");
        result.put("data",vppUagSettlements);

        return result;
    }

    public AjaxResult baseUserSettlement(String accountNum) {
        /**
         * 获取基本信息
         */

        return null;
    }

    public AjaxResult listVppUag(Long dept) {
        /**
         * 先根据dept,查找用户id,在根据用户id查找旗下的所有聚合用户
         */
        List<VppSettlementUag> maps = mapper.listVppUag(dept);
        AjaxResult result=new AjaxResult();
        result.put("code",200);
        result.put("msg","查询成功");
        result.put("data",maps);
        return result;
    }
}
