package com.vpp.plan.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 自动生成计划设备对象 plan_gen_devices
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public class PlanGenDevices extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long planGenDevicesId;

    /**
     * 关联plan_gen表
     */
    @Excel(name = "关联plan_gen表")
    private Long planGenId;

    /**
     * 关联vpp_base_device设备id
     */
    @Excel(name = "关联vpp_base_device设备id")
    private Long deviceId;

    /**
     * 设备额定功率
     */
    @Excel(name = "设备额定功率")
    private Long planGenRatedPower;

    /**
     * 设备额定电压
     */
    @Excel(name = "设备额定电压")
    private Long planGenRatedVoltage;

    /**
     * 设备额定电流
     */
    @Excel(name = "设备额定电流")
    private Long planGenRatedCurrent;

    public void setPlanGenDevicesId(Long planGenDevicesId) {
        this.planGenDevicesId = planGenDevicesId;
    }

    public Long getPlanGenDevicesId() {
        return planGenDevicesId;
    }

    public void setPlanGenId(Long planGenId) {
        this.planGenId = planGenId;
    }

    public Long getPlanGenId() {
        return planGenId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setPlanGenRatedPower(Long planGenRatedPower) {
        this.planGenRatedPower = planGenRatedPower;
    }

    public Long getPlanGenRatedPower() {
        return planGenRatedPower;
    }

    public void setPlanGenRatedVoltage(Long planGenRatedVoltage) {
        this.planGenRatedVoltage = planGenRatedVoltage;
    }

    public Long getPlanGenRatedVoltage() {
        return planGenRatedVoltage;
    }

    public void setPlanGenRatedCurrent(Long planGenRatedCurrent) {
        this.planGenRatedCurrent = planGenRatedCurrent;
    }

    public Long getPlanGenRatedCurrent() {
        return planGenRatedCurrent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("planGenDevicesId", getPlanGenDevicesId())
                .append("planGenId", getPlanGenId())
                .append("deviceId", getDeviceId())
                .append("planGenRatedPower", getPlanGenRatedPower())
                .append("planGenRatedVoltage", getPlanGenRatedVoltage())
                .append("planGenRatedCurrent", getPlanGenRatedCurrent())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}