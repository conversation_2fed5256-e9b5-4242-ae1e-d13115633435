package com.vpp.area.service;

import com.vpp.area.domain.SysAddrArea;

import java.util.List;

/**
 * 地区设置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface ISysAddrAreaService
{
    /**
     * 查询地区设置
     *
     * @param id 地区设置主键
     * @return 地区设置
     */
    public SysAddrArea selectSysAddrAreaById(String id);

    /**
     * 查询地区设置列表
     *
     * @param sysAddrArea 地区设置
     * @return 地区设置集合
     */
    public List<SysAddrArea> selectSysAddrAreaList(SysAddrArea sysAddrArea);

    /**
     * 新增地区设置
     *
     * @param sysAddrArea 地区设置
     * @return 结果
     */
    public int insertSysAddrArea(SysAddrArea sysAddrArea);

    /**
     * 修改地区设置
     *
     * @param sysAddrArea 地区设置
     * @return 结果
     */
    public int updateSysAddrArea(SysAddrArea sysAddrArea);

    /**
     * 批量删除地区设置
     *
     * @param ids 需要删除的地区设置主键集合
     * @return 结果
     */
    public int deleteSysAddrAreaByIds(String[] ids);

    /**
     * 删除地区设置信息
     *
     * @param id 地区设置主键
     * @return 结果
     */
    public int deleteSysAddrAreaById(String id);
}
