<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.vpp.sm.mapper.VppSettlementRecordMapper">

    <!-- 结果映射：数据库列与Entity属性对应 -->
    <resultMap id="BaseResultMap" type="com.vpp.sm.domain.VppSettlementRecord">
        <id column="settlement_id" jdbcType="BIGINT" property="settlementId"/>
        <result column="invitation_id" jdbcType="BIGINT" property="invitationId"/>
        <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime"/>
        <result column="actual_electricity" jdbcType="DECIMAL" property="actualElectricity"/>
        <result column="subsidy_total" jdbcType="DECIMAL" property="subsidyTotal"/>
        <result column="settlement_total" jdbcType="DECIMAL" property="settlementTotal"/>
        <result column="deviation_penalty" jdbcType="DECIMAL" property="deviationPenalty"/>
        <result column="settlement_unit_price" jdbcType="DECIMAL" property="settlementUnitPrice"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 查询邀约计划关联的结算记录 -->
    <select id="selectByInvitationId" resultMap="BaseResultMap">
        SELECT * FROM vpp_settlement_record
        WHERE invitation_id = #{invitationId}
        ORDER BY settlement_time DESC
    </select>

    <!-- 插入结算记录 -->
    <insert id="insert" parameterType="com.vpp.sm.domain.VppSettlementRecord"
            useGeneratedKeys="true" keyProperty="settlementId">
        INSERT INTO vpp_settlement_record (
            invitation_id, settlement_time, actual_electricity, subsidy_total,
            settlement_total, deviation_penalty, settlement_unit_price, create_by, create_time,
            update_by, update_time
        ) VALUES (
                     #{invitationId}, #{settlementTime}, #{actualElectricity}, #{subsidyTotal},
                     #{settlementTotal}, #{deviationPenalty}, #{settlementUnitPrice}, #{createBy}, #{createTime},
                     #{updateBy}, #{updateTime}
                 )
    </insert>

    <!-- 查询所有结算记录（统计概览用） -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM vpp_settlement_record
        ORDER BY settlement_time DESC
    </select>
</mapper>