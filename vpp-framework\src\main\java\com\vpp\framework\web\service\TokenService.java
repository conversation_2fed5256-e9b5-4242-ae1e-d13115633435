package com.vpp.framework.web.service;

import com.vpp.common.constant.CacheConstants;
import com.vpp.common.constant.Constants;
import com.vpp.common.core.domain.model.LoginUser;
import com.vpp.common.core.redis.RedisCache;
import com.vpp.common.utils.ServletUtils;
import com.vpp.common.utils.StringUtils;
import com.vpp.common.utils.ip.AddressUtils;
import com.vpp.common.utils.ip.IpUtils;
import com.vpp.common.utils.uuid.IdUtils;
import com.vpp.system.mapper.SysUserMapper;
import com.vpp.system.service.ISysUserService;
import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService {
    private static final Logger log = LoggerFactory.getLogger(TokenService.class);

    @Resource
    private SysUserMapper userMapper;

    @Autowired
    private ISysUserService userService;

    // 令牌自定义标识
    @Value("${token.header}")
    private String header;

    // 令牌秘钥
    @Value("${token.secret}")
    private String secret;

    // 令牌有效期（默认30分钟）
    @Value("${token.expireTime}")
    private int expireTime;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private static final Long MILLIS_MINUTE_TWENTY = 20 * 60 * 1000L;

    @Autowired
    private RedisCache redisCache;


    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            try {
                Claims claims = parseToken(token);
                // 解析对应的权限以及用户信息
                String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
                String userKey = getTokenKey(uuid);
                LoginUser user = redisCache.getCacheObject(userKey);
                return user;
            } catch (Exception e) {
                log.error("获取用户信息异常'{}'", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
//     public LoginUser getLoginUser(HttpServletRequest request) {
//         // 获取请求携带的令牌
//         String token = getToken(request);
//         String openid = request.getHeader("openid");
//         // System.out.println(token);
//         if (StringUtils.isNotEmpty(token)) {
//             // 发送到verify请求
//             try {
//                 System.out.println("发起请求");
//                 URL url = new URL("http://fiotcp.com:17329/api/v0/verify/verify");
//                 // URL url = new URL("http://localhost:20000/api/v0/verify/verify");
//                 HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//                 connection.setRequestMethod("POST");
//                 connection.setRequestProperty("Content-Type", "application/json");
//                 connection.setDoOutput(true);
//                 String jsonInput = "{\n" +
//                         "    \"token\":\"" + token + "\",\n" +
//                         "    \"openid\":\"" + openid + "\",\n" +
//                         "    \"appid\":\"\",\n" +
//                         "    \"appkey\":\"\"\n" +
//                         "}";
//                 byte[] inputBytes = jsonInput.getBytes(StandardCharsets.UTF_8);
//                 OutputStream outputStream = connection.getOutputStream();
//                 outputStream.write(inputBytes, 0, inputBytes.length);
//                 int responseCode = connection.getResponseCode();
//                 System.out.println("Response Code:" + responseCode);
//                 Map<String, Object> dataMap;
//                 if (responseCode == HttpURLConnection.HTTP_OK) {
//                     Scanner scanner = new Scanner(connection.getInputStream(), "UTF-8");
//                     String next = scanner.useDelimiter("\\A").next();
//                     System.out.println("Response Body:" + next);
//
//                     Map<String, Object> rootMap = JSON.parseObject(next, Map.class);
//                     dataMap = (Map<String, Object>) rootMap.get("data");
//                 } else {
//                     return null;
//                 }
//                 connection.disconnect();
//                 LoginUser user = new LoginUser();
//                 SysUser sysUser = userMapper.selectOneByOpenid(openid);
//                 if (sysUser == null) {
//                     sysUser = new SysUser();
//                     sysUser.setOpenid(openid);
//                     sysUser.setStatus("0");
//                     sysUser.setDelFlag("0");
//                     sysUser.setCreateBy("sys");
//                     sysUser.setCreateTime(new Date());
//
//                     sysUser.setUserName((String) dataMap.get("username"));
//                     sysUser.setEmail((String) dataMap.get("email"));
//                     sysUser.setPhonenumber((String) dataMap.get("phone"));
//
//                     userService.insertUser(sysUser);
//                     user.setInitUser(true);
//                 }
//                 user.setOpenid(openid);
//                 user.setUser(sysUser);
//                 user.setUserId(sysUser.getUserId());
//                 return user;
//             } catch (Exception e) {
//                 e.printStackTrace();
//             }
//
// //            try
// //            {
// //                Claims claims = parseToken(token);
// //                System.out.println(claims);
// //                // 解析对应的权限以及用户信息
// //                String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
// //                String userKey = getTokenKey(uuid);
// //
// //                LoginUser user = redisCache.getCacheObject(userKey);
// //                return user;
// //            }
// //            catch (Exception e)
// //            {
// //                log.error("获取用户信息异常'{}'", e.getMessage());
// //            }
//
//         }
//         return null;
//     }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userKey = getTokenKey(token);
            redisCache.deleteObject(userKey);
        }
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createToken(LoginUser loginUser) {
        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        setUserAgent(loginUser);
        refreshToken(loginUser);

        Map<String, Object> claims = new HashMap<>();
        claims.put(Constants.LOGIN_USER_KEY, token);
        claims.put(Constants.JWT_USERNAME, loginUser.getUsername());
        return createToken(claims);
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @param loginUser 登录信息
     * @return 令牌
     */
    public void verifyToken(LoginUser loginUser) {

        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TWENTY) {
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisCache.setCacheObject(userKey, loginUser, expireTime, TimeUnit.MINUTES);
    }

    /**
     * 设置用户代理信息
     *
     * @param loginUser 登录信息
     */
    public void setUserAgent(LoginUser loginUser) {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = IpUtils.getIpAddr();
        loginUser.setIpaddr(ip);
        loginUser.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
        loginUser.setBrowser(userAgent.getBrowser().getName());
        loginUser.setOs(userAgent.getOperatingSystem().getName());
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String createToken(Map<String, Object> claims) {
        String token = Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, secret).compact();
        return token;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.getSubject();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(header);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    private String getTokenKey(String uuid) {
        return CacheConstants.LOGIN_TOKEN_KEY + uuid;
    }
}
