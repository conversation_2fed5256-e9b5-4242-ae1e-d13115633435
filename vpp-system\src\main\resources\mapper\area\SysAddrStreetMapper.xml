<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.area.mapper.SysAddrStreetMapper">

    <resultMap type="SysAddrStreet" id="SysAddrStreetResult">
        <result property="id"    column="id"    />
        <result property="streetCode"    column="street_code"    />
        <result property="areaCode"    column="area_code"    />
        <result property="streetName"    column="street_name"    />
        <result property="shortName"    column="short_name"    />
        <result property="lng"    column="lng"    />
        <result property="lat"    column="lat"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysAddrStreetVo">
        select id, street_code, area_code, street_name, short_name, lng, lat, sort, create_time, update_time, create_by, update_by, remark from sys_addr_street
    </sql>

    <select id="selectSysAddrStreetList" parameterType="SysAddrStreet" resultMap="SysAddrStreetResult">
        <include refid="selectSysAddrStreetVo"/>
        <where>
            <if test="streetCode != null  and streetCode != ''"> and street_code = #{streetCode}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="streetName != null  and streetName != ''"> and street_name like concat('%', #{streetName}, '%')</if>
            <if test="shortName != null  and shortName != ''"> and short_name like concat('%', #{shortName}, '%')</if>
            <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectSysAddrStreetById" parameterType="Long" resultMap="SysAddrStreetResult">
        <include refid="selectSysAddrStreetVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysAddrStreet" parameterType="SysAddrStreet">
        insert into sys_addr_street
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="streetCode != null and streetCode != ''">street_code,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="streetName != null and streetName != ''">street_name,</if>
            <if test="shortName != null and shortName != ''">short_name,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="streetCode != null and streetCode != ''">#{streetCode},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="streetName != null and streetName != ''">#{streetName},</if>
            <if test="shortName != null and shortName != ''">#{shortName},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSysAddrStreet" parameterType="SysAddrStreet">
        update sys_addr_street
        <trim prefix="SET" suffixOverrides=",">
            <if test="streetCode != null and streetCode != ''">street_code = #{streetCode},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="streetName != null and streetName != ''">street_name = #{streetName},</if>
            <if test="shortName != null and shortName != ''">short_name = #{shortName},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAddrStreetById" parameterType="Long">
        delete from sys_addr_street where id = #{id}
    </delete>

    <delete id="deleteSysAddrStreetByIds" parameterType="String">
        delete from sys_addr_street where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>