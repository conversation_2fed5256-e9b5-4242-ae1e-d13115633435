package com.vpp.execute.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 设备实时检测状态对象 execute_device_status
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public class ExecuteDeviceStatus extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Date timestamp;

    /**
     * 关联vpp_base_device设备表
     */
    private Long deviceId;

    /**
     * 设备实时状态
     * 1. 储电设备：0储电，1放电
     * 2. 发电设备：0停机，1开机
     * 3. 用电设备：0拉闸断电，1合闸送电
     */
    @Excel(name = "设备实时状态")
    private Long deviceStatus;

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceStatus(Long deviceStatus) {
        this.deviceStatus = deviceStatus;
    }

    public Long getDeviceStatus() {
        return deviceStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("timestamp", getTimestamp())
                .append("deviceId", getDeviceId())
                .append("deviceStatus", getDeviceStatus())
                .toString();
    }
}