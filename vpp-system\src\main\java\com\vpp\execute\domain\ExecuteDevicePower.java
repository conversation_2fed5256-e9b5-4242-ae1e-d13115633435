package com.vpp.execute.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 设备实时检测功率对象 execute_device_power
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public class ExecuteDevicePower extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Date timestamp;

    /**
     * $column.columnComment
     */
    private Long deviceId;

    /**
     * 设备实时功率, 数据不连续，设备因天气原因存在停机可能
     */
    @Excel(name = "设备实时功率, 数据不连续，设备因天气原因存在停机可能")
    private Long devicePower;

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDevicePower(Long devicePower) {
        this.devicePower = devicePower;
    }

    public Long getDevicePower() {
        return devicePower;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("timestamp", getTimestamp())
                .append("deviceId", getDeviceId())
                .append("devicePower", getDevicePower())
                .toString();
    }
}