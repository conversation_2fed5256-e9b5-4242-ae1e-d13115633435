package com.vpp.area.service;

import com.vpp.area.domain.SysAddrArea;
import com.vpp.area.domain.SysAddrCity;
import com.vpp.area.domain.SysAddrProvince;
import com.vpp.area.domain.SysAddrStreet;
import com.vpp.area.domain.vo.SysTreeAreaVo;
import com.vpp.enums.AreaEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * packageName com.vpp.area.service
 *
 * <AUTHOR>
 * @version JDK 8
 * @className AreaConService
 * @date 2025/6/4
 * @description
 */
@Service
public class AreaConService {
    @Autowired
    private ISysAddrProvinceService iSysAddrProvinceService;

    @Autowired
    private ISysAddrCityService iSysAddrCityService;

    @Autowired
    private ISysAddrAreaService iSysAddrAreaService;

    @Autowired
    private ISysAddrStreetService iSysAddrStreetService;

    public List<SysTreeAreaVo> getSysTreeArea() {
        List<SysTreeAreaVo> sysTreeAreaVos = new ArrayList<>();
        List<SysAddrProvince> list = iSysAddrProvinceService.selectSysAddrProvinceList(new SysAddrProvince());
        for (SysAddrProvince SysAddrProvince : list) {
            // 1.省份
            SysTreeAreaVo sysTreeAreaVo = new SysTreeAreaVo();
            sysTreeAreaVo.setName(SysAddrProvince.getProvinceName());
            sysTreeAreaVo.setCode(SysAddrProvince.getProvinceCode());
            // 2.城市
            SysAddrCity sysAddrCity = new SysAddrCity();
            sysAddrCity.setProvinceCode(SysAddrProvince.getProvinceCode());
            List<SysAddrCity> sysCityEntities = iSysAddrCityService.selectSysAddrCityList(sysAddrCity);

            List<SysTreeAreaVo> children = new ArrayList<>();
            for (SysAddrCity sysCityEntity : sysCityEntities) {
                SysTreeAreaVo treeAreaVo = new SysTreeAreaVo();
                treeAreaVo.setCode(sysCityEntity.getCityCode());
                treeAreaVo.setName(sysCityEntity.getCityName());
                // 3.区域
                List<SysTreeAreaVo> areaChildren = new ArrayList<>();
                SysAddrArea sysAddrArea = new SysAddrArea();
                sysAddrArea.setCityCode(sysCityEntity.getCityCode());
                List<SysAddrArea> sysAreaEntities = iSysAddrAreaService.selectSysAddrAreaList(sysAddrArea);

                for (SysAddrArea sysAreaEntity : sysAreaEntities) {
                    SysTreeAreaVo sysTreeAreaVo1 = new SysTreeAreaVo();
                    sysTreeAreaVo1.setCode(sysAreaEntity.getAreaCode());
                    sysTreeAreaVo1.setName(sysAreaEntity.getAreaName());
                    areaChildren.add(sysTreeAreaVo1);
                }
                treeAreaVo.setChildren(areaChildren);
                children.add(treeAreaVo);
            }
            sysTreeAreaVo.setChildren(children);
            sysTreeAreaVos.add(sysTreeAreaVo);
        }
        return sysTreeAreaVos;
    }

    public List<SysAddrProvince> getProvinceList() {
        return iSysAddrProvinceService.selectSysAddrProvinceList(new SysAddrProvince());
    }

    public List<SysTreeAreaVo> getAreaInfoByCode(String code, String searchType) {
        AreaEnum areaEnum = AreaEnum.valueOf(searchType);
        List<SysTreeAreaVo> areaVoList = new ArrayList<>();

        switch (areaEnum) {
            case AREA:
                SysAddrArea sysAddrArea = new SysAddrArea();
                sysAddrArea.setCityCode(code);
                List<SysAddrArea> sysAreaEntities = iSysAddrAreaService.selectSysAddrAreaList(sysAddrArea);
                for (SysAddrArea sysAreaEntity : sysAreaEntities) {
                    SysTreeAreaVo sysTreeAreaVo1 = new SysTreeAreaVo();
                    sysTreeAreaVo1.setCode(sysAreaEntity.getAreaCode());
                    sysTreeAreaVo1.setName(sysAreaEntity.getAreaName());
                    areaVoList.add(sysTreeAreaVo1);
                }
                return areaVoList;
            case CITY:
                // 2.城市
                SysAddrCity sysAddrCity = new SysAddrCity();
                sysAddrCity.setProvinceCode(code);
                List<SysAddrCity> sysCityEntities = iSysAddrCityService.selectSysAddrCityList(sysAddrCity);
                for (SysAddrCity sysCityEntity : sysCityEntities) {
                    SysTreeAreaVo treeAreaVo = new SysTreeAreaVo();
                    treeAreaVo.setCode(sysCityEntity.getCityCode());
                    treeAreaVo.setName(sysCityEntity.getCityName());
                    areaVoList.add(treeAreaVo);
                }
                return areaVoList;
            case STREET:
                // 街道
                // 2.城市
                SysAddrStreet sysAddrStreet = new SysAddrStreet();
                sysAddrStreet.setAreaCode(code);
                List<SysAddrStreet> list = iSysAddrStreetService.selectSysAddrStreetList(sysAddrStreet);

                for (SysAddrStreet sysStreet : list) {
                    SysTreeAreaVo treeAreaVo = new SysTreeAreaVo();
                    treeAreaVo.setCode(sysStreet.getStreetCode());
                    treeAreaVo.setName(sysStreet.getStreetName());
                    areaVoList.add(treeAreaVo);
                }
                return areaVoList;
            default:
                return areaVoList;
        }
    }
}