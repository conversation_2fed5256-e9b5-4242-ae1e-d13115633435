<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.dr.mapper.VppExchangeInvitationMapper">

    <!-- 定义resultMap：数据库字段与Entity属性映射 -->
    <resultMap type="VppExchangeInvitation" id="invitationResultMap">
        <id column="invitation_id" property="invitationId" jdbcType="BIGINT"/> <!-- 主键映射 -->
        <result column="invitation_name" property="invitationName" jdbcType="VARCHAR"/>
        <result column="invitation_no" property="invitationNo" jdbcType="VARCHAR"/>
        <result column="response_date" property="responseDate" jdbcType="VARCHAR"/> <!-- 数据库DATETIME，接口用String -->
        <result column="demand_time_slots" property="demandTimeSlots" jdbcType="VARCHAR"/>
        <result column="demand_region" property="demandRegion" jdbcType="VARCHAR"/>
        <result column="response_type" property="responseType" jdbcType="VARCHAR"/>
        <result column="load_direction" property="loadDirection" jdbcType="VARCHAR"/>
        <result column="invited_user_count" property="invitedUserCount" jdbcType="INTEGER"/>
        <result column="aggregated_user_count" property="aggregatedUserCount" jdbcType="INTEGER"/>
        <result column="unreplied_user_count" property="unrepliedUserCount" jdbcType="INTEGER"/>
        <result column="non_participating_user_count" property="nonParticipatingUserCount" jdbcType="INTEGER"/>
        <result column="deadline_time" property="deadlineTime" jdbcType="VARCHAR"/> <!-- 数据库DATETIME，接口用String -->
        <result column="market_capacity" property="marketCapacity" jdbcType="DECIMAL"/>
        <result column="bid_response_capacity" property="bidResponseCapacity" jdbcType="DECIMAL"/>
        <result column="actual_response_capacity" property="actualResponseCapacity" jdbcType="DECIMAL"/>
        <result column="disclosure_time" property="disclosureTime" jdbcType="VARCHAR"/> <!-- 数据库DATETIME，接口用String -->
        <result column="aggregator_status" property="aggregatorStatus" jdbcType="VARCHAR"/>
        <result column="activity_status" property="activityStatus" jdbcType="VARCHAR"/>
        <result column="publish_status" property="publishStatus" jdbcType="VARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="VARCHAR"/> <!-- 数据库DATETIME，接口用String -->
        <result column="source_release" property="sourceRelease" jdbcType="VARCHAR"/>
        <result column="event_status" property="eventStatus" jdbcType="VARCHAR"/>
        <result column="lock_status" property="lockStatus" jdbcType="VARCHAR"/>
        <result column="lock_time" property="lockTime" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="VARCHAR"/> <!-- 数据库DATETIME，接口用String -->
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="VARCHAR"/> <!-- 数据库DATETIME，接口用String -->
        <result column="del_flag" property="delFlag" jdbcType="CHAR"/>
        <result column="sys_user_id" property="sysUserId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 查询邀约计划列表（带条件过滤） -->
    <select id="selectInvitationList" parameterType="Map" resultMap="invitationResultMap">
        SELECT * FROM vpp_exchange_invitation
        <where>
            <!-- 动态条件：邀约计划名称（模糊匹配） -->
            <if test="invitationName != null and invitationName.trim() != ''">
                AND invitation_name LIKE CONCAT('%', #{invitationName}, '%')
            </if>
            <!-- 动态条件：响应日（精确匹配） -->
            <if test="responseDate != null">
                AND response_date = #{responseDate}
            </if>
            <!-- 动态条件：邀约截止时间（精确匹配） -->
            <if test="responseDate != null">
                AND deadline_time = #{deadlineTime}
            </if>
            <!-- 动态条件：需求地区（精确匹配） -->
            <if test="demandRegion != null and demandRegion.trim() != ''">
                AND demand_region = #{demandRegion}
            </if>
            <!-- 动态条件：事件状态（枚举值） -->
            <if test="eventStatus != null">
                AND event_status = #{eventStatus}
            </if>
            <!-- 聚合商ID -->
            <if test="userId != null">
                AND sys_user_id = #{userId}
            </if>
            <!-- 逻辑删除过滤（未删除数据） -->
            <if test="delFlag == null">
                AND del_flag = '0'
            </if>
        </where>
        ORDER BY create_time DESC <!-- 按创建时间倒序 -->
    </select>

    <!-- 根据ID查询邀约计划详情 -->
    <select id="selectInvitationById" parameterType="Long" resultMap="invitationResultMap">
        SELECT * FROM vpp_exchange_invitation
        WHERE invitation_id = #{invitationId}
        AND del_flag = '0' <!-- 逻辑删除过滤 -->
    </select>

    <!-- 新增邀约计划 -->
    <insert id="insertInvitation" parameterType="VppExchangeInvitation" useGeneratedKeys="true" keyProperty="invitationId">
        INSERT INTO vpp_exchange_invitation (
            invitation_name, invitation_no, response_date, demand_time_slots,
            demand_region, response_type, load_direction, invited_user_count,
            aggregator_status, deadline_time, market_capacity, publish_status,
            source_release, event_status, publish_time, create_by, create_time,
            update_by, update_time, del_flag, sys_user_id
        ) VALUES (
                     #{invitationName}, #{invitationNo}, #{responseDate}, #{demandTimeSlots},
                     #{demandRegion}, #{responseType}, #{loadDirection}, #{invitedUserCount},
                     #{aggregatorStatus}, #{deadlineTime}, #{marketCapacity}, #{publishStatus},
                     #{sourceRelease}, #{eventStatus}, #{publishTime}, #{createBy}, #{createTime},
                     #{updateBy}, #{updateTime}, #{delFlag}, #{sysUserId}
                 )
    </insert>

    <!-- 修改邀约计划 -->
    <update id="updateInvitation" parameterType="VppExchangeInvitation">
        UPDATE vpp_exchange_invitation
        SET
        invitation_name = #{invitationName},
        invitation_no = #{invitationNo},
        response_date = #{responseDate},
        demand_time_slots = #{demandTimeSlots},
        demand_region = #{demandRegion},
        response_type = #{responseType},
        load_direction = #{loadDirection},
        invited_user_count = #{invitedUserCount},
        aggregator_status = #{aggregatorStatus},
        deadline_time = #{deadlineTime},
        market_capacity = #{marketCapacity},
        publish_status = #{publishStatus},
        source_release = #{sourceRelease},
        event_status = #{eventStatus},
        publish_time = #{publishTime},
        update_by = #{updateBy},
        update_time = #{updateTime}
        WHERE invitation_id = #{invitationId}
        AND del_flag = '0' <!-- 逻辑删除过滤 -->
    </update>

    <!-- 批量删除邀约计划（根据ID数组） -->
    <delete id="deleteInvitationByIds" parameterType="Long[]">
        DELETE FROM vpp_exchange_invitation
        WHERE invitation_id IN (
        <foreach collection="array" item="invitationId" open="(" separator="," close=")">
            #{invitationId}
        </foreach>
        )
        AND del_flag = '0' <!-- 逻辑删除过滤 -->
    </delete>

    <!-- 批量新增邀约计划（导入用） -->
    <insert id="insertBatchInvitation" parameterType="List" useGeneratedKeys="true" keyProperty="invitationId">
        INSERT INTO vpp_exchange_invitation (
        invitation_name, invitation_no, response_date, demand_time_slots,
        demand_region, response_type, load_direction, invited_user_count,
        aggregator_status, deadline_time, market_capacity, publish_status,
        source_release, event_status, publish_time, create_by, create_time,
        update_by, update_time, del_flag, sys_user_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.invitationName}, #{item.invitationNo}, #{item.responseDate}, #{item.demandTimeSlots},
            #{item.demandRegion}, #{item.responseType}, #{item.loadDirection}, #{item.invitedUserCount},
            #{item.aggregatorStatus}, #{item.deadlineTime}, #{item.marketCapacity}, #{item.publishStatus},
            #{item.sourceRelease}, #{item.eventStatus}, #{item.publishTime}, #{item.createBy}, #{item.createTime},
            #{item.updateBy}, #{item.updateTime}, #{item.delFlag}, #{item.sysUserId}
            )
        </foreach>
    </insert>

    <!-- 自定义更新：发布邀约计划（更新状态和时间） -->
    <update id="updateById" parameterType="VppExchangeInvitation">
        UPDATE vpp_exchange_invitation
        SET
            publish_status = #{publishStatus},
            publish_time = #{publishTime}
        WHERE invitation_id = #{invitationId}
    </update>

    <!-- 查询邀约计划列表（带条件过滤） -->
    <select id="queryByNameOrNo" parameterType="Map" resultMap="invitationResultMap">
        SELECT * FROM vpp_exchange_invitation
        <where>
            <!-- 动态条件：邀约计划名称（模糊匹配） -->
            <if test="invitationName != null and invitationName.trim() != ''">
                AND invitation_name LIKE CONCAT('%', #{invitationName}, '%')
            </if>
            <!-- 动态条件：邀约计划名称（模糊匹配） -->
            <if test="invitationNo != null and invitationNo.trim() != ''">
                AND invitation_no LIKE CONCAT('%', #{invitationNo}, '%')
            </if>
            <!-- 聚合商ID -->
            <if test="userId != null">
                AND sys_user_id = #{userId}
            </if>
            <!-- 逻辑删除过滤（未删除数据） -->
            <if test="delFlag == null">
                AND del_flag = '0'
            </if>
        </where>
        ORDER BY create_time DESC <!-- 按创建时间倒序 -->
    </select>

    <!-- 更新锁单状态和时间 -->
    <update id="updateLockStatus" parameterType="com.vpp.dr.domain.VppExchangeInvitation">
        UPDATE vpp_exchange_invitation
        SET
            lock_status = #{lockStatus},
            lock_time = #{lockTime}
        WHERE invitation_id = #{invitationId}
    </update>
</mapper>