package com.vpp.web.controller.mgr;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.mgr.domain.VppMgrUag;
import com.vpp.mgr.service.IVppMgrUagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 聚合商用户-信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/mgr/uag")
@Api(tags = "聚合商用户-信息管理")
public class VppMgrUagController extends BaseController {
    @Autowired
    private IVppMgrUagService vppMgrUagService;

    /**
     * 查询聚合商用户-信息列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:uag:list')")
    @GetMapping("/list")
    @ApiOperation("查询聚合商用户-信息列表")
    public TableDataInfo list(VppMgrUag vppMgrUag) {
        startPage();
        List<VppMgrUag> list = vppMgrUagService.selectVppMgrUagList(vppMgrUag);
        return getDataTable(list);
    }

    /**
     * 导出聚合商用户-信息列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:uag:export')")
    // @Log(title = "聚合商用户-信息", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, VppMgrUag vppMgrUag) {
    //     List<VppMgrUag> list = vppMgrUagService.selectVppMgrUagList(vppMgrUag);
    //     ExcelUtil<VppMgrUag> util = new ExcelUtil<VppMgrUag>(VppMgrUag.class);
    //     util.exportExcel(response, list, "聚合商用户-信息数据");
    // }

    /**
     * 获取聚合商用户-信息详细信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:uag:query')")
    @GetMapping(value = "/{uagId}")
    @ApiOperation("获取聚合商用户-信息详细信息")
    public AjaxResult getInfo(@PathVariable("uagId") Long uagId) {
        return success(vppMgrUagService.selectVppMgrUagByUagId(uagId));
    }

    /**
     * 新增聚合商用户-信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:uag:add')")
    @Log(title = "聚合商用户-信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增聚合商用户-信息")
    public AjaxResult add(@RequestBody VppMgrUag vppMgrUag) {
        return toAjax(vppMgrUagService.insertVppMgrUag(vppMgrUag));
    }

    /**
     * 修改聚合商用户-信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:uag:edit')")
    @Log(title = "聚合商用户-信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改聚合商用户-信息")
    public AjaxResult edit(@RequestBody VppMgrUag vppMgrUag) {
        return toAjax(vppMgrUagService.updateVppMgrUag(vppMgrUag));
    }

    /**
     * 删除聚合商用户-信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:uag:remove')")
    @Log(title = "聚合商用户-信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{uagIds}")
    @ApiOperation("删除聚合商用户-信息")
    public AjaxResult remove(@PathVariable Long[] uagIds) {
        return toAjax(vppMgrUagService.deleteVppMgrUagByUagIds(uagIds));
    }


    //==========================================================================
    // 根据聚合商的用户id 查询所有的聚合用户列表(聚合商用户信息只能聚合商添加)
    // @PreAuthorize("@ss.hasPermi('mgr:uag:list')")
    // @GetMapping("/listByUserId/{userId}")
    // @ApiOperation("根据聚合商的用户id 查询所有的聚合用户列表")
    // public TableDataInfo listByUserId(@PathVariable("userId") Long userId) {
    //     startPage();
    //     List<VppMgrUag> list = vppMgrUagService.listByUserId(userId);
    //     return getDataTable(list);
    // }

    // 根据当前登录用户(聚合用户)的机构id查询聚合用户信息
    @GetMapping("/selectVppMgrUagByDeptId/{deptId}/{userId}")
    @ApiOperation("根据当前登录用户(聚合用户)的机构id查询聚合用户信息")
    public AjaxResult selectVppMgrUagByDeptId(@PathVariable("deptId") Long deptId,@PathVariable("userId")Long userId) {
        return success(vppMgrUagService.selectVppMgrUagByDeptId(deptId,userId));
    }

}