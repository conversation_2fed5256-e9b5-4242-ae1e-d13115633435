package com.vpp.execute.mapper;

import com.vpp.execute.domain.ExecuteDeviceCurrent;

import java.util.List;

/**
 * 设备实时检测电流Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ExecuteDeviceCurrentMapper {
    /**
     * 查询设备实时检测电流
     *
     * @param timestamp 设备实时检测电流主键
     * @return 设备实时检测电流
     */
    // public ExecuteDeviceCurrent selectExecuteDeviceCurrentByTimestamp(Date timestamp);

    /**
     * 查询设备实时检测电流列表
     *
     * @param executeDeviceCurrent 设备实时检测电流
     * @return 设备实时检测电流集合
     */
    public List<ExecuteDeviceCurrent> selectExecuteDeviceCurrentList(ExecuteDeviceCurrent executeDeviceCurrent);

    /**
     * 新增设备实时检测电流
     *
     * @param executeDeviceCurrent 设备实时检测电流
     * @return 结果
     */
    public int insertExecuteDeviceCurrent(ExecuteDeviceCurrent executeDeviceCurrent);

    /**
     * 修改设备实时检测电流
     *
     * @param executeDeviceCurrent 设备实时检测电流
     * @return 结果
     */
    public int updateExecuteDeviceCurrent(ExecuteDeviceCurrent executeDeviceCurrent);

    /**
     * 删除设备实时检测电流
     *
     * @param timestamp 设备实时检测电流主键
     * @return 结果
     */
    // public int deleteExecuteDeviceCurrentByTimestamp(Date timestamp);

    /**
     * 批量删除设备实时检测电流
     *
     * @param timestamps 需要删除的数据主键集合
     * @return 结果
     */
    // public int deleteExecuteDeviceCurrentByTimestamps(Date[] timestamps);
}
