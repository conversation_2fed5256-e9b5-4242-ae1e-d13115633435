package com.vpp.area.service.impl;

import com.vpp.area.domain.SysAddrArea;
import com.vpp.area.mapper.SysAddrAreaMapper;
import com.vpp.area.service.ISysAddrAreaService;
import com.vpp.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 地区设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class SysAddrAreaServiceImpl implements ISysAddrAreaService
{
    @Autowired
    private SysAddrAreaMapper sysAddrAreaMapper;

    /**
     * 查询地区设置
     *
     * @param id 地区设置主键
     * @return 地区设置
     */
    @Override
    public SysAddrArea selectSysAddrAreaById(String id)
    {
        return sysAddrAreaMapper.selectSysAddrAreaById(id);
    }

    /**
     * 查询地区设置列表
     *
     * @param sysAddrArea 地区设置
     * @return 地区设置
     */
    @Override
    public List<SysAddrArea> selectSysAddrAreaList(SysAddrArea sysAddrArea)
    {
        return sysAddrAreaMapper.selectSysAddrAreaList(sysAddrArea);
    }

    /**
     * 新增地区设置
     *
     * @param sysAddrArea 地区设置
     * @return 结果
     */
    @Override
    public int insertSysAddrArea(SysAddrArea sysAddrArea)
    {
        sysAddrArea.setCreateTime(DateUtils.getNowDate());
        return sysAddrAreaMapper.insertSysAddrArea(sysAddrArea);
    }

    /**
     * 修改地区设置
     *
     * @param sysAddrArea 地区设置
     * @return 结果
     */
    @Override
    public int updateSysAddrArea(SysAddrArea sysAddrArea)
    {
        sysAddrArea.setUpdateTime(DateUtils.getNowDate());
        return sysAddrAreaMapper.updateSysAddrArea(sysAddrArea);
    }

    /**
     * 批量删除地区设置
     *
     * @param ids 需要删除的地区设置主键
     * @return 结果
     */
    @Override
    public int deleteSysAddrAreaByIds(String[] ids)
    {
        return sysAddrAreaMapper.deleteSysAddrAreaByIds(ids);
    }

    /**
     * 删除地区设置信息
     *
     * @param id 地区设置主键
     * @return 结果
     */
    @Override
    public int deleteSysAddrAreaById(String id)
    {
        return sysAddrAreaMapper.deleteSysAddrAreaById(id);
    }
}