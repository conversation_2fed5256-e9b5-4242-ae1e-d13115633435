package com.vpp.execute.mapper;

import com.vpp.execute.domain.ExecuteDevicePower;

import java.util.Date;
import java.util.List;

/**
 * 设备实时检测功率Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ExecuteDevicePowerMapper {
    /**
     * 查询设备实时检测功率
     *
     * @param timestamp 设备实时检测功率主键
     * @return 设备实时检测功率
     */
    public ExecuteDevicePower selectExecuteDevicePowerByTimestamp(Date timestamp);

    /**
     * 查询设备实时检测功率列表
     *
     * @param executeDevicePower 设备实时检测功率
     * @return 设备实时检测功率集合
     */
    public List<ExecuteDevicePower> selectExecuteDevicePowerList(ExecuteDevicePower executeDevicePower);

    /**
     * 新增设备实时检测功率
     *
     * @param executeDevicePower 设备实时检测功率
     * @return 结果
     */
    public int insertExecuteDevicePower(ExecuteDevicePower executeDevicePower);

    /**
     * 修改设备实时检测功率
     *
     * @param executeDevicePower 设备实时检测功率
     * @return 结果
     */
    public int updateExecuteDevicePower(ExecuteDevicePower executeDevicePower);

    /**
     * 删除设备实时检测功率
     *
     * @param timestamp 设备实时检测功率主键
     * @return 结果
     */
    public int deleteExecuteDevicePowerByTimestamp(Date timestamp);

    /**
     * 批量删除设备实时检测功率
     *
     * @param timestamps 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExecuteDevicePowerByTimestamps(Date[] timestamps);
}
