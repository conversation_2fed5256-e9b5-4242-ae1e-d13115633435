package com.vpp.td.domain;

import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 聚合用户邀约实体类
 */
@Data
public class VppUserInvitation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 用户邀约主键ID */
    private Long userInvitationId;

    /** 关联邀约计划ID */
    private Long invitationId;

    /** 用户名称 */
    private String userName;

    /** 用户号 */
    private String userCode;

    /** 是否参加响应（枚举：是/否/待回复） */
    private String isParticipate;

    /** 用户报价（元/kWh） */
    private BigDecimal offerPrice;

    /** 响应量（kWh） */
    private BigDecimal responseQuantity;

    /** 最大可调节功率（kW） */
    private BigDecimal maxRegulatePower;

    /** 最后回复时间 */
    private Date lastReplyTime;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 最后更新时间 */
    private Date updateTime;

    /** 删除标志（0-未删除，2-已删除） */
    private String delFlag;
}
