package com.vpp.dr.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 邀约计划实体类（实现Serializable接口，支持序列化）
 */
@Data
@ApiModel(value = "邀约计划实体", description = "邀约计划表对应的实体类")
public class VppExchangeInvitation implements Serializable {

    private static final long serialVersionUID = 1L; // 序列化版本号

    @ApiModelProperty(value = "邀约计划主键ID", example = "1")
    private Long invitationId;

    @ApiModelProperty(value = "邀约计划名称（如：龙腾响应计划）", example = "龙腾响应计划-20250325")
    private String invitationName;

    @ApiModelProperty(value = "邀约计划编号（全局唯一，如：TX20240921001）", example = "TX20240921001")
    private String invitationNo;

    @ApiModelProperty(value = "响应执行日（格式：yyyy-MM-dd HH:mm:ss）", example = "2025-03-25 10:30:00")
    private String responseDate; // 数据库中为DATETIME，接口用String

    @ApiModelProperty(value = "需求时段（多时段逗号分隔，如：01:00,19:00）", example = "01:00,19:00")
    private String demandTimeSlots;

    @ApiModelProperty(value = "需求地区（省份/城市，如：陕西省）", example = "陕西省")
    private String demandRegion;

    @ApiModelProperty(value = "响应类型（枚举值：日前响应/日内响应）", example = "日前响应")
    private String responseType;

    @ApiModelProperty(value = "负荷方向（枚举值：填谷响应/削峰响应）", example = "削峰响应")
    private String loadDirection;

    @ApiModelProperty(value = "受邀用户数（总邀请数量，如：30）", example = "30")
    private Integer invitedUserCount;

    @ApiModelProperty(value = "邀约聚合用户数（实际参与聚合的用户数，如：25）", example = "25")
    private Integer aggregatedUserCount;

    @ApiModelProperty(value = "未回复用户数（被邀请但未确认的用户数，如：3）", example = "3")
    private Integer unrepliedUserCount;

    @ApiModelProperty(value = "不参与用户数（明确拒绝参与的用户数，如：2）", example = "2")
    private Integer nonParticipatingUserCount;

    @ApiModelProperty(value = "邀约截止时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2025-03-24 15:30:00")
    private String deadlineTime; // 数据库中为DATETIME，接口用String

    @ApiModelProperty(value = "市场邀约容量（单位：MW，如：600000.00）", example = "600000.00")
    private BigDecimal marketCapacity;

    @ApiModelProperty(value = "中标响应容量 (kW)", example = "580000.00")
    private BigDecimal bidResponseCapacity;

    @ApiModelProperty(value = "实际响应量（MW）", example = "575000.00")
    private BigDecimal actualResponseCapacity;

    @ApiModelProperty(value = "披露公示时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2025-03-19 15:30:00")
    private String disclosureTime; // 数据库中为DATETIME，接口用String

    @ApiModelProperty(value = "聚合商响应状态（枚举值：待命/运行/完成/失败）", example = "完成")
    private String aggregatorStatus;

    @ApiModelProperty(value = "活动状态（枚举值：未开始/已开始/已结束/已取消）", example = "已结束")
    private String activityStatus;

    @ApiModelProperty(value = "发布状态（枚举值：未发布/已发布）", example = "已发布")
    private String publishStatus;

    @ApiModelProperty(value = "发布时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2025-03-19 15:30:00")
    private String publishTime; // 数据库中为DATETIME，接口用String

    @ApiModelProperty(value = "发布来源（枚举值：邀约发布/交易中心）", example = "交易中心")
    private String sourceRelease;

    @ApiModelProperty(value = "事件状态（枚举值：未开始,已开始,已结束,已取消）", example = "已结束")
    private String eventStatus;

    @ApiModelProperty(value = "锁单状态（枚举值：未锁单,已锁单）", example = "已锁单")
    private String lockStatus;

    @ApiModelProperty(value = "锁单时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-09-20 10:00:00")
    private String lockTime; // 数据库中为DATETIME，接口用String

    @ApiModelProperty(value = "创建者（系统用户账号，如：admin）", example = "admin")
    private String createBy;

    @ApiModelProperty(value = "创建时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-09-20 10:00:00")
    private String createTime; // 数据库中为DATETIME，接口用String

    @ApiModelProperty(value = "更新者（系统用户账号）", example = "admin")
    private String updateBy;

    @ApiModelProperty(value = "最后更新时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2025-03-25 11:00:00")
    private String updateTime; // 数据库中为DATETIME，接口用String

    @ApiModelProperty(value = "删除标志（0-未删除，2-已删除）", example = "0")
    private String delFlag;

    @ApiModelProperty(value = "关联用户ID（创建人/更新人通用）", example = "103")
    private Long sysUserId;

    /**
     * 重写toString方法（方便打印对象信息）
     */
    @Override
    public String toString() {
        return "VppExchangeInvitation{" +
                "invitationId=" + invitationId +
                ", invitationName='" + invitationName + '\'' +
                ", invitationNo='" + invitationNo + '\'' +
                ", responseDate='" + responseDate + '\'' +
                ", demandTimeSlots='" + demandTimeSlots + '\'' +
                ", demandRegion='" + demandRegion + '\'' +
                ", responseType='" + responseType + '\'' +
                ", loadDirection='" + loadDirection + '\'' +
                ", invitedUserCount=" + invitedUserCount +
                ", aggregatedUserCount=" + aggregatedUserCount +
                ", unrepliedUserCount=" + unrepliedUserCount +
                ", nonParticipatingUserCount=" + nonParticipatingUserCount +
                ", deadlineTime='" + deadlineTime + '\'' +
                ", marketCapacity=" + marketCapacity +
                ", bidResponseCapacity=" + bidResponseCapacity +
                ", actualResponseCapacity=" + actualResponseCapacity +
                ", disclosureTime='" + disclosureTime + '\'' +
                ", aggregatorStatus='" + aggregatorStatus + '\'' +
                ", activityStatus='" + activityStatus + '\'' +
                ", publishStatus='" + publishStatus + '\'' +
                ", publishTime='" + publishTime + '\'' +
                ", sourceRelease='" + sourceRelease + '\'' +
                ", eventStatus='" + eventStatus + '\'' +
                ", lockStatus='" + lockStatus + '\'' +
                ", lockTime='" + lockTime + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createTime='" + createTime + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", sysUserId=" + sysUserId +
                '}';
    }
}