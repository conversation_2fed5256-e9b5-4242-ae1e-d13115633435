package com.vpp.dr.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 市场出清记录实体（对应界面"市场出清记录"列表页）
 */
@Data
public class VppMarketClearanceRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 清算交易唯一ID（防重） */
    private String transactionId;

    /** 关联发布配置表ID */
    private Long publishId;

    /** 实际清算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clearanceTime;

    /** 实时出清价格（元，保留2位小数） */
    private BigDecimal realCleared;

    /** 日前出清价格（元，保留2位小数） */
    private BigDecimal recentlyCleared;

    /** 货币类型（默认人民币） */
    private String currency;

    /** 清算状态（0=待清算，1=已清算，2=失败） */
    private Integer status;

    /** 备注信息 */
    private String remark;

    /** 记录创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}