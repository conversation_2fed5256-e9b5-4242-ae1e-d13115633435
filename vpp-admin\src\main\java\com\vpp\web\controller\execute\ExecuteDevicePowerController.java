package com.vpp.web.controller.execute;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.execute.domain.ExecuteDevicePower;
import com.vpp.execute.service.IExecuteDevicePowerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 设备实时检测功率Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/execute/devicePower")
public class ExecuteDevicePowerController extends BaseController {
    @Autowired
    private IExecuteDevicePowerService executeDevicePowerService;

    /**
     * 查询设备实时检测功率列表
     */
    @PreAuthorize("@ss.hasPermi('execute:devicePower:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExecuteDevicePower executeDevicePower) {
        startPage();
        List<ExecuteDevicePower> list = executeDevicePowerService.selectExecuteDevicePowerList(executeDevicePower);
        return getDataTable(list);
    }

    /**
     * 导出设备实时检测功率列表
     */
    @PreAuthorize("@ss.hasPermi('execute:devicePower:export')")
    @Log(title = "设备实时检测功率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExecuteDevicePower executeDevicePower) {
        List<ExecuteDevicePower> list = executeDevicePowerService.selectExecuteDevicePowerList(executeDevicePower);
        ExcelUtil<ExecuteDevicePower> util = new ExcelUtil<ExecuteDevicePower>(ExecuteDevicePower.class);
        util.exportExcel(response, list, "设备实时检测功率数据");
    }

    /**
     * 获取设备实时检测功率详细信息
     */
    @PreAuthorize("@ss.hasPermi('execute:devicePower:query')")
    @GetMapping(value = "/{timestamp}")
    public AjaxResult getInfo(@PathVariable("timestamp") Date timestamp) {
        return success(executeDevicePowerService.selectExecuteDevicePowerByTimestamp(timestamp));
    }

    /**
     * 新增设备实时检测功率
     */
    @PreAuthorize("@ss.hasPermi('execute:devicePower:add')")
    @Log(title = "设备实时检测功率", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExecuteDevicePower executeDevicePower) {
        return toAjax(executeDevicePowerService.insertExecuteDevicePower(executeDevicePower));
    }

    /**
     * 修改设备实时检测功率
     */
    @PreAuthorize("@ss.hasPermi('execute:devicePower:edit')")
    @Log(title = "设备实时检测功率", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExecuteDevicePower executeDevicePower) {
        return toAjax(executeDevicePowerService.updateExecuteDevicePower(executeDevicePower));
    }

    /**
     * 删除设备实时检测功率
     */
    @PreAuthorize("@ss.hasPermi('execute:devicePower:remove')")
    @Log(title = "设备实时检测功率", businessType = BusinessType.DELETE)
    @DeleteMapping("/{timestamps}")
    public AjaxResult remove(@PathVariable Date[] timestamps) {
        return toAjax(executeDevicePowerService.deleteExecuteDevicePowerByTimestamps(timestamps));
    }
}