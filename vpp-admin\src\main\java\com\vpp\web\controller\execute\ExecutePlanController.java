package com.vpp.web.controller.execute;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.execute.domain.ExecutePlan;
import com.vpp.execute.service.IExecutePlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 执行计划(中标后的执行计划)Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/execute/plan")
public class ExecutePlanController extends BaseController {
    @Autowired
    private IExecutePlanService executePlanService;

    /**
     * 查询执行计划，中标后的执行计划列表
     */
    @PreAuthorize("@ss.hasPermi('execute:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExecutePlan executePlan) {
        startPage();
        List<ExecutePlan> list = executePlanService.selectExecutePlanList(executePlan);
        return getDataTable(list);
    }

    /**
     * 导出执行计划，中标后的执行计划列表
     */
    @PreAuthorize("@ss.hasPermi('execute:plan:export')")
    @Log(title = "执行计划，中标后的执行计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExecutePlan executePlan) {
        List<ExecutePlan> list = executePlanService.selectExecutePlanList(executePlan);
        ExcelUtil<ExecutePlan> util = new ExcelUtil<ExecutePlan>(ExecutePlan.class);
        util.exportExcel(response, list, "执行计划，中标后的执行计划数据");
    }

    /**
     * 获取执行计划，中标后的执行计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('execute:plan:query')")
    @GetMapping(value = "/{executePlanId}")
    public AjaxResult getInfo(@PathVariable("executePlanId") Long executePlanId) {
        return success(executePlanService.selectExecutePlanByExecutePlanId(executePlanId));
    }

    /**
     * 新增执行计划，中标后的执行计划
     */
    @PreAuthorize("@ss.hasPermi('execute:plan:add')")
    @Log(title = "执行计划，中标后的执行计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExecutePlan executePlan) {
        return toAjax(executePlanService.insertExecutePlan(executePlan));
    }

    /**
     * 修改执行计划，中标后的执行计划
     */
    @PreAuthorize("@ss.hasPermi('execute:plan:edit')")
    @Log(title = "执行计划，中标后的执行计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExecutePlan executePlan) {
        return toAjax(executePlanService.updateExecutePlan(executePlan));
    }

    /**
     * 删除执行计划，中标后的执行计划
     */
    @PreAuthorize("@ss.hasPermi('execute:plan:remove')")
    @Log(title = "执行计划，中标后的执行计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{executePlanIds}")
    public AjaxResult remove(@PathVariable Long[] executePlanIds) {
        return toAjax(executePlanService.deleteExecutePlanByExecutePlanIds(executePlanIds));
    }
}