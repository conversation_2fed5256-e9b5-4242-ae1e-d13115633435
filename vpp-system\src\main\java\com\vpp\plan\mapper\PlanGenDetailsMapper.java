package com.vpp.plan.mapper;

import com.vpp.plan.domain.PlanGenDetails;

import java.util.List;

/**
 * 生成计划的详细信息，key-value形式Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface PlanGenDetailsMapper {
    /**
     * 查询生成计划的详细信息，key-value形式
     *
     * @param planGenDetailsId 生成计划的详细信息，key-value形式主键
     * @return 生成计划的详细信息，key-value形式
     */
    public PlanGenDetails selectPlanGenDetailsByPlanGenDetailsId(Long planGenDetailsId);

    /**
     * 查询生成计划的详细信息，key-value形式列表
     *
     * @param planGenDetails 生成计划的详细信息，key-value形式
     * @return 生成计划的详细信息，key-value形式集合
     */
    public List<PlanGenDetails> selectPlanGenDetailsList(PlanGenDetails planGenDetails);

    /**
     * 新增生成计划的详细信息，key-value形式
     *
     * @param planGenDetails 生成计划的详细信息，key-value形式
     * @return 结果
     */
    public int insertPlanGenDetails(PlanGenDetails planGenDetails);

    /**
     * 修改生成计划的详细信息，key-value形式
     *
     * @param planGenDetails 生成计划的详细信息，key-value形式
     * @return 结果
     */
    public int updatePlanGenDetails(PlanGenDetails planGenDetails);

    /**
     * 删除生成计划的详细信息，key-value形式
     *
     * @param planGenDetailsId 生成计划的详细信息，key-value形式主键
     * @return 结果
     */
    public int deletePlanGenDetailsByPlanGenDetailsId(Long planGenDetailsId);

    /**
     * 批量删除生成计划的详细信息，key-value形式
     *
     * @param planGenDetailsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanGenDetailsByPlanGenDetailsIds(Long[] planGenDetailsIds);
}
