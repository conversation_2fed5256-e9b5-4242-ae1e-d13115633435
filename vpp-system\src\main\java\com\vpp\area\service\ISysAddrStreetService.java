package com.vpp.area.service;

import com.vpp.area.domain.SysAddrStreet;

import java.util.List;

/**
 * 街道设置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface ISysAddrStreetService {
    /**
     * 查询街道设置
     *
     * @param id 街道设置主键
     * @return 街道设置
     */
    public SysAddrStreet selectSysAddrStreetById(Long id);

    /**
     * 查询街道设置列表
     *
     * @param sysAddrStreet 街道设置
     * @return 街道设置集合
     */
    public List<SysAddrStreet> selectSysAddrStreetList(SysAddrStreet sysAddrStreet);

    /**
     * 新增街道设置
     *
     * @param sysAddrStreet 街道设置
     * @return 结果
     */
    public int insertSysAddrStreet(SysAddrStreet sysAddrStreet);

    /**
     * 修改街道设置
     *
     * @param sysAddrStreet 街道设置
     * @return 结果
     */
    public int updateSysAddrStreet(SysAddrStreet sysAddrStreet);

    /**
     * 批量删除街道设置
     *
     * @param ids 需要删除的街道设置主键集合
     * @return 结果
     */
    public int deleteSysAddrStreetByIds(Long[] ids);

    /**
     * 删除街道设置信息
     *
     * @param id 街道设置主键
     * @return 结果
     */
    public int deleteSysAddrStreetById(Long id);
}
