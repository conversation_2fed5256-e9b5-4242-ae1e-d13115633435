<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.area.mapper.SysAddrProvinceMapper">

    <resultMap type="SysAddrProvince" id="SysAddrProvinceResult">
        <result property="id"    column="id"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="provinceName"    column="province_name"    />
        <result property="shortName"    column="short_name"    />
        <result property="lng"    column="lng"    />
        <result property="lat"    column="lat"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysAddrProvinceVo">
        select id, province_code, province_name, short_name, lng, lat, sort, create_time, update_time, create_by, update_by, remark from sys_addr_province
    </sql>

    <select id="selectSysAddrProvinceList" parameterType="SysAddrProvince" resultMap="SysAddrProvinceResult">
        <include refid="selectSysAddrProvinceVo"/>
        <where>
            <if test="provinceCode != null  and provinceCode != ''"> and province_code = #{provinceCode}</if>
            <if test="provinceName != null  and provinceName != ''"> and province_name like concat('%', #{provinceName}, '%')</if>
            <if test="shortName != null  and shortName != ''"> and short_name like concat('%', #{shortName}, '%')</if>
            <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectSysAddrProvinceById" parameterType="String" resultMap="SysAddrProvinceResult">
        <include refid="selectSysAddrProvinceVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysAddrProvince" parameterType="SysAddrProvince">
        insert into sys_addr_province
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="provinceCode != null and provinceCode != ''">province_code,</if>
            <if test="provinceName != null and provinceName != ''">province_name,</if>
            <if test="shortName != null and shortName != ''">short_name,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="provinceCode != null and provinceCode != ''">#{provinceCode},</if>
            <if test="provinceName != null and provinceName != ''">#{provinceName},</if>
            <if test="shortName != null and shortName != ''">#{shortName},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSysAddrProvince" parameterType="SysAddrProvince">
        update sys_addr_province
        <trim prefix="SET" suffixOverrides=",">
            <if test="provinceCode != null and provinceCode != ''">province_code = #{provinceCode},</if>
            <if test="provinceName != null and provinceName != ''">province_name = #{provinceName},</if>
            <if test="shortName != null and shortName != ''">short_name = #{shortName},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAddrProvinceById" parameterType="String">
        delete from sys_addr_province where id = #{id}
    </delete>

    <delete id="deleteSysAddrProvinceByIds" parameterType="String">
        delete from sys_addr_province where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>