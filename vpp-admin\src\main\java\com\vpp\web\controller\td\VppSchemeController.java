package com.vpp.web.controller.td;

import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.td.domain.VppInvitationScheme;
import com.vpp.td.service.ISchemeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/trade/scheme")
@Api(tags = "方案信息", description = "邀约方案信息管理接口")
public class VppSchemeController extends BaseController {

    @Autowired
    private ISchemeService schemeService;

    /**
     * 新建邀约方案
     */
    @PostMapping
    @ApiOperation("新建邀约方案")
    public AjaxResult addScheme(@RequestBody VppInvitationScheme scheme) {
        boolean success = schemeService.saveScheme(scheme);
        return success ? AjaxResult.success("新建成功") : AjaxResult.error("新建失败");
    }

    /**
     * 查询方案列表（关联邀约计划）
     */
    @GetMapping("/list")
    @ApiOperation("查询方案列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invitationId", value = "邀约计划ID", dataType = "long"),
            @ApiImplicitParam(name = "schemeName", value = "方案名称", dataType = "string"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", dataType = "integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", dataType = "integer", defaultValue = "10")
    })
    public TableDataInfo listScheme(
            @RequestParam(required = false) Long invitationId,
            @RequestParam(required = false) String schemeName,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {

        Map<String, Object> params = new HashMap<>();
        params.put("invitationId", invitationId);
        params.put("schemeName", schemeName);

        List<VppInvitationScheme> list = schemeService.getSchemeList(params, pageNum, pageSize);
        return getDataTable(list);
    }

    /**
     * 修改方案的最优状态
     */
    @PutMapping("/optimal")
    @ApiOperation("修改方案的最优状态")
    public AjaxResult updateIsOptimal(
            @RequestBody Map<String, Object> params) {
        try {
            // 参数校验
            if (!params.containsKey("schemeId") || !params.containsKey("isOptimal")) {
                return AjaxResult.error("参数缺失：schemeId 或 isOptimal");
            }
            Long schemeId = Long.valueOf(params.get("schemeId").toString());
            Integer isOptimal = Integer.valueOf(params.get("isOptimal").toString());

            // 调用Service更新
            VppInvitationScheme updatedScheme = schemeService.updateIsOptimal(schemeId, isOptimal);
            return success(updatedScheme);
        } catch (NumberFormatException e) {
            return AjaxResult.error("参数格式错误：schemeId 或 isOptimal 必须为数字");
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("参数错误：" + e.getMessage());
        } catch (RuntimeException e) {
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }
}