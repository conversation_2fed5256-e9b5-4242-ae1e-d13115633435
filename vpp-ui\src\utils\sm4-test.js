// SM4加密测试文件
import { encrypt, decrypt } from './jsencrypt'

// 测试SM4加密解密功能
export function testSM4Encryption() {
  console.log('=== SM4加密测试开始 ===')

  const testPasswords = [
    'admin123',
    'password',
    '123456',
    'test@123'
  ]

  let allTestsPassed = true

  testPasswords.forEach((password, index) => {
    console.log(`\n测试 ${index + 1}: 原始密码 = "${password}"`)

    try {
      // 加密
      const encrypted = encrypt(password)
      console.log(`加密后 = "${encrypted}"`)

      if (!encrypted) {
        console.error(`❌ 加密失败: 返回空字符串`)
        allTestsPassed = false
        return
      }

      // 解密
      const decrypted = decrypt(encrypted)
      console.log(`解密后 = "${decrypted}"`)

      // 验证
      const isValid = password === decrypted
      console.log(`验证结果: ${isValid ? '✓ 通过' : '✗ 失败'}`)

      if (!isValid) {
        console.error(`❌ 测试失败: 原始密码 "${password}" 与解密后密码 "${decrypted}" 不匹配`)
        allTestsPassed = false
      }
    } catch (error) {
      console.error(`❌ 测试异常:`, error)
      allTestsPassed = false
    }
  })

  console.log(`\n=== SM4加密测试结束 ===`)
  console.log(`总体结果: ${allTestsPassed ? '✓ 所有测试通过' : '✗ 部分测试失败'}`)

  return allTestsPassed
}

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  window.testSM4 = testSM4Encryption
  console.log('SM4测试函数已加载，请在控制台运行 testSM4() 进行测试')
}
