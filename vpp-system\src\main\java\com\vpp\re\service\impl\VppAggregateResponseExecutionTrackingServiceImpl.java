package com.vpp.re.service.impl;

import com.vpp.re.domain.VppBasicInformation;
import com.vpp.re.domain.VppExchangeOperationOverview;
import com.vpp.re.domain.VppOperationMonitoring;
import com.vpp.re.mapper.VppAggregateResponseExecutionTrackingMapper;
import com.vpp.re.service.IVppAggregateResponseExecutionTrackingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class VppAggregateResponseExecutionTrackingServiceImpl implements IVppAggregateResponseExecutionTrackingService {

    @Autowired
    private VppAggregateResponseExecutionTrackingMapper mapper;

    /**
     * 根据邀约计划ID查询运行概览列表（分页）
     */
    @Override
    public VppExchangeOperationOverview getByInvitationId(Long invitationId) {
        // 参数校验
        if (invitationId == null || invitationId <= 0) {
            throw new IllegalArgumentException("邀约计划ID不能为空或无效");
        }

        // 构造查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("invitationId", invitationId);

        return mapper.selectByInvitationId(params);
    }

    /**
     * 根据邀约计划ID查询基本信息
     */
    @Override
    public VppBasicInformation selectBasicInfoByInvitationId(Long invitationId) {
        // 参数校验
        if (invitationId == null || invitationId <= 0) {
            throw new IllegalArgumentException("邀约计划ID不能为空或无效");
        }

        // 构造查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("invitationId", invitationId);

        return mapper.selectBasicInfoByInvitationId(params);
    }

    /**
     * 根据邀约计划ID查询运行监控表
     */
    @Override
    public VppOperationMonitoring selectOperationByInvitationId(Long invitationId) {
        // 参数校验
        if (invitationId == null || invitationId <= 0) {
            throw new IllegalArgumentException("邀约计划ID不能为空或无效");
        }

        // 构造查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("invitationId", invitationId);

        return mapper.selectOperationByInvitationId(params);
    }
}
