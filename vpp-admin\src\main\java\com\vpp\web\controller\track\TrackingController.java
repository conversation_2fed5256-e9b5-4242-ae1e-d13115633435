package com.vpp.web.controller.track;

import com.vpp.common.core.domain.AjaxResult;
import com.vpp.tracking.service.TrackingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/tracking")
@Api(tags = "响应执行-聚合响应执行跟踪-事件追踪", description = "用户结算接口")
public class TrackingController {
    @Autowired
    TrackingService service;
    /**
     * 获取状态为运行中，非开始，结束阶段的事件
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "列出所有可用的事件",tags = {"响应执行-聚合响应执行跟踪-事件追踪"})
    public AjaxResult list(){
        return service.listNeedTrack();
    }
    //爬坡率
    @GetMapping("/overview/climb/{event_id}")
    @ApiOperation(value = "爬坡率",tags = {"响应执行-聚合响应执行跟踪-事件追踪","运行概览"})
    public AjaxResult climb(@PathVariable("event_id") Long event_id){
        return service.climb(event_id);
    }
    @GetMapping("/overview/completion-rate/{event_id}")
    @ApiOperation(value = "响应完成率",tags = {"响应执行-聚合响应执行跟踪-事件追踪","运行概览"})
    //响应完成率
    public AjaxResult completionRate(@PathVariable("event_id")Long event_id){
        return service.completionRate(event_id);
    }
}
