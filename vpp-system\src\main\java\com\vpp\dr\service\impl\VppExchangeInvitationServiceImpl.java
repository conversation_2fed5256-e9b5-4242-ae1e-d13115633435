package com.vpp.dr.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.common.utils.StringUtils;
import com.vpp.dr.domain.VppExchangeInvitation;
import com.vpp.dr.mapper.VppExchangeInvitationMapper;
import com.vpp.dr.service.IVppExchangeInvitationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 邀约计划服务实现类
 */
@Service
public class VppExchangeInvitationServiceImpl implements IVppExchangeInvitationService {

    @Autowired
    private VppExchangeInvitationMapper invitationMapper;

    @Override
    public List<VppExchangeInvitation> getInvitationList(Map<String, Object> params) {
        return invitationMapper.selectInvitationList(params);
    }

    @Override
    public VppExchangeInvitation getInvitationById(Long invitationId) {
        return invitationMapper.selectInvitationById(invitationId);
    }

    @Override
    @Transactional
    public int insertInvitation(VppExchangeInvitation invitation) {
        return invitationMapper.insertInvitation(invitation);
    }

    @Override
    @Transactional
    public int updateInvitation(VppExchangeInvitation invitation) {
        return invitationMapper.updateInvitation(invitation);
    }

    @Override
    @Transactional
    public int deleteInvitationByIds(Long[] invitationIds) {
        return invitationMapper.deleteInvitationByIds(invitationIds);
    }

    @Override
    @Transactional
    public int insertBatchInvitation(List<VppExchangeInvitation> invitations) {
        return invitationMapper.insertBatchInvitation(invitations);
    }

    /**
     * 发布邀约计划（更新状态为已发布）
     * @param invitationId 邀约计划ID（主键）
     * @return 是否发布成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class) // 声明式事务
    public boolean publishInvitation(Long invitationId) {
        // 1. 查询邀约计划是否存在
        VppExchangeInvitation invitation = invitationMapper.selectInvitationById(invitationId);
        if (invitation == null) {
            return false; // 邀约不存在，返回失败
        }

        // 2. 校验当前状态是否为未发布（0=未发布）
        if (!"未发布".equals(invitation.getPublishStatus())) {
            return false; // 状态异常，返回失败
        }

        // 3. 更新为已发布状态，并设置发布时间
        invitation.setPublishStatus("已发布"); // 已发布
        invitation.setPublishTime(DateUtils.getTime()); // 当前时间作为发布时间

        // 4. 执行更新操作
        return invitationMapper.updateById(invitation) > 0;
    }

    /**
     * 按名称或编号查询邀约计划
     * @param params 查询参数（邀约计划名称、邀约计划编号、聚合商ID）
     * @return 邀约计划集合
     */
    @Override
    public List<VppExchangeInvitation> queryByNameOrNo(Map<String, Object> params) {
        return invitationMapper.queryByNameOrNo(params);
    }

    /**
     * 更新锁单状态和锁单时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VppExchangeInvitation updateLockStatus(Long invitationId, String lockStatus) {
        // 参数校验
        if (!StringUtils.hasText(lockStatus)) {
            throw new IllegalArgumentException("锁单状态不能为空");
        }
        if (!"未锁单".equals(lockStatus) && !"已锁单".equals(lockStatus)) {
            throw new IllegalArgumentException("锁单状态枚举值错误（仅支持：未锁单/已锁单）");
        }

        // 查询邀约计划是否存在
        VppExchangeInvitation invitation = invitationMapper.selectInvitationById(invitationId);
        if (invitation == null) {
            throw new RuntimeException("邀约计划不存在（ID：" + invitationId + "）");
        }

        // 更新锁单状态和时间
        invitation.setLockStatus(lockStatus);
        invitation.setLockTime(DateUtils.getTime()); // 系统自动生成锁单时间

        // 执行更新
        int rows = invitationMapper.updateLockStatus(invitation);
        if (rows == 0) {
            throw new RuntimeException("更新锁单状态失败（可能数据已变更）");
        }

        return invitation;
    }
}