package com.vpp.td.domain;

import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户调节能力实体类
 */
@Data
public class VppUserCapacity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 调节能力主键ID */
    private Long capacityId;

    /** 关联邀约计划ID */
    private Long invitationId;

    /** 用户号 */
    private String userCode;

    /** 日期 */
    private Date date;

    /** 基线（kW） */
    private BigDecimal baseline;

    /** 向上调节能力（kW） */
    private BigDecimal upRegulatePower;

    /** 向下调节能力（kW） */
    private BigDecimal downRegulatePower;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 最后更新时间 */
    private Date updateTime;

    /** 删除标志（0-未删除，2-已删除） */
    private String delFlag;
}
