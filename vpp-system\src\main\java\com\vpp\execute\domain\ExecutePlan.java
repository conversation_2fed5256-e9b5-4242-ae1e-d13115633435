package com.vpp.execute.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 执行计划(中标后的执行计划对象) execute_plan
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public class ExecutePlan extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long executePlanId;

    /**
     * 关联生成的计划表
     */
    @Excel(name = "关联生成的计划表")
    private Long executePlanGenId;

    /**
     * 活动编号
     */
    @Excel(name = "活动编号")
    private String activityCode;

    /**
     * 虚拟电厂id,
     */
    @Excel(name = "虚拟电厂id,")
    private Long vppId;

    /**
     * 设备总数
     */
    @Excel(name = "设备总数")
    private Long devicesTotal;

    /**
     * 计划额定总功率
     */
    @Excel(name = "计划额定总功率")
    private Long ratedPower;

    /**
     * 计划总电量
     */
    @Excel(name = "计划总电量")
    private Long ratedElectricity;

    /**
     * 0,未执行，1正在执行，2结束
     */
    @Excel(name = "0,未执行，1正在执行，2结束")
    private String executeStatus;

    public void setExecutePlanId(Long executePlanId) {
        this.executePlanId = executePlanId;
    }

    public Long getExecutePlanId() {
        return executePlanId;
    }

    public void setExecutePlanGenId(Long executePlanGenId) {
        this.executePlanGenId = executePlanGenId;
    }

    public Long getExecutePlanGenId() {
        return executePlanGenId;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setVppId(Long vppId) {
        this.vppId = vppId;
    }

    public Long getVppId() {
        return vppId;
    }

    public void setDevicesTotal(Long devicesTotal) {
        this.devicesTotal = devicesTotal;
    }

    public Long getDevicesTotal() {
        return devicesTotal;
    }

    public void setRatedPower(Long ratedPower) {
        this.ratedPower = ratedPower;
    }

    public Long getRatedPower() {
        return ratedPower;
    }

    public void setRatedElectricity(Long ratedElectricity) {
        this.ratedElectricity = ratedElectricity;
    }

    public Long getRatedElectricity() {
        return ratedElectricity;
    }

    public void setExecuteStatus(String executeStatus) {
        this.executeStatus = executeStatus;
    }

    public String getExecuteStatus() {
        return executeStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("executePlanId", getExecutePlanId())
                .append("executePlanGenId", getExecutePlanGenId())
                .append("activityCode", getActivityCode())
                .append("vppId", getVppId())
                .append("devicesTotal", getDevicesTotal())
                .append("ratedPower", getRatedPower())
                .append("ratedElectricity", getRatedElectricity())
                .append("executeStatus", getExecuteStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}