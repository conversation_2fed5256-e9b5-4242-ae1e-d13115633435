package com.vpp.area.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 地区设置对象 sys_addr_area
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public class SysAddrArea extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String id;

    /**
     * 区代码
     */
    @Excel(name = "区代码")
    private String areaCode;

    /**
     * 父级市代码
     */
    @Excel(name = "父级市代码")
    private String cityCode;

    /**
     * 市名称
     */
    @Excel(name = "市名称")
    private String areaName;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String shortName;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String lng;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String lat;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long sort;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getShortName() {
        return shortName;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLng() {
        return lng;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLat() {
        return lat;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public Long getSort() {
        return sort;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("areaCode", getAreaCode())
                .append("cityCode", getCityCode())
                .append("areaName", getAreaName())
                .append("shortName", getShortName())
                .append("lng", getLng())
                .append("lat", getLat())
                .append("sort", getSort())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("remark", getRemark())
                .toString();
    }
}