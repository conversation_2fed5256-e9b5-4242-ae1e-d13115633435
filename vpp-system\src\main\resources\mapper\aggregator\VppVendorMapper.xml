<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.aggregator.mapper.VppVendorMapper">

    <resultMap type="VppVendor" id="VppVendorResult">
        <result property="vendorId"    column="vendor_id"    />
        <result property="vendorCode"    column="vendor_code"    />
        <result property="vendorName"    column="vendor_name"    />
        <result property="vendorType"    column="vendor_type"    />
        <result property="vendorType2"    column="vendor_type2"    />
        <result property="vendorStatus"    column="vendor_status"    />
        <result property="vendorCorporationCode"    column="vendor_corporation_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVppVendorVo">
        select vendor_id, vendor_code, vendor_name, vendor_type, vendor_type2, vendor_status, vendor_corporation_code, create_by, create_time, update_by, update_time, remark from vpp_vendor
    </sql>

    <select id="selectVppVendorList" parameterType="VppVendor" resultMap="VppVendorResult">
        <include refid="selectVppVendorVo"/>
        <where>
            <if test="vendorCode != null  and vendorCode != ''"> and vendor_code = #{vendorCode}</if>
            <if test="vendorName != null  and vendorName != ''"> and vendor_name like concat('%', #{vendorName}, '%')</if>
            <if test="vendorType != null "> and vendor_type = #{vendorType}</if>
            <if test="vendorType2 != null "> and vendor_type2 = #{vendorType2}</if>
            <if test="vendorStatus != null "> and vendor_status = #{vendorStatus}</if>
            <if test="vendorCorporationCode != null  and vendorCorporationCode != ''"> and vendor_corporation_code = #{vendorCorporationCode}</if>
        </where>
    </select>

    <select id="selectVppVendorByVendorId" parameterType="Long" resultMap="VppVendorResult">
        <include refid="selectVppVendorVo"/>
        where vendor_id = #{vendorId}
    </select>

    <insert id="insertVppVendor" parameterType="VppVendor" useGeneratedKeys="true" keyProperty="vendorId">
        insert into vpp_vendor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vendorCode != null">vendor_code,</if>
            <if test="vendorName != null">vendor_name,</if>
            <if test="vendorType != null">vendor_type,</if>
            <if test="vendorType2 != null">vendor_type2,</if>
            <if test="vendorStatus != null">vendor_status,</if>
            <if test="vendorCorporationCode != null">vendor_corporation_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vendorCode != null">#{vendorCode},</if>
            <if test="vendorName != null">#{vendorName},</if>
            <if test="vendorType != null">#{vendorType},</if>
            <if test="vendorType2 != null">#{vendorType2},</if>
            <if test="vendorStatus != null">#{vendorStatus},</if>
            <if test="vendorCorporationCode != null">#{vendorCorporationCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVppVendor" parameterType="VppVendor">
        update vpp_vendor
        <trim prefix="SET" suffixOverrides=",">
            <if test="vendorCode != null">vendor_code = #{vendorCode},</if>
            <if test="vendorName != null">vendor_name = #{vendorName},</if>
            <if test="vendorType != null">vendor_type = #{vendorType},</if>
            <if test="vendorType2 != null">vendor_type2 = #{vendorType2},</if>
            <if test="vendorStatus != null">vendor_status = #{vendorStatus},</if>
            <if test="vendorCorporationCode != null">vendor_corporation_code = #{vendorCorporationCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where vendor_id = #{vendorId}
    </update>

    <delete id="deleteVppVendorByVendorId" parameterType="Long">
        delete from vpp_vendor where vendor_id = #{vendorId}
    </delete>

    <delete id="deleteVppVendorByVendorIds" parameterType="String">
        delete from vpp_vendor where vendor_id in
        <foreach item="vendorId" collection="array" open="(" separator="," close=")">
            #{vendorId}
        </foreach>
    </delete>
</mapper>