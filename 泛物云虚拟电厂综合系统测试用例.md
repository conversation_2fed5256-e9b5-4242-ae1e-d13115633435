# 泛物云虚拟电厂综合系统测试用例

## 1. 测试概述

### 1.1 测试目的
对泛物云虚拟电厂综合系统进行全面的功能测试，确保系统各模块功能正常，满足业务需求。

### 1.2 测试范围
- 用户管理模块
- 聚合商管理模块
- 设备管理模块
- 虚拟电厂管理模块
- 市场交易模块
- 需求响应模块
- 系统安全性测试

### 1.3 测试环境
- 操作系统：Windows/Linux
- 数据库：MySQL 8.0
- 应用服务器：Spring Boot 2.5.15
- 前端框架：Vue.js + Element UI
- 浏览器：Chrome、Firefox、Edge

## 2. 功能测试用例

### 2.1 用户管理模块

#### 测试用例001：用户登录功能
- **用例编号**：TC_USER_001
- **测试目标**：验证用户登录功能
- **前置条件**：系统已部署，数据库中存在测试用户
- **测试步骤**：
  1. 打开系统登录页面
  2. 输入正确的用户名和密码
  3. 输入验证码
  4. 点击登录按钮
- **预期结果**：登录成功，跳转到系统主页
- **测试数据**：用户名：admin，密码：admin123

#### 测试用例002：用户登录失败
- **用例编号**：TC_USER_002
- **测试目标**：验证错误密码登录失败
- **前置条件**：系统已部署
- **测试步骤**：
  1. 打开系统登录页面
  2. 输入正确的用户名和错误密码
  3. 点击登录按钮
- **预期结果**：登录失败，显示错误提示信息
- **测试数据**：用户名：admin，密码：wrongpassword

#### 测试用例003：用户权限验证
- **用例编号**：TC_USER_003
- **测试目标**：验证用户权限控制
- **前置条件**：已登录普通用户
- **测试步骤**：
  1. 尝试访问管理员专用功能
  2. 检查系统响应
- **预期结果**：显示权限不足提示，无法访问

### 2.2 聚合商管理模块

#### 测试用例004：新增聚合商
- **用例编号**：TC_AGG_001
- **测试目标**：验证新增聚合商功能
- **前置条件**：已登录管理员账户
- **测试步骤**：
  1. 进入聚合商管理页面
  2. 点击新增按钮
  3. 填写聚合商基本信息
  4. 点击保存
- **预期结果**：聚合商创建成功，列表中显示新增记录
- **测试数据**：
  - 聚合商名称：测试聚合商A
  - 聚合商编码：AGG001
  - 联系人：张三
  - 手机号码：13800138000

#### 测试用例005：聚合商信息修改
- **用例编号**：TC_AGG_002
- **测试目标**：验证聚合商信息修改功能
- **前置条件**：系统中已存在聚合商记录
- **测试步骤**：
  1. 在聚合商列表中选择一条记录
  2. 点击编辑按钮
  3. 修改聚合商信息
  4. 点击保存
- **预期结果**：修改成功，列表显示更新后的信息

#### 测试用例006：聚合商删除
- **用例编号**：TC_AGG_003
- **测试目标**：验证聚合商删除功能
- **前置条件**：系统中已存在聚合商记录
- **测试步骤**：
  1. 在聚合商列表中选择一条记录
  2. 点击删除按钮
  3. 确认删除操作
- **预期结果**：删除成功，记录从列表中移除

### 2.3 设备管理模块

#### 测试用例007：设备注册
- **用例编号**：TC_DEV_001
- **测试目标**：验证设备注册功能
- **前置条件**：已登录系统
- **测试步骤**：
  1. 进入设备管理页面
  2. 点击新增设备
  3. 填写设备信息
  4. 选择设备类型
  5. 点击保存
- **预期结果**：设备注册成功，设备列表中显示新设备
- **测试数据**：
  - 设备名称：储能设备001
  - 设备类型：储能设备
  - 额定功率：100kW
  - 额定电压：380V

#### 测试用例008：设备状态监控
- **用例编号**：TC_DEV_002
- **测试目标**：验证设备状态监控功能
- **前置条件**：系统中已注册设备
- **测试步骤**：
  1. 进入设备监控页面
  2. 查看设备实时状态
  3. 检查设备运行参数
- **预期结果**：能够正确显示设备状态和运行参数

#### 测试用例009：设备控制
- **用例编号**：TC_DEV_003
- **测试目标**：验证设备控制功能
- **前置条件**：系统中已注册可控设备
- **测试步骤**：
  1. 选择可控设备
  2. 发送控制指令
  3. 观察设备响应
- **预期结果**：设备按照指令执行相应操作

### 2.4 虚拟电厂管理模块

#### 测试用例010：虚拟电厂创建
- **用例编号**：TC_VPP_001
- **测试目标**：验证虚拟电厂创建功能
- **前置条件**：已登录管理员账户
- **测试步骤**：
  1. 进入虚拟电厂管理页面
  2. 点击新建虚拟电厂
  3. 填写虚拟电厂信息
  4. 选择虚拟电厂类型
  5. 点击保存
- **预期结果**：虚拟电厂创建成功
- **测试数据**：
  - 虚拟电厂名称：测试VPP001
  - 虚拟电厂类型：综合型
  - 运营商编码：VPP001

#### 测试用例011：虚拟电厂资源配置
- **用例编号**：TC_VPP_002
- **测试目标**：验证虚拟电厂资源配置功能
- **前置条件**：已创建虚拟电厂，系统中存在设备资源
- **测试步骤**：
  1. 选择虚拟电厂
  2. 进入资源配置页面
  3. 添加设备资源
  4. 配置资源参数
  5. 保存配置
- **预期结果**：资源配置成功，虚拟电厂包含指定资源

### 2.5 市场交易模块

#### 测试用例012：邀约计划创建
- **用例编号**：TC_MKT_001
- **测试目标**：验证邀约计划创建功能
- **前置条件**：已登录系统
- **测试步骤**：
  1. 进入市场交易页面
  2. 点击创建邀约计划
  3. 填写邀约信息
  4. 设置需求时段
  5. 选择邀约用户
  6. 保存并发布
- **预期结果**：邀约计划创建成功并发布
- **测试数据**：
  - 邀约名称：夏季削峰响应
  - 响应类型：日前响应
  - 负荷方向：削峰响应
  - 需求时段：14:00-16:00

#### 测试用例013：响应申报
- **用例编号**：TC_MKT_002
- **测试目标**：验证响应申报功能
- **前置条件**：已发布邀约计划
- **测试步骤**：
  1. 聚合商登录系统
  2. 查看邀约计划
  3. 填写响应申报
  4. 提交申报
- **预期结果**：响应申报提交成功

#### 测试用例014：市场出清
- **用例编号**：TC_MKT_003
- **测试目标**：验证市场出清功能
- **前置条件**：已收到响应申报
- **测试步骤**：
  1. 进入市场出清页面
  2. 查看申报情况
  3. 执行出清计算
  4. 发布出清结果
- **预期结果**：出清计算正确，结果发布成功

### 2.6 需求响应模块

#### 测试用例015：需求响应执行
- **用例编号**：TC_DR_001
- **测试目标**：验证需求响应执行功能
- **前置条件**：已完成市场出清，进入执行阶段
- **测试步骤**：
  1. 系统自动启动需求响应
  2. 监控用户响应情况
  3. 记录实际响应数据
  4. 计算响应效果
- **预期结果**：需求响应正常执行，数据记录完整

#### 测试用例016：响应效果评估
- **用例编号**：TC_DR_002
- **测试目标**：验证响应效果评估功能
- **前置条件**：需求响应执行完成
- **测试步骤**：
  1. 进入效果评估页面
  2. 查看响应数据统计
  3. 生成评估报告
- **预期结果**：评估报告生成正确，数据准确

## 3. 安全性测试用例

### 3.1 SQL注入测试

#### 测试用例017：SQL注入防护
- **用例编号**：TC_SEC_001
- **测试目标**：验证系统SQL注入防护
- **测试步骤**：
  1. 在登录框输入SQL注入代码
  2. 在查询框输入恶意SQL语句
  3. 观察系统响应
- **预期结果**：系统能够有效防护SQL注入攻击

### 3.2 XSS攻击测试

#### 测试用例018：XSS防护
- **用例编号**：TC_SEC_002
- **测试目标**：验证系统XSS攻击防护
- **测试步骤**：
  1. 在输入框输入XSS脚本
  2. 提交表单
  3. 查看页面显示
- **预期结果**：恶意脚本被过滤，不会执行

### 3.3 权限控制测试

#### 测试用例019：越权访问防护
- **用例编号**：TC_SEC_003
- **测试目标**：验证权限控制机制
- **测试步骤**：
  1. 使用普通用户账户
  2. 尝试访问管理员功能URL
  3. 检查系统响应
- **预期结果**：系统拒绝访问，返回权限不足提示

## 4. 性能测试用例

### 4.1 并发用户测试

#### 测试用例020：并发登录测试
- **用例编号**：TC_PERF_001
- **测试目标**：验证系统并发处理能力
- **测试步骤**：
  1. 模拟100个用户同时登录
  2. 监控系统响应时间
  3. 检查系统稳定性
- **预期结果**：系统能够正常处理并发请求，响应时间在可接受范围内

### 4.2 数据处理性能测试

#### 测试用例021：大数据量查询测试
- **用例编号**：TC_PERF_002
- **测试目标**：验证大数据量查询性能
- **测试步骤**：
  1. 在数据库中插入大量测试数据
  2. 执行复杂查询操作
  3. 监控查询响应时间
- **预期结果**：查询响应时间在3秒以内

## 5. 兼容性测试用例

### 5.1 浏览器兼容性测试

#### 测试用例022：多浏览器兼容性
- **用例编号**：TC_COMP_001
- **测试目标**：验证系统在不同浏览器下的兼容性
- **测试步骤**：
  1. 分别在Chrome、Firefox、Edge浏览器中打开系统
  2. 执行主要功能操作
  3. 检查页面显示和功能正常性
- **预期结果**：系统在各浏览器中均能正常运行

## 6. 测试总结

本测试用例覆盖了泛物云虚拟电厂综合系统的主要功能模块，包括用户管理、聚合商管理、设备管理、虚拟电厂管理、市场交易、需求响应等核心业务功能，以及安全性、性能和兼容性测试。通过执行这些测试用例，可以全面验证系统的功能完整性、安全性和稳定性。
