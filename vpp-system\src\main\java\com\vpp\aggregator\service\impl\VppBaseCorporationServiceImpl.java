package com.vpp.aggregator.service.impl;

import com.vpp.aggregator.domain.VppBaseCorporation;
import com.vpp.aggregator.mapper.VppBaseCorporationMapper;
import com.vpp.aggregator.service.IVppBaseCorporationService;
import com.vpp.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公司主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class VppBaseCorporationServiceImpl implements IVppBaseCorporationService {
    @Autowired
    private VppBaseCorporationMapper vppBaseCorporationMapper;

    /**
     * 查询公司主体
     *
     * @param corporationId 公司主体主键
     * @return 公司主体
     */
    @Override
    public VppBaseCorporation selectVppBaseCorporationByCorporationId(Long corporationId) {
        return vppBaseCorporationMapper.selectVppBaseCorporationByCorporationId(corporationId);
    }

    /**
     * 查询公司主体列表
     *
     * @param vppBaseCorporation 公司主体
     * @return 公司主体
     */
    @Override
    public List<VppBaseCorporation> selectVppBaseCorporationList(VppBaseCorporation vppBaseCorporation) {
        return vppBaseCorporationMapper.selectVppBaseCorporationList(vppBaseCorporation);
    }

    /**
     * 新增公司主体
     *
     * @param vppBaseCorporation 公司主体
     * @return 结果
     */
    @Override
    public int insertVppBaseCorporation(VppBaseCorporation vppBaseCorporation) {
        vppBaseCorporation.setCreateTime(DateUtils.getNowDate());
        return vppBaseCorporationMapper.insertVppBaseCorporation(vppBaseCorporation);
    }

    /**
     * 修改公司主体
     *
     * @param vppBaseCorporation 公司主体
     * @return 结果
     */
    @Override
    public int updateVppBaseCorporation(VppBaseCorporation vppBaseCorporation) {
        vppBaseCorporation.setUpdateTime(DateUtils.getNowDate());
        return vppBaseCorporationMapper.updateVppBaseCorporation(vppBaseCorporation);
    }

    /**
     * 批量删除公司主体
     *
     * @param corporationIds 需要删除的公司主体主键
     * @return 结果
     */
    @Override
    public int deleteVppBaseCorporationByCorporationIds(Long[] corporationIds) {
        return vppBaseCorporationMapper.deleteVppBaseCorporationByCorporationIds(corporationIds);
    }

    /**
     * 删除公司主体信息
     *
     * @param corporationId 公司主体主键
     * @return 结果
     */
    @Override
    public int deleteVppBaseCorporationByCorporationId(Long corporationId) {
        return vppBaseCorporationMapper.deleteVppBaseCorporationByCorporationId(corporationId);
    }
}