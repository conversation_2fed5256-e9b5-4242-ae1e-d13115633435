package com.vpp.td.mapper;

import com.vpp.td.domain.VppInvitationScheme;
import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface VppInvitationSchemeMapper {
    /**
     * 新增邀约方案
     */
    int insertScheme(VppInvitationScheme scheme);

    /**
     * 校验方案名称唯一性（同一邀约计划下）
     */
    int checkSchemeNameUnique(@Param("schemeName") String schemeName, @Param("invitationId") Long invitationId);

    /**
     * 根据邀约ID查询方案列表（按创建时间倒序）
     */
    List<VppInvitationScheme> selectSchemeListByInvitationId(Long invitationId);

    /**
     * 根据方案ID删除方案
     */
    int deleteSchemeById(Long schemeId);

    /**
     * 根据条件查询方案列表（示例：支持分页、名称模糊查询）
     */
    List<VppInvitationScheme> selectSchemeList(Map<String, Object> params);

    /**
     * 根据条件统计方案数量（用于分页查询总记录数）
     */
    Integer selectSchemeCount(Map<String, Object> params);

    /**
     * 根据ID查询方案
     */
    VppInvitationScheme selectById(Long schemeId);

    /**
     * 更新方案的最优状态
     */
    int updateIsOptimal(VppInvitationScheme scheme);
}