package com.vpp.aggregator.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 聚合用户设备对象 vpp_base_device
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public class VppBaseDevice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long deviceId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String deviceName;

    /**
     * 设备额定电压，V
     */
    @Excel(name = "设备额定电压，V")
    private Long deviceRatedVoltage;

    /**
     * 设备额定电流
     */
    @Excel(name = "设备额定电流")
    private Long deviceRatedCurrent;

    /**
     * 设备额定功率
     */
    @Excel(name = "设备额定功率")
    private Long deviceRatedPower;

    /**
     * 设备最大电压
     */
    @Excel(name = "设备最大电压")
    private Long deviceMaxVoltage;

    /**
     * 设备最大电流
     */
    @Excel(name = "设备最大电流")
    private Long deviceMaxCurrent;

    /**
     * 设备最大功率
     */
    @Excel(name = "设备最大功率")
    private Long deviceMaxPower;

    /**
     * 设备类型
     */
    @Excel(name = "设备类型")
    private Long deviceType;

    /**
     * 设备状态
     */
    @Excel(name = "设备状态")
    private Long deviceStatus;

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceRatedVoltage(Long deviceRatedVoltage) {
        this.deviceRatedVoltage = deviceRatedVoltage;
    }

    public Long getDeviceRatedVoltage() {
        return deviceRatedVoltage;
    }

    public void setDeviceRatedCurrent(Long deviceRatedCurrent) {
        this.deviceRatedCurrent = deviceRatedCurrent;
    }

    public Long getDeviceRatedCurrent() {
        return deviceRatedCurrent;
    }

    public void setDeviceRatedPower(Long deviceRatedPower) {
        this.deviceRatedPower = deviceRatedPower;
    }

    public Long getDeviceRatedPower() {
        return deviceRatedPower;
    }

    public void setDeviceMaxVoltage(Long deviceMaxVoltage) {
        this.deviceMaxVoltage = deviceMaxVoltage;
    }

    public Long getDeviceMaxVoltage() {
        return deviceMaxVoltage;
    }

    public void setDeviceMaxCurrent(Long deviceMaxCurrent) {
        this.deviceMaxCurrent = deviceMaxCurrent;
    }

    public Long getDeviceMaxCurrent() {
        return deviceMaxCurrent;
    }

    public void setDeviceMaxPower(Long deviceMaxPower) {
        this.deviceMaxPower = deviceMaxPower;
    }

    public Long getDeviceMaxPower() {
        return deviceMaxPower;
    }

    public void setDeviceType(Long deviceType) {
        this.deviceType = deviceType;
    }

    public Long getDeviceType() {
        return deviceType;
    }

    public void setDeviceStatus(Long deviceStatus) {
        this.deviceStatus = deviceStatus;
    }

    public Long getDeviceStatus() {
        return deviceStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("deviceId", getDeviceId())
                .append("deviceName", getDeviceName())
                .append("deviceRatedVoltage", getDeviceRatedVoltage())
                .append("deviceRatedCurrent", getDeviceRatedCurrent())
                .append("deviceRatedPower", getDeviceRatedPower())
                .append("deviceMaxVoltage", getDeviceMaxVoltage())
                .append("deviceMaxCurrent", getDeviceMaxCurrent())
                .append("deviceMaxPower", getDeviceMaxPower())
                .append("deviceType", getDeviceType())
                .append("deviceStatus", getDeviceStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}