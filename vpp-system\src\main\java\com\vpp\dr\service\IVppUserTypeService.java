package com.vpp.dr.service;

import com.vpp.dr.domain.VppUserType;
import java.util.List;

public interface IVppUserTypeService {
    /**
     * 查询所有用户类型（含层级结构）
     */
    List<VppUserType> getAllUserTypes();

    /**
     * 根据ID查询用户类型详情
     */
    VppUserType getUserTypeById(Long id);

    /**
     * 新增用户类型
     */
    boolean addUserType(VppUserType userType);

    /**
     * 更新用户类型
     */
    boolean updateUserType(VppUserType userType);

    /**
     * 删除用户类型
     */
    boolean deleteUserType(Long id);

    /**
     * 根据类型编码查询用户类型及关联用户
     * @return 用户类型对象（包含用户列表，无用户时返回空列表）
     */
    VppUserType queryUsersByUserType(Long userId);
}