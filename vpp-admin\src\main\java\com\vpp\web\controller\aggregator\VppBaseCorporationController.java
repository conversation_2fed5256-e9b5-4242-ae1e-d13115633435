package com.vpp.web.controller.aggregator;

import com.vpp.aggregator.domain.VppBaseCorporation;
import com.vpp.aggregator.service.IVppBaseCorporationService;
import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 公司主体Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/vpp/baseCorporation")
// @Api(tags = "公司主体")
public class VppBaseCorporationController extends BaseController {
    @Autowired
    private IVppBaseCorporationService vppBaseCorporationService;

    /**
     * 查询公司主体列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseCorporation:list')")
    @GetMapping("/list")
    // @ApiOperation(value = "获取公司主体列表")
    public TableDataInfo list(VppBaseCorporation vppBaseCorporation) {
        startPage();
        List<VppBaseCorporation> list = vppBaseCorporationService.selectVppBaseCorporationList(vppBaseCorporation);
        return getDataTable(list);
    }

    /**
     * 导出公司主体列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseCorporation:export')")
    @Log(title = "公司主体", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    // @ApiOperation(value = "导出公司主体列表")
    public void export(HttpServletResponse response, VppBaseCorporation vppBaseCorporation) {
        List<VppBaseCorporation> list = vppBaseCorporationService.selectVppBaseCorporationList(vppBaseCorporation);
        ExcelUtil<VppBaseCorporation> util = new ExcelUtil<VppBaseCorporation>(VppBaseCorporation.class);
        util.exportExcel(response, list, "公司主体数据");
    }

    /**
     * 获取公司主体详细信息
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseCorporation:query')")
    @GetMapping(value = "/{corporationId}")
    // @ApiOperation(value = "获取公司主体详细信息")
    public AjaxResult getInfo(@PathVariable("corporationId") Long corporationId) {
        return success(vppBaseCorporationService.selectVppBaseCorporationByCorporationId(corporationId));
    }

    /**
     * 新增公司主体
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseCorporation:add')")
    @Log(title = "公司主体", businessType = BusinessType.INSERT)
    @PostMapping
    // @ApiOperation(value = "新增公司主体")
    public AjaxResult add(@RequestBody VppBaseCorporation vppBaseCorporation) {
        return toAjax(vppBaseCorporationService.insertVppBaseCorporation(vppBaseCorporation));
    }

    /**
     * 修改公司主体
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseCorporation:edit')")
    @Log(title = "公司主体", businessType = BusinessType.UPDATE)
    @PutMapping
    // @ApiOperation(value = "修改公司主体")
    public AjaxResult edit(@RequestBody VppBaseCorporation vppBaseCorporation) {
        return toAjax(vppBaseCorporationService.updateVppBaseCorporation(vppBaseCorporation));
    }

    /**
     * 删除公司主体
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseCorporation:remove')")
    @Log(title = "公司主体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{corporationIds}")
    // @ApiOperation(value = "删除公司主体")
    public AjaxResult remove(@PathVariable Long[] corporationIds) {
        return toAjax(vppBaseCorporationService.deleteVppBaseCorporationByCorporationIds(corporationIds));
    }
}