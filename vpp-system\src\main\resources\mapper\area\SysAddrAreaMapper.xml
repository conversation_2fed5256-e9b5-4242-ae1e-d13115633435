<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.area.mapper.SysAddrAreaMapper">

    <resultMap type="SysAddrArea" id="SysAddrAreaResult">
        <result property="id"    column="id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="cityCode"    column="city_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="shortName"    column="short_name"    />
        <result property="lng"    column="lng"    />
        <result property="lat"    column="lat"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysAddrAreaVo">
        select id, area_code, city_code, area_name, short_name, lng, lat, sort, create_time, update_time, create_by, update_by, remark from sys_addr_area
    </sql>

    <select id="selectSysAddrAreaList" parameterType="SysAddrArea" resultMap="SysAddrAreaResult">
        <include refid="selectSysAddrAreaVo"/>
        <where>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="cityCode != null  and cityCode != ''"> and city_code = #{cityCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="shortName != null  and shortName != ''"> and short_name like concat('%', #{shortName}, '%')</if>
            <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectSysAddrAreaById" parameterType="String" resultMap="SysAddrAreaResult">
        <include refid="selectSysAddrAreaVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysAddrArea" parameterType="SysAddrArea">
        insert into sys_addr_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="areaCode != null and areaCode != ''">area_code,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="areaName != null and areaName != ''">area_name,</if>
            <if test="shortName != null and shortName != ''">short_name,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="areaCode != null and areaCode != ''">#{areaCode},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="areaName != null and areaName != ''">#{areaName},</if>
            <if test="shortName != null and shortName != ''">#{shortName},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSysAddrArea" parameterType="SysAddrArea">
        update sys_addr_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaCode != null and areaCode != ''">area_code = #{areaCode},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="areaName != null and areaName != ''">area_name = #{areaName},</if>
            <if test="shortName != null and shortName != ''">short_name = #{shortName},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAddrAreaById" parameterType="String">
        delete from sys_addr_area where id = #{id}
    </delete>

    <delete id="deleteSysAddrAreaByIds" parameterType="String">
        delete from sys_addr_area where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>