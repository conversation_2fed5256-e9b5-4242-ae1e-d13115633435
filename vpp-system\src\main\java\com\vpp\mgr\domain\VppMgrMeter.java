package com.vpp.mgr.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 设备对象 vpp_mgr_meter
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VppMgrMeter extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long meterId;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String meterName;

    /**
     * 设备编号
     */
    @Excel(name = "设备编号")
    private String meterCode;

    /**
     * 在线状态(01-在线, 02离线)
     */
    @Excel(name = "在线状态(01-在线, 02离线)")
    private String online;

    /**
     * 所属用户户号
     */
    @Excel(name = "所属用户户号")
    private String accountNumber;

    /**
     * 设备类型
     */
    @Excel(name = "设备类型")
    private String meterTye;

    /**
     * 供电局资产编号
     */
    @Excel(name = "供电局资产编号")
    private String psban;

    /**
     * 设备状态
     */
    @Excel(name = "设备状态")
    private String meterStatus;

    /**
     * 电压等级
     */
    @Excel(name = "电压等级")
    private String vLevel;

    /**
     * 额定电压
     */
    @Excel(name = "额定电压")
    private String vRated;

    /**
     * 额定电压浮动
     */
    @Excel(name = "额定电压浮动")
    private String vRatedSwim;

    /**
     * 额定电压除数
     */
    @Excel(name = "额定电压除数")
    private String vRatedDivider;

    /**
     * 安装地址
     */
    @Excel(name = "安装地址")
    private String mountAddr;

    /**
     * 调节方式(01-可调节,02-不可调节)
     */
    @Excel(name = "调节方式(01-可调节,02-不可调节)")
    private String adjustType;

    /**
     * 响应类型(01-人工响应,02自动响应)
     */
    @Excel(name = "响应类型(01-人工响应,02自动响应)")
    private String respType;

    /**
     * 向上调节容量(kw)
     */
    @Excel(name = "向上调节容量(kw)")
    private BigDecimal upRegCap;

    /**
     * 向下调节容量(kw)
     */
    @Excel(name = "向下调节容量(kw)")
    private BigDecimal downRegCap;

    /**
     * 向上调节速率(kw/min)
     */
    @Excel(name = "向上调节速率(kw/min)")
    private BigDecimal upRegRate;

    /**
     * 向下调节速率(kw/min)
     */
    @Excel(name = "向下调节速率(kw/min)")
    private BigDecimal downRegRate;

    /**
     * 向上调节持续时长(h)
     */
    @Excel(name = "向上调节持续时长(h)")
    private BigDecimal upDuration;

    /**
     * 向下调节持续时长(h)
     */
    @Excel(name = "向下调节持续时长(h)")
    private BigDecimal downDuration;

    /**
     * 机构(部门)ID
     */
    @Excel(name = "机构(部门)ID")
    private Long deptId;

    /**
     * 用户ID(sys_user)
     */
    @Excel(name = "用户ID(sys_user)")
    private Long userId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 额定功率
     */
    private String powerRated;
}