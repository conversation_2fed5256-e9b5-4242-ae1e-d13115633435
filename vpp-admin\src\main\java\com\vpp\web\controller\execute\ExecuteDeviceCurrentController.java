package com.vpp.web.controller.execute;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.execute.domain.ExecuteDeviceCurrent;
import com.vpp.execute.service.IExecuteDeviceCurrentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备实时检测电流Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/execute/deviceCurrent")
public class ExecuteDeviceCurrentController extends BaseController {
    @Autowired
    private IExecuteDeviceCurrentService executeDeviceCurrentService;

    /**
     * 查询设备实时检测电流列表
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceCurrent:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExecuteDeviceCurrent executeDeviceCurrent) {
        startPage();
        List<ExecuteDeviceCurrent> list = executeDeviceCurrentService.selectExecuteDeviceCurrentList(executeDeviceCurrent);
        return getDataTable(list);
    }

    /**
     * 导出设备实时检测电流列表
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceCurrent:export')")
    @Log(title = "设备实时检测电流", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExecuteDeviceCurrent executeDeviceCurrent) {
        List<ExecuteDeviceCurrent> list = executeDeviceCurrentService.selectExecuteDeviceCurrentList(executeDeviceCurrent);
        ExcelUtil<ExecuteDeviceCurrent> util = new ExcelUtil<ExecuteDeviceCurrent>(ExecuteDeviceCurrent.class);
        util.exportExcel(response, list, "设备实时检测电流数据");
    }

    /**
     * 获取设备实时检测电流详细信息
     */
    // @PreAuthorize("@ss.hasPermi('execute:deviceCurrent:query')")
    // @GetMapping(value = "/{timestamp}")
    // public AjaxResult getInfo(@PathVariable("timestamp") Date timestamp)
    // {
    //     return success(executeDeviceCurrentService.selectExecuteDeviceCurrentByTimestamp(timestamp));
    // }

    /**
     * 新增设备实时检测电流
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceCurrent:add')")
    @Log(title = "设备实时检测电流", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExecuteDeviceCurrent executeDeviceCurrent) {
        return toAjax(executeDeviceCurrentService.insertExecuteDeviceCurrent(executeDeviceCurrent));
    }

    /**
     * 修改设备实时检测电流
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceCurrent:edit')")
    @Log(title = "设备实时检测电流", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExecuteDeviceCurrent executeDeviceCurrent) {
        return toAjax(executeDeviceCurrentService.updateExecuteDeviceCurrent(executeDeviceCurrent));
    }

    /**
     * 删除设备实时检测电流
     */
    // @PreAuthorize("@ss.hasPermi('execute:deviceCurrent:remove')")
    // @Log(title = "设备实时检测电流", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{timestamps}")
    // public AjaxResult remove(@PathVariable Date[] timestamps)
    // {
    //     return toAjax(executeDeviceCurrentService.deleteExecuteDeviceCurrentByTimestamps(timestamps));
    // }
}