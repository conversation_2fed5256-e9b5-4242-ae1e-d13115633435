<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.execute.mapper.ExecutePlanDevicesMapper">

    <resultMap type="ExecutePlanDevices" id="ExecutePlanDevicesResult">
        <result property="executePlanDevicesId"    column="execute_plan_devices_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceRatedCurrent"    column="device_rated_current"    />
        <result property="deviceRatedPower"    column="device_rated_power"    />
        <result property="deviceRatedVoltage"    column="device_rated_voltage"    />
    </resultMap>

    <sql id="selectExecutePlanDevicesVo">
        select execute_plan_devices_id, device_id, device_rated_current, device_rated_power, device_rated_voltage from execute_plan_devices
    </sql>

    <select id="selectExecutePlanDevicesList" parameterType="ExecutePlanDevices" resultMap="ExecutePlanDevicesResult">
        <include refid="selectExecutePlanDevicesVo"/>
        <where>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="deviceRatedCurrent != null "> and device_rated_current = #{deviceRatedCurrent}</if>
            <if test="deviceRatedPower != null "> and device_rated_power = #{deviceRatedPower}</if>
            <if test="deviceRatedVoltage != null "> and device_rated_voltage = #{deviceRatedVoltage}</if>
        </where>
    </select>

    <select id="selectExecutePlanDevicesByExecutePlanDevicesId" parameterType="Long" resultMap="ExecutePlanDevicesResult">
        <include refid="selectExecutePlanDevicesVo"/>
        where execute_plan_devices_id = #{executePlanDevicesId}
    </select>

    <insert id="insertExecutePlanDevices" parameterType="ExecutePlanDevices" useGeneratedKeys="true" keyProperty="executePlanDevicesId">
        insert into execute_plan_devices
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deviceRatedCurrent != null">device_rated_current,</if>
            <if test="deviceRatedPower != null">device_rated_power,</if>
            <if test="deviceRatedVoltage != null">device_rated_voltage,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceRatedCurrent != null">#{deviceRatedCurrent},</if>
            <if test="deviceRatedPower != null">#{deviceRatedPower},</if>
            <if test="deviceRatedVoltage != null">#{deviceRatedVoltage},</if>
        </trim>
    </insert>

    <update id="updateExecutePlanDevices" parameterType="ExecutePlanDevices">
        update execute_plan_devices
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceRatedCurrent != null">device_rated_current = #{deviceRatedCurrent},</if>
            <if test="deviceRatedPower != null">device_rated_power = #{deviceRatedPower},</if>
            <if test="deviceRatedVoltage != null">device_rated_voltage = #{deviceRatedVoltage},</if>
        </trim>
        where execute_plan_devices_id = #{executePlanDevicesId}
    </update>

    <delete id="deleteExecutePlanDevicesByExecutePlanDevicesId" parameterType="Long">
        delete from execute_plan_devices where execute_plan_devices_id = #{executePlanDevicesId}
    </delete>

    <delete id="deleteExecutePlanDevicesByExecutePlanDevicesIds" parameterType="String">
        delete from execute_plan_devices where execute_plan_devices_id in
        <foreach item="executePlanDevicesId" collection="array" open="(" separator="," close=")">
            #{executePlanDevicesId}
        </foreach>
    </delete>
</mapper>