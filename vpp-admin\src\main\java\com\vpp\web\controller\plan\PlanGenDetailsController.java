package com.vpp.web.controller.plan;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.plan.domain.PlanGenDetails;
import com.vpp.plan.service.IPlanGenDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 生成计划的详细信息，key-value形式Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/vpp/genDetails")
public class PlanGenDetailsController extends BaseController {
    @Autowired
    private IPlanGenDetailsService planGenDetailsService;

    /**
     * 查询生成计划的详细信息，key-value形式列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanGenDetails planGenDetails) {
        startPage();
        List<PlanGenDetails> list = planGenDetailsService.selectPlanGenDetailsList(planGenDetails);
        return getDataTable(list);
    }

    /**
     * 导出生成计划的详细信息，key-value形式列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDetails:export')")
    @Log(title = "生成计划的详细信息，key-value形式", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanGenDetails planGenDetails) {
        List<PlanGenDetails> list = planGenDetailsService.selectPlanGenDetailsList(planGenDetails);
        ExcelUtil<PlanGenDetails> util = new ExcelUtil<PlanGenDetails>(PlanGenDetails.class);
        util.exportExcel(response, list, "生成计划的详细信息，key-value形式数据");
    }

    /**
     * 获取生成计划的详细信息，key-value形式详细信息
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDetails:query')")
    @GetMapping(value = "/{planGenDetailsId}")
    public AjaxResult getInfo(@PathVariable("planGenDetailsId") Long planGenDetailsId) {
        return success(planGenDetailsService.selectPlanGenDetailsByPlanGenDetailsId(planGenDetailsId));
    }

    /**
     * 新增生成计划的详细信息，key-value形式
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDetails:add')")
    @Log(title = "生成计划的详细信息，key-value形式", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanGenDetails planGenDetails) {
        return toAjax(planGenDetailsService.insertPlanGenDetails(planGenDetails));
    }

    /**
     * 修改生成计划的详细信息，key-value形式
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDetails:edit')")
    @Log(title = "生成计划的详细信息，key-value形式", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanGenDetails planGenDetails) {
        return toAjax(planGenDetailsService.updatePlanGenDetails(planGenDetails));
    }

    /**
     * 删除生成计划的详细信息，key-value形式
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDetails:remove')")
    @Log(title = "生成计划的详细信息，key-value形式", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planGenDetailsIds}")
    public AjaxResult remove(@PathVariable Long[] planGenDetailsIds) {
        return toAjax(planGenDetailsService.deletePlanGenDetailsByPlanGenDetailsIds(planGenDetailsIds));
    }
}