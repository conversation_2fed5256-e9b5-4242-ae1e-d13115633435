package com.vpp.utils;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * packageName com.vpp.utils
 *
 * <AUTHOR>
 * @version JDK 8
 * @className GenerateOrderNumUtil
 * @date 2025/6/13
 * @description TODO
 */
@Component
public class GenerateOrderNo {

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyyMMdd");

    private static final String ORDER_CODE_KEY = "order_code_key";
    private static final int INCREMENT = 1;

    @Resource
    private RedisTemplate<String, Integer> redisTemplate;


    // public String test() {
    //     // 定义日期格式
    //     DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
    //     //根据当前时间获取yyMMdd格式的时间字符串
    //     String format = LocalDate.now().format(formatter);
    //     //判断是否存在key，不存在则设置1天的过期时间
    //     if (Boolean.FALSE.equals(redisTemplate.hasKey(format))) {
    //         redisTemplate.expire(format, 1, TimeUnit.DAYS);
    //     }
    //     //使用incr获取自增的订单号
    //     Long increment = redisTemplate.opsForValue().increment(format);
    //     //将订单号不足4位前缀补0，"%04d"，%0表示前缀补0，4表示补充4位，d表示数字类型
    //     String oderCode = String.format("%04d", increment);
    //     return format + oderCode;
    // }

    /**
     * 生成默认格式的单号
     * 格式：yyyyMMdd + 递增编号（5位）
     */
    public String generateOrderNo() {
        String datePrefix = SDF.format(new Date());
        if (Boolean.FALSE.equals(redisTemplate.hasKey(datePrefix))) {
            redisTemplate.expire(datePrefix, 1, TimeUnit.DAYS);
        }
        long incrementNumber = getIncrement(ORDER_CODE_KEY);
        String numberPart = String.format("%05d", incrementNumber);
        return datePrefix + numberPart;
    }

    /**
     * 生成自定义前缀的单号
     * 格式：自定义前缀 + yyyyMMdd + 递增编号（5位）
     */
    public String generateCustomOrderNo(String prefix) {
        String datePrefix = SDF.format(new Date());
        String counterKey = prefix + "_counter_" + datePrefix;
        if (Boolean.FALSE.equals(redisTemplate.hasKey(counterKey))) {
            redisTemplate.expire(counterKey, 1, TimeUnit.DAYS);
        }
        long incrementNumber = getIncrement(counterKey);
        String numberPart = String.format("%05d", incrementNumber);
        return prefix + datePrefix + numberPart;
    }

    private long getIncrement(String key) {
        try {
            // 使用 Redis 的 INCR 函数生成递增编号
            return redisTemplate.opsForValue().increment(key, INCREMENT);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate order number", e);
        }
    }

}