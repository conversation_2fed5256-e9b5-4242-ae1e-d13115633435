package com.vpp.td.service;

import com.vpp.dr.domain.VppExchangeInvitation;
import com.vpp.td.domain.VppInvitationDeclaration;
import com.vpp.td.domain.VppInvitationScheme;
import java.util.List;
import java.util.Map;

public interface ITradeDeclarationService {
    /**
     * 根据邀约ID获取事件信息
     */
    VppExchangeInvitation getEventInfo(Long invitationId);

    /**
     * 根据邀约ID获取关联的方案列表
     */
    List<VppInvitationScheme> getSchemeList(Long invitationId);

    /**
     * 分页查询交易申报列表（带筛选）
     */
    List<VppInvitationDeclaration> getDeclarationList(Map<String, Object> params);

    /**
     * 查询交易申报总记录数（带筛选）
     */
    int getDeclarationCount(Map<String, Object> params);
}