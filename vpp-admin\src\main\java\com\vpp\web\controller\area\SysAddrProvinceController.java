package com.vpp.web.controller.area;

import com.vpp.area.domain.SysAddrProvince;
import com.vpp.area.service.ISysAddrProvinceService;
import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 省份Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/system/province")
public class SysAddrProvinceController extends BaseController {
    @Autowired
    private ISysAddrProvinceService sysAddrProvinceService;

    /**
     * 查询省份列表
     */
    @PreAuthorize("@ss.hasPermi('system:province:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAddrProvince sysAddrProvince) {
        startPage();
        List<SysAddrProvince> list = sysAddrProvinceService.selectSysAddrProvinceList(sysAddrProvince);
        return getDataTable(list);
    }

    /**
     * 导出省份列表
     */
    @PreAuthorize("@ss.hasPermi('system:province:export')")
    @Log(title = "省份", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAddrProvince sysAddrProvince) {
        List<SysAddrProvince> list = sysAddrProvinceService.selectSysAddrProvinceList(sysAddrProvince);
        ExcelUtil<SysAddrProvince> util = new ExcelUtil<SysAddrProvince>(SysAddrProvince.class);
        util.exportExcel(response, list, "省份数据");
    }

    /**
     * 获取省份详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:province:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(sysAddrProvinceService.selectSysAddrProvinceById(id));
    }

    /**
     * 新增省份
     */
    @PreAuthorize("@ss.hasPermi('system:province:add')")
    @Log(title = "省份", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAddrProvince sysAddrProvince) {
        return toAjax(sysAddrProvinceService.insertSysAddrProvince(sysAddrProvince));
    }

    /**
     * 修改省份
     */
    @PreAuthorize("@ss.hasPermi('system:province:edit')")
    @Log(title = "省份", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAddrProvince sysAddrProvince) {
        return toAjax(sysAddrProvinceService.updateSysAddrProvince(sysAddrProvince));
    }

    /**
     * 删除省份
     */
    @PreAuthorize("@ss.hasPermi('system:province:remove')")
    @Log(title = "省份", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(sysAddrProvinceService.deleteSysAddrProvinceByIds(ids));
    }
}