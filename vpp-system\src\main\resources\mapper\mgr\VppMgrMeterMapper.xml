<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.mgr.mapper.VppMgrMeterMapper">

    <resultMap type="VppMgrMeter" id="VppMgrMeterResult">
        <result property="meterId"    column="meter_id"    />
        <result property="meterName"    column="meter_name"    />
        <result property="meterCode"    column="meter_code"    />
        <result property="online"    column="online"    />
        <result property="accountNumber"    column="account_number"    />
        <result property="meterTye"    column="meter_tye"    />
        <result property="psban"    column="psban"    />
        <result property="meterStatus"    column="meter_status"    />
        <result property="vLevel"    column="v_level"    />
        <result property="vRated"    column="v_rated"    />
        <result property="vRatedSwim"    column="v_rated_swim"    />
        <result property="vRatedDivider"    column="v_rated_divider"    />
        <result property="mountAddr"    column="mount_addr"    />
        <result property="adjustType"    column="adjust_type"    />
        <result property="respType"    column="resp_type"    />
        <result property="upRegCap"    column="up_reg_cap"    />
        <result property="downRegCap"    column="down_reg_cap"    />
        <result property="upRegRate"    column="up_reg_rate"    />
        <result property="downRegRate"    column="down_reg_rate"    />
        <result property="upDuration"    column="up_duration"    />
        <result property="downDuration"    column="down_duration"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="powerRated"    column="power_rated"    />
    </resultMap>

    <sql id="selectVppMgrMeterVo">
        select meter_id, meter_name, meter_code, online, account_number, meter_tye, psban, meter_status, v_level, v_rated, v_rated_swim, v_rated_divider, mount_addr, adjust_type, resp_type, up_reg_cap, down_reg_cap, up_reg_rate, down_reg_rate, up_duration, down_duration, dept_id, user_id, del_flag, create_by, create_time, update_by, update_time, remark,power_rated from vpp_mgr_meter
    </sql>

    <select id="selectVppMgrMeterList" parameterType="VppMgrMeter" resultMap="VppMgrMeterResult">
        <include refid="selectVppMgrMeterVo"/>
        <where>
            <if test="meterName != null  and meterName != ''"> and meter_name like concat('%', #{meterName}, '%')</if>
            <if test="meterCode != null  and meterCode != ''"> and meter_code = #{meterCode}</if>
            <if test="online != null  and online != ''"> and online = #{online}</if>
            <if test="accountNumber != null  and accountNumber != ''"> and account_number = #{accountNumber}</if>
            <if test="meterTye != null  and meterTye != ''"> and meter_tye = #{meterTye}</if>
            <if test="psban != null  and psban != ''"> and psban = #{psban}</if>
            <if test="meterStatus != null  and meterStatus != ''"> and meter_status = #{meterStatus}</if>
            <if test="vLevel != null  and vLevel != ''"> and v_level = #{vLevel}</if>
            <if test="vRated != null  and vRated != ''"> and v_rated = #{vRated}</if>
            <if test="vRatedSwim != null  and vRatedSwim != ''"> and v_rated_swim = #{vRatedSwim}</if>
            <if test="vRatedDivider != null  and vRatedDivider != ''"> and v_rated_divider = #{vRatedDivider}</if>
            <if test="mountAddr != null  and mountAddr != ''"> and mount_addr = #{mountAddr}</if>
            <if test="adjustType != null  and adjustType != ''"> and adjust_type = #{adjustType}</if>
            <if test="respType != null  and respType != ''"> and resp_type = #{respType}</if>
            <if test="upRegCap != null "> and up_reg_cap = #{upRegCap}</if>
            <if test="downRegCap != null "> and down_reg_cap = #{downRegCap}</if>
            <if test="upRegRate != null "> and up_reg_rate = #{upRegRate}</if>
            <if test="downRegRate != null "> and down_reg_rate = #{downRegRate}</if>
            <if test="upDuration != null "> and up_duration = #{upDuration}</if>
            <if test="downDuration != null "> and down_duration = #{downDuration}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="powerRated != null  and powerRated != ''"> and power_rated = #{powerRated}</if>
        </where>
    </select>

    <select id="selectVppMgrMeterByMeterId" parameterType="Long" resultMap="VppMgrMeterResult">
        <include refid="selectVppMgrMeterVo"/>
        where meter_id = #{meterId}
    </select>

    <insert id="insertVppMgrMeter" parameterType="VppMgrMeter">
        insert into vpp_mgr_meter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="meterId != null">meter_id,</if>
            <if test="meterName != null and meterName != ''">meter_name,</if>
            <if test="meterCode != null and meterCode != ''">meter_code,</if>
            <if test="online != null and online != ''">online,</if>
            <if test="accountNumber != null and accountNumber != ''">account_number,</if>
            <if test="meterTye != null">meter_tye,</if>
            <if test="psban != null and psban != ''">psban,</if>
            <if test="meterStatus != null">meter_status,</if>
            <if test="vLevel != null and vLevel != ''">v_level,</if>
            <if test="vRated != null and vRated != ''">v_rated,</if>
            <if test="vRatedSwim != null and vRatedSwim != ''">v_rated_swim,</if>
            <if test="vRatedDivider != null and vRatedDivider != ''">v_rated_divider,</if>
            <if test="mountAddr != null and mountAddr != ''">mount_addr,</if>
            <if test="adjustType != null and adjustType != ''">adjust_type,</if>
            <if test="respType != null and respType != ''">resp_type,</if>
            <if test="upRegCap != null">up_reg_cap,</if>
            <if test="downRegCap != null">down_reg_cap,</if>
            <if test="upRegRate != null">up_reg_rate,</if>
            <if test="downRegRate != null">down_reg_rate,</if>
            <if test="upDuration != null">up_duration,</if>
            <if test="downDuration != null">down_duration,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="powerRated != null  and powerRated != ''"> power_rated,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="meterId != null">#{meterId},</if>
            <if test="meterName != null and meterName != ''">#{meterName},</if>
            <if test="meterCode != null and meterCode != ''">#{meterCode},</if>
            <if test="online != null and online != ''">#{online},</if>
            <if test="accountNumber != null and accountNumber != ''">#{accountNumber},</if>
            <if test="meterTye != null">#{meterTye},</if>
            <if test="psban != null and psban != ''">#{psban},</if>
            <if test="meterStatus != null">#{meterStatus},</if>
            <if test="vLevel != null and vLevel != ''">#{vLevel},</if>
            <if test="vRated != null and vRated != ''">#{vRated},</if>
            <if test="vRatedSwim != null and vRatedSwim != ''">#{vRatedSwim},</if>
            <if test="vRatedDivider != null and vRatedDivider != ''">#{vRatedDivider},</if>
            <if test="mountAddr != null and mountAddr != ''">#{mountAddr},</if>
            <if test="adjustType != null and adjustType != ''">#{adjustType},</if>
            <if test="respType != null and respType != ''">#{respType},</if>
            <if test="upRegCap != null">#{upRegCap},</if>
            <if test="downRegCap != null">#{downRegCap},</if>
            <if test="upRegRate != null">#{upRegRate},</if>
            <if test="downRegRate != null">#{downRegRate},</if>
            <if test="upDuration != null">#{upDuration},</if>
            <if test="downDuration != null">#{downDuration},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="powerRated != null  and powerRated != ''"> #{powerRated},</if>
        </trim>
    </insert>

    <update id="updateVppMgrMeter" parameterType="VppMgrMeter">
        update vpp_mgr_meter
        <trim prefix="SET" suffixOverrides=",">
            <if test="meterName != null and meterName != ''">meter_name = #{meterName},</if>
            <if test="meterCode != null and meterCode != ''">meter_code = #{meterCode},</if>
            <if test="online != null and online != ''">online = #{online},</if>
            <if test="accountNumber != null and accountNumber != ''">account_number = #{accountNumber},</if>
            <if test="meterTye != null">meter_tye = #{meterTye},</if>
            <if test="psban != null and psban != ''">psban = #{psban},</if>
            <if test="meterStatus != null">meter_status = #{meterStatus},</if>
            <if test="vLevel != null and vLevel != ''">v_level = #{vLevel},</if>
            <if test="vRated != null and vRated != ''">v_rated = #{vRated},</if>
            <if test="vRatedSwim != null and vRatedSwim != ''">v_rated_swim = #{vRatedSwim},</if>
            <if test="vRatedDivider != null and vRatedDivider != ''">v_rated_divider = #{vRatedDivider},</if>
            <if test="mountAddr != null and mountAddr != ''">mount_addr = #{mountAddr},</if>
            <if test="adjustType != null and adjustType != ''">adjust_type = #{adjustType},</if>
            <if test="respType != null and respType != ''">resp_type = #{respType},</if>
            <if test="upRegCap != null">up_reg_cap = #{upRegCap},</if>
            <if test="downRegCap != null">down_reg_cap = #{downRegCap},</if>
            <if test="upRegRate != null">up_reg_rate = #{upRegRate},</if>
            <if test="downRegRate != null">down_reg_rate = #{downRegRate},</if>
            <if test="upDuration != null">up_duration = #{upDuration},</if>
            <if test="downDuration != null">down_duration = #{downDuration},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="powerRated != null and powerRated != ''">power_rated = #{powerRated},</if>
        </trim>
        where meter_id = #{meterId}
    </update>

    <delete id="deleteVppMgrMeterByMeterId" parameterType="Long">
        delete from vpp_mgr_meter where meter_id = #{meterId}
    </delete>

    <delete id="deleteVppMgrMeterByMeterIds" parameterType="String">
        delete from vpp_mgr_meter where meter_id in
        <foreach item="meterId" collection="array" open="(" separator="," close=")">
            #{meterId}
        </foreach>
    </delete>
</mapper>