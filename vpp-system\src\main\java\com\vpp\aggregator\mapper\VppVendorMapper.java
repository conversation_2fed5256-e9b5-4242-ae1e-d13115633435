package com.vpp.aggregator.mapper;

import com.vpp.aggregator.domain.VppVendor;

import java.util.List;

/**
 * 虚拟电厂供应商，是vpp_base虚拟电厂的子关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface VppVendorMapper {
    /**
     * 查询虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     *
     * @param vendorId 虚拟电厂供应商，是vpp_base虚拟电厂的子关系主键
     * @return 虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     */
    public VppVendor selectVppVendorByVendorId(Long vendorId);

    /**
     * 查询虚拟电厂供应商，是vpp_base虚拟电厂的子关系列表
     *
     * @param vppVendor 虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     * @return 虚拟电厂供应商，是vpp_base虚拟电厂的子关系集合
     */
    public List<VppVendor> selectVppVendorList(VppVendor vppVendor);

    /**
     * 新增虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     *
     * @param vppVendor 虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     * @return 结果
     */
    public int insertVppVendor(VppVendor vppVendor);

    /**
     * 修改虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     *
     * @param vppVendor 虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     * @return 结果
     */
    public int updateVppVendor(VppVendor vppVendor);

    /**
     * 删除虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     *
     * @param vendorId 虚拟电厂供应商，是vpp_base虚拟电厂的子关系主键
     * @return 结果
     */
    public int deleteVppVendorByVendorId(Long vendorId);

    /**
     * 批量删除虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     *
     * @param vendorIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVppVendorByVendorIds(Long[] vendorIds);
}
