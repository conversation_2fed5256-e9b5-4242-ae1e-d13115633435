<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.mgr.mapper.VppMgrCntMapper">

    <resultMap type="VppMgrCnt" id="VppMgrCntResult">
        <result property="cntId"    column="cnt_id"    />
        <result property="cntName"    column="cnt_name"    />
        <result property="cntNum"    column="cnt_num"    />
        <result property="proxyUser"    column="uag_name"    />
        <result property="phone"    column="phone"    />
        <result property="cntType"    column="cnt_type"    />
        <result property="cntStattus"    column="cnt_stattus"    />
        <result property="cntEndTime"    column="cnt_end_time"    />
        <result property="cntStartTime"    column="cnt_start_time"    />
        <result property="cntSign"    column="cnt_sign"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="uagId"    column="proxy_uag_id"    />
    </resultMap>

    <sql id="selectVppMgrCntVo">
        select cnt_id, cnt_name, cnt_num, proxy_user, phone, cnt_type, cnt_stattus, cnt_end_time, cnt_start_time, cnt_sign, dept_id, user_id, del_flag, create_by, create_time, update_by, update_time, remark,(select uag_name from vpp_mgr_uag where uag_id=proxy_uag_id) as uag_name from vpp_mgr_cnt
    </sql>

    <select id="selectVppMgrCntList" parameterType="VppMgrCnt" resultMap="VppMgrCntResult">
<!--        <include refid="selectVppMgrCntVo"/>-->
        select *,(select uag_name from vpp_mgr_uag where uag_id=a.proxy_uag_id) as uag_name from vpp_mgr_cnt a
        where cnt_end_time >=now()
            <if test="cntName != null  and cntName != ''"> and cnt_name like concat('%', #{cntName}, '%')</if>
            <if test="cntNum != null  and cntNum != ''"> and cnt_num = #{cntNum}</if>
<!--            <if test="proxyUser != null  and proxyUser != ''"> and proxy_user = #{proxyUser}</if>-->
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="cntType != null  and cntType != ''"> and cnt_type = #{cntType}</if>
            <if test="cntStattus != null  and cntStattus != ''"> and cnt_stattus = #{cntStattus}</if>
            <if test="cntEndTime != null "> and cnt_end_time = #{cntEndTime}</if>
            <if test="cntStartTime != null "> and cnt_start_time = #{cntStartTime}</if>
            <if test="cntSign != null "> and cnt_sign = #{cntSign}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>

    </select>

    <select id="selectVppMgrCntByCntId" parameterType="Long" resultMap="VppMgrCntResult">
        <include refid="selectVppMgrCntVo"/>
        where cnt_id = #{cntId}
    </select>

    <insert id="insertVppMgrCnt" parameterType="VppMgrCnt" useGeneratedKeys="true" keyProperty="cntId">
        insert into vpp_mgr_cnt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cntName != null and cntName != ''">cnt_name,</if>
            <if test="cntNum != null and cntNum != ''">cnt_num,</if>
<!--            <if test="proxyUser != null and proxyUser != ''">proxy_user,</if>-->
            <if test="phone != null">phone,</if>
            <if test="cntType != null and cntType != ''">cnt_type,</if>
            <if test="cntStattus != null">cnt_stattus,</if>
            <if test="cntEndTime != null">cnt_end_time,</if>
            <if test="cntStartTime != null">cnt_start_time,</if>
            <if test="cntSign != null">cnt_sign,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="uagId != null">proxy_uag_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cntName != null and cntName != ''">#{cntName},</if>
            <if test="cntNum != null and cntNum != ''">#{cntNum},</if>
<!--            <if test="proxyUser != null and proxyUser != ''">(select uag_name from vpp_mgr_uag where uag_id=#{uagId}),</if>-->
            <if test="phone != null">#{phone},</if>
            <if test="cntType != null and cntType != ''">#{cntType},</if>
            <if test="cntStattus != null">#{cntStattus},</if>
            <if test="cntEndTime != null">#{cntEndTime},</if>
            <if test="cntStartTime != null">#{cntStartTime},</if>
            <if test="cntSign != null">#{cntSign},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="uagId != null">#{uagId},</if>
        </trim>
    </insert>

    <update id="updateVppMgrCnt" parameterType="VppMgrCnt">
        update vpp_mgr_cnt
        <trim prefix="SET" suffixOverrides=",">
            <if test="cntName != null and cntName != ''">cnt_name = #{cntName},</if>
            <if test="cntNum != null and cntNum != ''">cnt_num = #{cntNum},</if>
            <if test="proxyUser != null and proxyUser != ''">proxy_user = #{proxyUser},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="cntType != null and cntType != ''">cnt_type = #{cntType},</if>
            <if test="cntStattus != null">cnt_stattus = #{cntStattus},</if>
            <if test="cntEndTime != null">cnt_end_time = #{cntEndTime},</if>
            <if test="cntStartTime != null">cnt_start_time = #{cntStartTime},</if>
            <if test="cntSign != null">cnt_sign = #{cntSign},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where cnt_id = #{cntId}
    </update>

    <delete id="deleteVppMgrCntByCntId" parameterType="Long">
        delete from vpp_mgr_cnt where cnt_id = #{cntId}
    </delete>

    <delete id="deleteVppMgrCntByCntIds" parameterType="String">
        delete from vpp_mgr_cnt where cnt_id in
        <foreach item="cntId" collection="array" open="(" separator="," close=")">
            #{cntId}
        </foreach>
    </delete>

<!--   ==================== -->
    <select id="listByUserId" parameterType="Long" resultMap="VppMgrCntResult">
        <include refid="selectVppMgrCntVo"/>
        where user_id = #{userId}
    </select>
    <select id="listByDeptId" parameterType="Long" resultMap="VppMgrCntResult">
        <include refid="selectVppMgrCntVo"/>
        where dept_id = #{deptId}
    </select>
    <select id="selectVppMgrCntByUagId" parameterType="Long" resultMap="VppMgrCntResult">
        <include refid="selectVppMgrCntVo"/>
        where proxy_uag_id = #{uagid}
    </select>
</mapper>