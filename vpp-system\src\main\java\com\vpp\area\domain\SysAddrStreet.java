package com.vpp.area.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 街道设置对象 sys_addr_street
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public class SysAddrStreet extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 街道代码
     */
    @Excel(name = "街道代码")
    private String streetCode;

    /**
     * 父级区代码
     */
    @Excel(name = "父级区代码")
    private String areaCode;

    /**
     * 街道名称
     */
    @Excel(name = "街道名称")
    private String streetName;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String shortName;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String lng;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String lat;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long sort;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setStreetCode(String streetCode) {
        this.streetCode = streetCode;
    }

    public String getStreetCode() {
        return streetCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setStreetName(String streetName) {
        this.streetName = streetName;
    }

    public String getStreetName() {
        return streetName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getShortName() {
        return shortName;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLng() {
        return lng;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLat() {
        return lat;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public Long getSort() {
        return sort;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("streetCode", getStreetCode())
                .append("areaCode", getAreaCode())
                .append("streetName", getStreetName())
                .append("shortName", getShortName())
                .append("lng", getLng())
                .append("lat", getLat())
                .append("sort", getSort())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("remark", getRemark())
                .toString();
    }
}