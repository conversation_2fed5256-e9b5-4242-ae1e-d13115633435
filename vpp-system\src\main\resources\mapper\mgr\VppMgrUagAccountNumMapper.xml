<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.mgr.mapper.VppMgrUagAccountNumMapper">

    <resultMap type="VppMgrUagAccountNum" id="VppMgrUagAccountNumResult">
        <result property="deptId"    column="dept_id"    />
        <result property="uagAccountNum"    column="uag_account_num"    />
    </resultMap>

    <sql id="selectVppMgrUagAccountNumVo">
        select dept_id, uag_account_num from vpp_mgr_uag_account_num
    </sql>

    <select id="selectVppMgrUagAccountNumList" parameterType="VppMgrUagAccountNum" resultMap="VppMgrUagAccountNumResult">
        <include refid="selectVppMgrUagAccountNumVo"/>
        <where>
            <if test="uagAccountNum != null  and uagAccountNum != ''"> and uag_account_num = #{uagAccountNum}</if>
        </where>
    </select>

    <select id="selectVppMgrUagAccountNumByUserId" parameterType="Long" resultMap="VppMgrUagAccountNumResult">
        <include refid="selectVppMgrUagAccountNumVo"/>
        where dept_id = #{deptId}
    </select>

    <insert id="insertVppMgrUagAccountNum" parameterType="VppMgrUagAccountNum">
        insert into vpp_mgr_uag_account_num
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="uagAccountNum != null">uag_account_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="uagAccountNum != null">#{uagAccountNum},</if>
        </trim>
    </insert>

    <update id="updateVppMgrUagAccountNum" parameterType="VppMgrUagAccountNum">
        update vpp_mgr_uag_account_num
        <trim prefix="SET" suffixOverrides=",">
            <if test="uagAccountNum != null">uag_account_num = #{uagAccountNum},</if>
        </trim>
        where dept_id = #{deptId}
    </update>

    <delete id="deleteVppMgrUagAccountNumByDeptId" parameterType="Long">
        delete from vpp_mgr_uag_account_num where dept_id = #{deptId}
    </delete>

    <delete id="deleteVppMgrUagAccountNumByUserIds" parameterType="String">
        delete from vpp_mgr_uag_account_num where user_id in
        <foreach item="deptId" collection="array" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </delete>
<!--  ================================================================  -->
    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into vpp_mgr_uag_account_num(dept_id, uag_account_num)
        values
        <foreach collection="list" item="vppMgrUagAccountNum" separator=",">
            (#{vppMgrUagAccountNum.deptId}, #{vppMgrUagAccountNum.uagAccountNum})
        </foreach>
        on duplicate key
        update dept_id=values(dept_id), uag_account_num=values(uag_account_num);
    </insert>

    <select id="selectByDeptId" parameterType="Long" resultMap="VppMgrUagAccountNumResult">
        <include refid="selectVppMgrUagAccountNumVo"/>
        where dept_id = #{deptId}
    </select>
</mapper>