package com.vpp.td.service;

import com.vpp.td.domain.VppInvitationScheme;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface ISchemeService {
    /**
     * 新建邀约方案
     */
    boolean saveScheme(VppInvitationScheme scheme);

    /**
     * 查询方案列表（带分页和筛选）
     */
    List<VppInvitationScheme> getSchemeList(Map<String, Object> params, Integer pageNum, Integer pageSize);

    /**
     * 获取方案总记录数
     */
    int getSchemeCount(Map<String, Object> params);

    /**
     * 修改方案的最优状态
     * @param schemeId 方案ID
     * @param isOptimal 最优状态（0-是，1-否）
     * @return 更新后的方案实体
     */
    @Transactional(rollbackFor = Exception.class)
    VppInvitationScheme updateIsOptimal(Long schemeId, Integer isOptimal);
}