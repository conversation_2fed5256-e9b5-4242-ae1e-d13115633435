<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.plan.mapper.PlanGenPrepMapper">

    <resultMap type="PlanGenPrep" id="PlanGenPrepResult">
        <result property="planGenPrepId"    column="plan_gen_prep_id"    />
        <result property="planGenId"    column="plan_gen_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceRatedCurrent"    column="device_rated_current"    />
        <result property="deviceRatedPower"    column="device_rated_power"    />
        <result property="deviceRatedVoltage"    column="device_rated_voltage"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlanGenPrepVo">
        select plan_gen_prep_id, plan_gen_id, device_id, device_rated_current, device_rated_power, device_rated_voltage, create_by, create_time, update_by, update_time, remark from plan_gen_prep
    </sql>

    <select id="selectPlanGenPrepList" parameterType="PlanGenPrep" resultMap="PlanGenPrepResult">
        <include refid="selectPlanGenPrepVo"/>
        <where>
            <if test="planGenId != null "> and plan_gen_id = #{planGenId}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="deviceRatedCurrent != null "> and device_rated_current = #{deviceRatedCurrent}</if>
            <if test="deviceRatedPower != null "> and device_rated_power = #{deviceRatedPower}</if>
            <if test="deviceRatedVoltage != null "> and device_rated_voltage = #{deviceRatedVoltage}</if>
        </where>
    </select>

    <select id="selectPlanGenPrepByPlanGenPrepId" parameterType="Long" resultMap="PlanGenPrepResult">
        <include refid="selectPlanGenPrepVo"/>
        where plan_gen_prep_id = #{planGenPrepId}
    </select>

    <insert id="insertPlanGenPrep" parameterType="PlanGenPrep" useGeneratedKeys="true" keyProperty="planGenPrepId">
        insert into plan_gen_prep
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planGenId != null">plan_gen_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="deviceRatedCurrent != null">device_rated_current,</if>
            <if test="deviceRatedPower != null">device_rated_power,</if>
            <if test="deviceRatedVoltage != null">device_rated_voltage,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planGenId != null">#{planGenId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceRatedCurrent != null">#{deviceRatedCurrent},</if>
            <if test="deviceRatedPower != null">#{deviceRatedPower},</if>
            <if test="deviceRatedVoltage != null">#{deviceRatedVoltage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePlanGenPrep" parameterType="PlanGenPrep">
        update plan_gen_prep
        <trim prefix="SET" suffixOverrides=",">
            <if test="planGenId != null">plan_gen_id = #{planGenId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceRatedCurrent != null">device_rated_current = #{deviceRatedCurrent},</if>
            <if test="deviceRatedPower != null">device_rated_power = #{deviceRatedPower},</if>
            <if test="deviceRatedVoltage != null">device_rated_voltage = #{deviceRatedVoltage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where plan_gen_prep_id = #{planGenPrepId}
    </update>

    <delete id="deletePlanGenPrepByPlanGenPrepId" parameterType="Long">
        delete from plan_gen_prep where plan_gen_prep_id = #{planGenPrepId}
    </delete>

    <delete id="deletePlanGenPrepByPlanGenPrepIds" parameterType="String">
        delete from plan_gen_prep where plan_gen_prep_id in
        <foreach item="planGenPrepId" collection="array" open="(" separator="," close=")">
            #{planGenPrepId}
        </foreach>
    </delete>
</mapper>