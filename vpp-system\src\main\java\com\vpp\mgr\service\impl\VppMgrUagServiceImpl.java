package com.vpp.mgr.service.impl;

import com.vpp.common.core.domain.entity.SysDept;
import com.vpp.common.core.domain.entity.SysUser;
import com.vpp.common.utils.DateUtils;
import com.vpp.common.utils.SecurityUtils;
import com.vpp.mgr.domain.VppMgrUag;
import com.vpp.mgr.domain.VppMgrUagAccountNum;
import com.vpp.mgr.mapper.VppMgrUagAccountNumMapper;
import com.vpp.mgr.mapper.VppMgrUagMapper;
import com.vpp.mgr.service.IVppMgrUagService;
import com.vpp.system.mapper.SysDeptMapper;
import com.vpp.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 聚合商用户-信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class VppMgrUagServiceImpl implements IVppMgrUagService {
    @Autowired
    private VppMgrUagMapper vppMgrUagMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private VppMgrUagAccountNumMapper vppMgrUagAccountNumMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询聚合商用户-信息
     *
     * @param uagId 聚合商用户-信息主键
     * @return 聚合商用户-信息
     */
    @Override
    public VppMgrUag selectVppMgrUagByUagId(Long uagId) {
        VppMgrUag vppMgrUag = vppMgrUagMapper.selectVppMgrUagByUagId(uagId);
        vppMgrUag.setAccountNumList(vppMgrUagAccountNumMapper.selectByDeptId(vppMgrUag.getDeptId()));
        return vppMgrUag;
    }

    /**
     * 查询聚合商用户-信息列表
     *
     * @param vppMgrUag 聚合商用户-信息
     * @return 聚合商用户-信息
     */
    @Override
    public List<VppMgrUag> selectVppMgrUagList(VppMgrUag vppMgrUag) {
        Long deptId = SecurityUtils.getDeptId();

        vppMgrUag.setDeptId(deptId);

        List<VppMgrUag> vppMgrUags = vppMgrUagMapper.selectVppMgrUagList(vppMgrUag);

        vppMgrUags.forEach(vppMgrUag1 -> {
            List<VppMgrUagAccountNum> accountNums = vppMgrUagAccountNumMapper.selectByDeptId(vppMgrUag1.getDeptId());
            vppMgrUag1.setAccountNumList(accountNums);
        });
        return vppMgrUags;
    }

    /**
     * 新增聚合商用户-信息
     *
     * @param vppMgrUag 聚合商用户-信息
     * @return 结果
     */
    @Override
    public int insertVppMgrUag(VppMgrUag vppMgrUag) {
        List<VppMgrUagAccountNum> accountList = vppMgrUag.getAccountNumList();
        vppMgrUag.setCreateBy(SecurityUtils.getDeptId()+"");
        if (!CollectionUtils.isEmpty(accountList)) {
            vppMgrUagAccountNumMapper.saveOrUpdateBatch(accountList);
        }
        vppMgrUag.setCreateTime(DateUtils.getNowDate());
        return vppMgrUagMapper.insertVppMgrUag(vppMgrUag);
    }

    /**
     * 修改聚合商用户-信息
     *
     * @param vppMgrUag 聚合商用户-信息
     * @return 结果
     */
    @Override
    public int updateVppMgrUag(VppMgrUag vppMgrUag) {
        vppMgrUag.setUpdateTime(DateUtils.getNowDate());
        vppMgrUagAccountNumMapper.deleteVppMgrUagAccountNumByDeptId(vppMgrUag.getDeptId());
        List<VppMgrUagAccountNum> accountNumList = vppMgrUag.getAccountNumList();

        if (!CollectionUtils.isEmpty(accountNumList)) {
            vppMgrUagAccountNumMapper.saveOrUpdateBatch(accountNumList);
        }
        return vppMgrUagMapper.updateVppMgrUag(vppMgrUag);
    }

    /**
     * 批量删除聚合商用户-信息
     *
     * @param uagIds 需要删除的聚合商用户-信息主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrUagByUagIds(Long[] uagIds) {
        return vppMgrUagMapper.deleteVppMgrUagByUagIds(uagIds);
    }

    /**
     * 删除聚合商用户-信息信息
     *
     * @param uagId 聚合商用户-信息主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrUagByUagId(Long uagId) {
        return vppMgrUagMapper.deleteVppMgrUagByUagId(uagId);
    }

    @Override
    public List<VppMgrUag> listByUserId(Long userId) {

        return vppMgrUagMapper.listByUserId(userId);
        // 查询当用户的子机构
        // SysUser sysUser = sysUserMapper.selectUserById(userId);
        // List<SysDept> deptIds = sysDeptMapper.selectByParentId(sysUser.getDeptId());
        // List<Long> collect = deptIds.stream().map(SysDept::getDeptId).collect(Collectors.toList());
        // // 查询所有机构为查询机构的数据
        // if (CollectionUtils.isEmpty(collect)) {
        //     return Collections.emptyList();
        // }
        // return vppMgrUagMapper.listByUserIds(collect);
    }

    @Override
    public VppMgrUag selectVppMgrUagByDeptId(Long deptId,Long userId) {
        //根据userid查找uag id,获取聚合用户信息
        System.out.println(userId);
        VppMgrUag vppMgrUag = vppMgrUagMapper.selectVppMgrUagByDeptId(userId);
        System.out.println(vppMgrUag);
        if (vppMgrUag == null) {
            return null;
        }
        vppMgrUag.setAccountNumList(vppMgrUagAccountNumMapper.selectByDeptId(deptId));
        return vppMgrUag;
    }
}