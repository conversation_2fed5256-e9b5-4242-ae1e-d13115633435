package com.vpp.web.controller.mgr;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.enums.BusinessType;
import com.vpp.mgr.domain.VppMgrAggPp;
import com.vpp.mgr.service.IVppMgrAggPpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 聚合商-电厂信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("/mgr/agg/pp")
@Api(tags = "聚合商-电厂信息")
public class VppMgrAggPpController extends BaseController {
    @Autowired
    private IVppMgrAggPpService vppMgrAggPpService;

    /**
     * 查询聚合商-电厂信息列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:pp:list')")
    // @GetMapping("/list")
    // public TableDataInfo list(VppMgrAggPp vppMgrAggPp) {
    //     startPage();
    //     List<VppMgrAggPp> list = vppMgrAggPpService.selectVppMgrAggPpList(vppMgrAggPp);
    //     return getDataTable(list);
    // }

    /**
     * 导出聚合商-电厂信息列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:pp:export')")
    // @Log(title = "聚合商-电厂信息", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, VppMgrAggPp vppMgrAggPp) {
    //     List<VppMgrAggPp> list = vppMgrAggPpService.selectVppMgrAggPpList(vppMgrAggPp);
    //     ExcelUtil<VppMgrAggPp> util = new ExcelUtil<VppMgrAggPp>(VppMgrAggPp.class);
    //     util.exportExcel(response, list, "聚合商-电厂信息数据");
    // }

    /**
     * 获取聚合商-电厂信息详细信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:pp:query')")
    @GetMapping(value = "/{userId}")
    @ApiOperation("获取聚合商-电厂信息详细信息")
    public AjaxResult getInfo(@PathVariable("userId") Long userId) {
        return success(vppMgrAggPpService.selectVppMgrAggPpByAggPpId(userId));
    }

    /**
     * 新增聚合商-电厂信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:pp:add')")
    @Log(title = "聚合商-电厂信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增聚合商-电厂信息")
    public AjaxResult add(@RequestBody VppMgrAggPp vppMgrAggPp) {
        return toAjax(vppMgrAggPpService.insertVppMgrAggPp(vppMgrAggPp));
    }

    /**
     * 修改聚合商-电厂信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:pp:edit')")
    @Log(title = "聚合商-电厂信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改聚合商-电厂信息")
    public AjaxResult edit(@RequestBody VppMgrAggPp vppMgrAggPp) {
        return toAjax(vppMgrAggPpService.updateVppMgrAggPp(vppMgrAggPp));
    }

    /**
     * 删除聚合商-电厂信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:pp:remove')")
    // @Log(title = "聚合商-电厂信息", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{aggPpIds}")
    // public AjaxResult remove(@PathVariable Long[] aggPpIds) {
    //     return toAjax(vppMgrAggPpService.deleteVppMgrAggPpByAggPpIds(aggPpIds));
    // }
}