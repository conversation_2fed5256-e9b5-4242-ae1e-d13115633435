package com.vpp.area.service;

import com.vpp.area.domain.SysAddrProvince;

import java.util.List;

/**
 * 省份Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface ISysAddrProvinceService
{
    /**
     * 查询省份
     *
     * @param id 省份主键
     * @return 省份
     */
    public SysAddrProvince selectSysAddrProvinceById(String id);

    /**
     * 查询省份列表
     *
     * @param sysAddrProvince 省份
     * @return 省份集合
     */
    public List<SysAddrProvince> selectSysAddrProvinceList(SysAddrProvince sysAddrProvince);

    /**
     * 新增省份
     *
     * @param sysAddrProvince 省份
     * @return 结果
     */
    public int insertSysAddrProvince(SysAddrProvince sysAddrProvince);

    /**
     * 修改省份
     *
     * @param sysAddrProvince 省份
     * @return 结果
     */
    public int updateSysAddrProvince(SysAddrProvince sysAddrProvince);

    /**
     * 批量删除省份
     *
     * @param ids 需要删除的省份主键集合
     * @return 结果
     */
    public int deleteSysAddrProvinceByIds(String[] ids);

    /**
     * 删除省份信息
     *
     * @param id 省份主键
     * @return 结果
     */
    public int deleteSysAddrProvinceById(String id);
}
