package com.vpp.web.controller.execute;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.execute.domain.ExecuteDeviceStatus;
import com.vpp.execute.service.IExecuteDeviceStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 设备实时检测状态Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/execute/deviceStatus")
public class ExecuteDeviceStatusController extends BaseController {
    @Autowired
    private IExecuteDeviceStatusService executeDeviceStatusService;

    /**
     * 查询设备实时检测状态列表
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceStatus:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExecuteDeviceStatus executeDeviceStatus) {
        startPage();
        List<ExecuteDeviceStatus> list = executeDeviceStatusService.selectExecuteDeviceStatusList(executeDeviceStatus);
        return getDataTable(list);
    }

    /**
     * 导出设备实时检测状态列表
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceStatus:export')")
    @Log(title = "设备实时检测状态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExecuteDeviceStatus executeDeviceStatus) {
        List<ExecuteDeviceStatus> list = executeDeviceStatusService.selectExecuteDeviceStatusList(executeDeviceStatus);
        ExcelUtil<ExecuteDeviceStatus> util = new ExcelUtil<ExecuteDeviceStatus>(ExecuteDeviceStatus.class);
        util.exportExcel(response, list, "设备实时检测状态数据");
    }

    /**
     * 获取设备实时检测状态详细信息
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceStatus:query')")
    @GetMapping(value = "/{timestamp}")
    public AjaxResult getInfo(@PathVariable("timestamp") Date timestamp) {
        return success(executeDeviceStatusService.selectExecuteDeviceStatusByTimestamp(timestamp));
    }

    /**
     * 新增设备实时检测状态
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceStatus:add')")
    @Log(title = "设备实时检测状态", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExecuteDeviceStatus executeDeviceStatus) {
        return toAjax(executeDeviceStatusService.insertExecuteDeviceStatus(executeDeviceStatus));
    }

    /**
     * 修改设备实时检测状态
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceStatus:edit')")
    @Log(title = "设备实时检测状态", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExecuteDeviceStatus executeDeviceStatus) {
        return toAjax(executeDeviceStatusService.updateExecuteDeviceStatus(executeDeviceStatus));
    }

    /**
     * 删除设备实时检测状态
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceStatus:remove')")
    @Log(title = "设备实时检测状态", businessType = BusinessType.DELETE)
    @DeleteMapping("/{timestamps}")
    public AjaxResult remove(@PathVariable Date[] timestamps) {
        return toAjax(executeDeviceStatusService.deleteExecuteDeviceStatusByTimestamps(timestamps));
    }
}