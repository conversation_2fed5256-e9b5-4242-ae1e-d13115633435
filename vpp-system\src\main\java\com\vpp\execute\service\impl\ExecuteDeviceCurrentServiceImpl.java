package com.vpp.execute.service.impl;

import com.vpp.execute.domain.ExecuteDeviceCurrent;
import com.vpp.execute.mapper.ExecuteDeviceCurrentMapper;
import com.vpp.execute.service.IExecuteDeviceCurrentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备实时检测电流Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class ExecuteDeviceCurrentServiceImpl implements IExecuteDeviceCurrentService {
    @Autowired
    private ExecuteDeviceCurrentMapper executeDeviceCurrentMapper;

    /**
     * 查询设备实时检测电流
     *
     * @param timestamp 设备实时检测电流主键
     * @return 设备实时检测电流
     */
    // @Override
    // public ExecuteDeviceCurrent selectExecuteDeviceCurrentByTimestamp(Date timestamp)
    // {
    //     return executeDeviceCurrentMapper.selectExecuteDeviceCurrentByTimestamp(timestamp);
    // }

    /**
     * 查询设备实时检测电流列表
     *
     * @param executeDeviceCurrent 设备实时检测电流
     * @return 设备实时检测电流
     */
    @Override
    public List<ExecuteDeviceCurrent> selectExecuteDeviceCurrentList(ExecuteDeviceCurrent executeDeviceCurrent) {
        return executeDeviceCurrentMapper.selectExecuteDeviceCurrentList(executeDeviceCurrent);
    }

    /**
     * 新增设备实时检测电流
     *
     * @param executeDeviceCurrent 设备实时检测电流
     * @return 结果
     */
    @Override
    public int insertExecuteDeviceCurrent(ExecuteDeviceCurrent executeDeviceCurrent) {
        return executeDeviceCurrentMapper.insertExecuteDeviceCurrent(executeDeviceCurrent);
    }

    /**
     * 修改设备实时检测电流
     *
     * @param executeDeviceCurrent 设备实时检测电流
     * @return 结果
     */
    @Override
    public int updateExecuteDeviceCurrent(ExecuteDeviceCurrent executeDeviceCurrent) {
        return executeDeviceCurrentMapper.updateExecuteDeviceCurrent(executeDeviceCurrent);
    }

    /**
     * 批量删除设备实时检测电流
     *
     * @param timestamps 需要删除的设备实时检测电流主键
     * @return 结果
     */
    // @Override
    // public int deleteExecuteDeviceCurrentByTimestamps(Date[] timestamps)
    // {
    //     return executeDeviceCurrentMapper.deleteExecuteDeviceCurrentByTimestamps(timestamps);
    // }

    /**
     * 删除设备实时检测电流信息
     *
     * @param timestamp 设备实时检测电流主键
     * @return 结果
     */
    // @Override
    // public int deleteExecuteDeviceCurrentByTimestamp(Date timestamp)
    // {
    //     return executeDeviceCurrentMapper.deleteExecuteDeviceCurrentByTimestamp(timestamp);
    // }
}