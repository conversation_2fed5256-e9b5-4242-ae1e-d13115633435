<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.mgr.mapper.VppMgrAggBaseMapper">

    <resultMap type="VppMgrAggBase" id="VppMgrAggBaseResult">
        <result property="aggBaseId"    column="agg_base_id"    />
        <result property="aggName"    column="agg_name"    />
        <result property="aggCode"    column="agg_code"    />
        <result property="operator"    column="operator"    />
        <result property="userCount"    column="user_count"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="region"    column="region"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVppMgrAggBaseVo">
        select agg_base_id, agg_name, agg_code, operator, user_count, credit_code, region, contact, phone, img_url, dept_id, user_id, del_flag, create_by, create_time, update_by, update_time, remark from vpp_mgr_agg_base
    </sql>

    <select id="selectVppMgrAggBaseList" parameterType="VppMgrAggBase" resultMap="VppMgrAggBaseResult">
        <include refid="selectVppMgrAggBaseVo"/>
        <where>
            <if test="aggName != null  and aggName != ''"> and agg_name like concat('%', #{aggName}, '%')</if>
            <if test="aggCode != null  and aggCode != ''"> and agg_code = #{aggCode}</if>
            <if test="operator != null  and operator != ''"> and operator = #{operator}</if>
            <if test="userCount != null "> and user_count = #{userCount}</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="region != null  and region != ''"> and region = #{region}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="imgUrl != null  and imgUrl != ''"> and img_url = #{imgUrl}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectVppMgrAggBaseByAggBaseId" parameterType="Long" resultMap="VppMgrAggBaseResult">
        <include refid="selectVppMgrAggBaseVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertVppMgrAggBase" parameterType="VppMgrAggBase" useGeneratedKeys="true" keyProperty="aggBaseId">
        insert into vpp_mgr_agg_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aggName != null and aggName != ''">agg_name,</if>
            <if test="aggCode != null and aggCode != ''">agg_code,</if>
            <if test="operator != null and operator != ''">operator,</if>
            <if test="userCount != null">user_count,</if>
            <if test="creditCode != null and creditCode != ''">credit_code,</if>
            <if test="region != null and region != ''">region,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aggName != null and aggName != ''">#{aggName},</if>
            <if test="aggCode != null and aggCode != ''">#{aggCode},</if>
            <if test="operator != null and operator != ''">#{operator},</if>
            <if test="userCount != null">#{userCount},</if>
            <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
            <if test="region != null and region != ''">#{region},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVppMgrAggBase" parameterType="VppMgrAggBase">
        update vpp_mgr_agg_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="aggName != null and aggName != ''">agg_name = #{aggName},</if>
            <if test="aggCode != null and aggCode != ''">agg_code = #{aggCode},</if>
            <if test="operator != null and operator != ''">operator = #{operator},</if>
            <if test="userCount != null">user_count = #{userCount},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="region != null and region != ''">region = #{region},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where agg_base_id = #{aggBaseId}
    </update>

    <delete id="deleteVppMgrAggBaseByAggBaseId" parameterType="Long">
        delete from vpp_mgr_agg_base where agg_base_id = #{aggBaseId}
    </delete>

    <delete id="deleteVppMgrAggBaseByAggBaseIds" parameterType="String">
        delete from vpp_mgr_agg_base where agg_base_id in
        <foreach item="aggBaseId" collection="array" open="(" separator="," close=")">
            #{aggBaseId}
        </foreach>
    </delete>
</mapper>