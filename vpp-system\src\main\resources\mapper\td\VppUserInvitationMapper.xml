<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.td.mapper.VppUserInvitationMapper">
    <resultMap id="vppUserInvitationMap" type="com.vpp.td.domain.VppUserInvitation">
        <result column="user_invitation_id" property="userInvitationId"/>
        <result column="invitation_id" property="invitationId"/>
        <result column="user_name" property="userName"/>
        <result column="user_code" property="userCode"/>
        <result column="is_participate" property="isParticipate"/>
        <result column="offer_price" property="offerPrice"/>
        <result column="response_quantity" property="responseQuantity"/>
        <result column="max_regulate_power" property="maxRegulatePower"/>
        <result column="last_reply_time" property="lastReplyTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <select id="selectList" parameterType="VppUserInvitation" resultMap="vppUserInvitationMap">
        SELECT * FROM vpp_user_invitation
        <where>
            <if test="invitationId != null">AND invitation_id = #{invitationId}</if>
            <if test="userName != null and userName != ''">AND user_name LIKE CONCAT('%', #{userName}, '%')</if>
            <if test="isParticipate != null">AND is_participate = #{isParticipate}</if>
            <if test="delFlag == null or delFlag == ''">AND del_flag = '0'</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectById" parameterType="Long" resultMap="vppUserInvitationMap">
        SELECT * FROM vpp_user_invitation WHERE user_invitation_id = #{userInvitationId} AND del_flag = '0'
    </select>

    <insert id="insert" parameterType="VppUserInvitation" useGeneratedKeys="true" keyProperty="userInvitationId">
        INSERT INTO vpp_user_invitation (
            invitation_id, user_name, user_code, is_participate, offer_price, response_quantity,
            max_regulate_power, last_reply_time, create_by, create_time, update_by, update_time, del_flag
        ) VALUES (
                     #{invitationId}, #{userName}, #{userCode}, #{isParticipate}, #{offerPrice}, #{responseQuantity},
                     #{maxRegulatePower}, #{lastReplyTime}, #{createBy}, NOW(), #{updateBy}, NOW(), '0'
                 )
    </insert>

    <update id="update" parameterType="VppUserInvitation">
        UPDATE vpp_user_invitation
        <set>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="isParticipate != null">is_participate = #{isParticipate},</if>
            <if test="offerPrice != null">offer_price = #{offerPrice},</if>
            <if test="responseQuantity != null">response_quantity = #{responseQuantity},</if>
            <if test="maxRegulatePower != null">max_regulate_power = #{maxRegulatePower},</if>
            <if test="lastReplyTime != null">last_reply_time = #{lastReplyTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE user_invitation_id = #{userInvitationId} AND del_flag = '0'
    </update>

    <update id="deleteById" parameterType="Long">
        UPDATE vpp_user_invitation SET del_flag = '2' WHERE user_invitation_id = #{userInvitationId} AND del_flag = '0'
    </update>
</mapper>