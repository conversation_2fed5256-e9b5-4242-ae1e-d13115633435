package com.vpp.plan.mapper;

import com.vpp.plan.domain.PlanGenDevices;

import java.util.List;

/**
 * 自动生成计划设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface PlanGenDevicesMapper {
    /**
     * 查询自动生成计划设备
     *
     * @param planGenDevicesId 自动生成计划设备主键
     * @return 自动生成计划设备
     */
    public PlanGenDevices selectPlanGenDevicesByPlanGenDevicesId(Long planGenDevicesId);

    /**
     * 查询自动生成计划设备列表
     *
     * @param planGenDevices 自动生成计划设备
     * @return 自动生成计划设备集合
     */
    public List<PlanGenDevices> selectPlanGenDevicesList(PlanGenDevices planGenDevices);

    /**
     * 新增自动生成计划设备
     *
     * @param planGenDevices 自动生成计划设备
     * @return 结果
     */
    public int insertPlanGenDevices(PlanGenDevices planGenDevices);

    /**
     * 修改自动生成计划设备
     *
     * @param planGenDevices 自动生成计划设备
     * @return 结果
     */
    public int updatePlanGenDevices(PlanGenDevices planGenDevices);

    /**
     * 删除自动生成计划设备
     *
     * @param planGenDevicesId 自动生成计划设备主键
     * @return 结果
     */
    public int deletePlanGenDevicesByPlanGenDevicesId(Long planGenDevicesId);

    /**
     * 批量删除自动生成计划设备
     *
     * @param planGenDevicesIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanGenDevicesByPlanGenDevicesIds(Long[] planGenDevicesIds);
}
