<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.execute.mapper.ExecuteDeviceCurrentMapper">

    <resultMap type="ExecuteDeviceCurrent" id="ExecuteDeviceCurrentResult">
        <result property="timestamp"    column="timestamp"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceCurrent"    column="device_current"    />
    </resultMap>

    <sql id="selectExecuteDeviceCurrentVo">
        select timestamp, device_id, device_current from execute_device_current
    </sql>

    <select id="selectExecuteDeviceCurrentList" parameterType="ExecuteDeviceCurrent" resultMap="ExecuteDeviceCurrentResult">
        <include refid="selectExecuteDeviceCurrentVo"/>
        <where>
            <if test="deviceCurrent != null "> and device_current = #{deviceCurrent}</if>
        </where>
    </select>

    <select id="selectExecuteDeviceCurrentByTimestamp" parameterType="Date" resultMap="ExecuteDeviceCurrentResult">
        <include refid="selectExecuteDeviceCurrentVo"/>
        where timestamp = #{timestamp}
    </select>

    <insert id="insertExecuteDeviceCurrent" parameterType="ExecuteDeviceCurrent">
        insert into execute_device_current
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">timestamp,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="deviceCurrent != null">device_current,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">#{timestamp},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceCurrent != null">#{deviceCurrent},</if>
        </trim>
    </insert>

    <update id="updateExecuteDeviceCurrent" parameterType="ExecuteDeviceCurrent">
        update execute_device_current
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceCurrent != null">device_current = #{deviceCurrent},</if>
        </trim>
        where timestamp = #{timestamp}
    </update>

    <delete id="deleteExecuteDeviceCurrentByTimestamp" parameterType="Date">
        delete from execute_device_current where timestamp = #{timestamp}
    </delete>

    <delete id="deleteExecuteDeviceCurrentByTimestamps" parameterType="String">
        delete from execute_device_current where timestamp in
        <foreach item="timestamp" collection="array" open="(" separator="," close=")">
            #{timestamp}
        </foreach>
    </delete>
</mapper>