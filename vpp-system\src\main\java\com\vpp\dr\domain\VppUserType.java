package com.vpp.dr.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.mgr.domain.VppMgrUag;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 用户类型实体（存储用户类型编码和名称，如：光伏发电用户）
 */
@Data
public class VppUserType implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long typeId;

    /** 用户类型名称（如：光伏发电用户） */
    private String typeName;

    /** 用户类型编码（唯一标识，如：PV_USER） */
    private String typeCode;

    /** 父级类型ID（层级结构，如：0表示根节点） */
    private Long parentId;

    /** 排序顺序 */
    private Integer sortOrder;

    /** 关联聚合商用户 */
    private List<VppMgrUag> mgrUags;

    /**
     * 主键ID
     */
    private Long uagId;

    /**
     * 用户类型(单选) 01-光伏发电用户,02-风电发电用户,03-充电桩负荷用户,04-负荷类用户,05-储电类用户
     */
    @Excel(name = "用户类型(单选) 01-光伏发电用户,02-风电发电用户,03-充电桩负荷用户,04-负荷类用户,05-储电类用户")
    private String uagType;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String uagName;

    /**
     * 聚合商id(sys_user用户ID)
     */
    @Excel(name = "聚合商用户用户id(sys_user用户ID)")
    private Long userId;

    @Override
    public String toString() {
        return "VppUserType{" +
                "typeId=" + typeId +
                "uagId=" + uagId +
                "userId=" + userId +
                ", typeName='" + typeName + '\'' +
                ", typeCode='" + typeCode + '\'' +
                ", uagType='" + uagType + '\'' +
                ", uagName='" + uagName + '\'' +
                ", parentId=" + parentId +
                ", sortOrder=" + sortOrder +
                '}';
    }
}