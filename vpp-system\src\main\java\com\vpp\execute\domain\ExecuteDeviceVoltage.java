package com.vpp.execute.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 设备实时检测电压对象 execute_device_voltage
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public class ExecuteDeviceVoltage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Date timestamp;

    /**
     * 关联vpp_base_device表id
     */
    private Long deviceId;

    /**
     * 设备实时电压，每1分钟，或5分钟，或固定时间获取，有两种设备：
     * 1. 用电设备
     * 用电设备在用电时间内精确获取设备信息电压信息，实时计算功率
     * 2. 储发电设备
     * 发电时间内的发电数据
     * 储电和放电电压
     * 设备监控数据不连续，比如用电设备，需要根据用电设备精确计算用电功率，以及用电电量，预测明日用电电量，发电设备如风电，太阳能发电因为天气原因可能停机
     */
    @Excel(name = "设备实时电压")
    private Long deviceVoltage;

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceVoltage(Long deviceVoltage) {
        this.deviceVoltage = deviceVoltage;
    }

    public Long getDeviceVoltage() {
        return deviceVoltage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("timestamp", getTimestamp())
                .append("deviceId", getDeviceId())
                .append("deviceVoltage", getDeviceVoltage())
                .toString();
    }
}