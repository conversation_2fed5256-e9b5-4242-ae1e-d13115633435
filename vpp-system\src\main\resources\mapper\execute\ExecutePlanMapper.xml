<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.execute.mapper.ExecutePlanMapper">

    <resultMap type="ExecutePlan" id="ExecutePlanResult">
        <result property="executePlanId"    column="execute_plan_id"    />
        <result property="executePlanGenId"    column="execute_plan_gen_id"    />
        <result property="activityCode"    column="activity_code"    />
        <result property="vppId"    column="vpp_id"    />
        <result property="devicesTotal"    column="devices_total"    />
        <result property="ratedPower"    column="rated_power"    />
        <result property="ratedElectricity"    column="rated_electricity"    />
        <result property="executeStatus"    column="execute_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectExecutePlanVo">
        select execute_plan_id, execute_plan_gen_id, activity_code, vpp_id, devices_total, rated_power, rated_electricity, execute_status, create_by, create_time, update_by, update_time, remark from execute_plan
    </sql>

    <select id="selectExecutePlanList" parameterType="ExecutePlan" resultMap="ExecutePlanResult">
        <include refid="selectExecutePlanVo"/>
        <where>
            <if test="executePlanGenId != null "> and execute_plan_gen_id = #{executePlanGenId}</if>
            <if test="activityCode != null  and activityCode != ''"> and activity_code = #{activityCode}</if>
            <if test="vppId != null "> and vpp_id = #{vppId}</if>
            <if test="devicesTotal != null "> and devices_total = #{devicesTotal}</if>
            <if test="ratedPower != null "> and rated_power = #{ratedPower}</if>
            <if test="ratedElectricity != null "> and rated_electricity = #{ratedElectricity}</if>
            <if test="executeStatus != null  and executeStatus != ''"> and execute_status = #{executeStatus}</if>
        </where>
    </select>

    <select id="selectExecutePlanByExecutePlanId" parameterType="Long" resultMap="ExecutePlanResult">
        <include refid="selectExecutePlanVo"/>
        where execute_plan_id = #{executePlanId}
    </select>

    <insert id="insertExecutePlan" parameterType="ExecutePlan" useGeneratedKeys="true" keyProperty="executePlanId">
        insert into execute_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="executePlanGenId != null">execute_plan_gen_id,</if>
            <if test="activityCode != null">activity_code,</if>
            <if test="vppId != null">vpp_id,</if>
            <if test="devicesTotal != null">devices_total,</if>
            <if test="ratedPower != null">rated_power,</if>
            <if test="ratedElectricity != null">rated_electricity,</if>
            <if test="executeStatus != null">execute_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="executePlanGenId != null">#{executePlanGenId},</if>
            <if test="activityCode != null">#{activityCode},</if>
            <if test="vppId != null">#{vppId},</if>
            <if test="devicesTotal != null">#{devicesTotal},</if>
            <if test="ratedPower != null">#{ratedPower},</if>
            <if test="ratedElectricity != null">#{ratedElectricity},</if>
            <if test="executeStatus != null">#{executeStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateExecutePlan" parameterType="ExecutePlan">
        update execute_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="executePlanGenId != null">execute_plan_gen_id = #{executePlanGenId},</if>
            <if test="activityCode != null">activity_code = #{activityCode},</if>
            <if test="vppId != null">vpp_id = #{vppId},</if>
            <if test="devicesTotal != null">devices_total = #{devicesTotal},</if>
            <if test="ratedPower != null">rated_power = #{ratedPower},</if>
            <if test="ratedElectricity != null">rated_electricity = #{ratedElectricity},</if>
            <if test="executeStatus != null">execute_status = #{executeStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where execute_plan_id = #{executePlanId}
    </update>

    <delete id="deleteExecutePlanByExecutePlanId" parameterType="Long">
        delete from execute_plan where execute_plan_id = #{executePlanId}
    </delete>

    <delete id="deleteExecutePlanByExecutePlanIds" parameterType="String">
        delete from execute_plan where execute_plan_id in
        <foreach item="executePlanId" collection="array" open="(" separator="," close=")">
            #{executePlanId}
        </foreach>
    </delete>
</mapper>