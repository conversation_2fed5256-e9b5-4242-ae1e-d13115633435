package com.vpp.mgr.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Transient;

import java.util.Date;
import java.util.List;

/**
 * 合同(聚合商-聚合用户)对象 vpp_mgr_cnt
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VppMgrCnt extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long cntId;

    /**
     * 合同名称
     */
    @Excel(name = "合同名称")
    private String cntName;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号")
    private String cntNum;

    /**
     * 代理用户
     */
    @Excel(name = "代理用户")
    private String proxyUser;

    /**
     * 电话
     */
    @Excel(name = "电话")
    private String phone;

    /**
     * 合同类型(01-全部,02-资源代理合同.03-购售电合同,04-其他合同)
     */
    @Excel(name = "合同类型(01-全部,02-资源代理合同.03-购售电合同,04-其他合同)")
    private String cntType;

    /**
     * 合同状态(01-已签订,02-未签订)
     */
    @Excel(name = "合同状态(01-已签订,02-未签订)")
    private String cntStattus;

    /**
     * 合同结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cntEndTime;

    /**
     * 合同开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cntStartTime;

    /**
     * 合同签署时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同签署时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cntSign;

    /**
     * 部门(机构)ID
     */
    @Excel(name = "部门(机构)ID")
    private Long deptId;

    /**
     * 用户ID(sys_user)
     */
    @Excel(name = "用户ID(sys_user)")
    private Long userId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Transient
    private List<VppMgrCntAtt> vppMgrCntAttList;

    private Long uagId;//根据uagid查找所有关联的合同
}