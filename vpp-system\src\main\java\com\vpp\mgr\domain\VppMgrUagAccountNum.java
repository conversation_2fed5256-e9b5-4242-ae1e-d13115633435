package com.vpp.mgr.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 聚合商用户-户号 关联对象 vpp_mgr_uag_account_num
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode
public class VppMgrUagAccountNum implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 聚合商用户用户机构id
     */
    private Long deptId;

    /**
     * 用户户号
     */
    @Excel(name = "用户户号")
    private String uagAccountNum;

}