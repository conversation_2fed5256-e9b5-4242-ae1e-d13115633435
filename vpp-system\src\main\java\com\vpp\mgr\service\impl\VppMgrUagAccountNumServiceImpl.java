package com.vpp.mgr.service.impl;

import com.vpp.mgr.domain.VppMgrUagAccountNum;
import com.vpp.mgr.mapper.VppMgrUagAccountNumMapper;
import com.vpp.mgr.service.IVppMgrUagAccountNumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 聚合商用户-户号 关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class VppMgrUagAccountNumServiceImpl implements IVppMgrUagAccountNumService {
    @Autowired
    private VppMgrUagAccountNumMapper vppMgrUagAccountNumMapper;

    /**
     * 查询聚合商用户-户号 关联
     *
     * @param userId 聚合商用户-户号 关联主键
     * @return 聚合商用户-户号 关联
     */
    @Override
    public VppMgrUagAccountNum selectVppMgrUagAccountNumByUserId(Long userId) {
        return vppMgrUagAccountNumMapper.selectVppMgrUagAccountNumByUserId(userId);
    }

    /**
     * 查询聚合商用户-户号 关联列表
     *
     * @param vppMgrUagAccountNum 聚合商用户-户号 关联
     * @return 聚合商用户-户号 关联
     */
    @Override
    public List<VppMgrUagAccountNum> selectVppMgrUagAccountNumList(VppMgrUagAccountNum vppMgrUagAccountNum) {
        return vppMgrUagAccountNumMapper.selectVppMgrUagAccountNumList(vppMgrUagAccountNum);
    }

    /**
     * 新增聚合商用户-户号 关联
     *
     * @param vppMgrUagAccountNum 聚合商用户-户号 关联
     * @return 结果
     */
    @Override
    public int insertVppMgrUagAccountNum(VppMgrUagAccountNum vppMgrUagAccountNum) {
        return vppMgrUagAccountNumMapper.insertVppMgrUagAccountNum(vppMgrUagAccountNum);
    }

    /**
     * 修改聚合商用户-户号 关联
     *
     * @param vppMgrUagAccountNum 聚合商用户-户号 关联
     * @return 结果
     */
    @Override
    public int updateVppMgrUagAccountNum(VppMgrUagAccountNum vppMgrUagAccountNum) {
        return vppMgrUagAccountNumMapper.updateVppMgrUagAccountNum(vppMgrUagAccountNum);
    }

    /**
     * 批量删除聚合商用户-户号 关联
     *
     * @param userIds 需要删除的聚合商用户-户号 关联主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrUagAccountNumByUserIds(Long[] userIds) {
        return vppMgrUagAccountNumMapper.deleteVppMgrUagAccountNumByUserIds(userIds);
    }

    /**
     * 删除聚合商用户-户号 关联信息
     *
     * @param userId 聚合商用户-户号 关联主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrUagAccountNumByUserId(Long userId) {
        return vppMgrUagAccountNumMapper.deleteVppMgrUagAccountNumByDeptId(userId);
    }

    //======================================================
    @Override
    public List<VppMgrUagAccountNum> selectByDeptId(Long deptId) {
        return vppMgrUagAccountNumMapper.selectByDeptId(deptId);
    }
}