package com.vpp.tracking.domain;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

@Data
public class TrackingEvent {
    private Long id;
    private String name;//名称
    private String no;//编号
    private LocalDate requirementDate;//响应日
    private String requirementTime;//开始时间
    private String province;//省份
    private String directive;//方向，填谷还是削峰
    private LocalDate date;//开始执行日
    private LocalTime begin;//开始执行时间
    private LocalTime end;//结束时间
    private String type;//类型
    private String from;//来源

}
