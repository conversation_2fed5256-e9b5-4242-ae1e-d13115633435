# 附件5：测试环境配置文档

## 泛物云虚拟电厂综合系统测试环境配置文档

### 文档信息
- 文档版本：V1.0
- 编制日期：2025年8月21日
- 编制人员：测试团队
- 适用范围：泛物云虚拟电厂综合系统测试环境

## 1. 测试环境架构

### 1.1 整体架构图
```
[负载均衡器] → [Web服务器] → [应用服务器集群] → [数据库服务器]
     ↓              ↓              ↓                ↓
   Nginx         Nginx        Spring Boot        MySQL
  (1台)         (1台)          (2台)            (1台)
                                  ↓
                              [缓存服务器]
                                  ↓
                                Redis
                               (1台)
```

### 1.2 网络拓扑
- 测试网段：*************/24
- 管理网段：*************/24
- 外网访问：通过NAT网关

## 2. 硬件环境配置

### 2.1 服务器硬件配置清单

#### 2.1.1 负载均衡器
| 配置项 | 规格 |
|--------|------|
| 服务器型号 | Dell PowerEdge R230 |
| CPU | Intel Xeon E3-1220 v6 4核 3.0GHz |
| 内存 | 8GB DDR4 ECC |
| 硬盘 | 500GB SSD |
| 网卡 | 双千兆网卡 |
| IP地址 | ************** |

#### 2.1.2 Web服务器
| 配置项 | 规格 |
|--------|------|
| 服务器型号 | Dell PowerEdge R240 |
| CPU | Intel Xeon E-2224 4核 3.4GHz |
| 内存 | 16GB DDR4 ECC |
| 硬盘 | 500GB SSD |
| 网卡 | 双千兆网卡 |
| IP地址 | ************** |

#### 2.1.3 应用服务器集群
**应用服务器1**
| 配置项 | 规格 |
|--------|------|
| 服务器型号 | Dell PowerEdge R340 |
| CPU | Intel Xeon E-2234 4核 3.6GHz |
| 内存 | 32GB DDR4 ECC |
| 硬盘 | 1TB SSD |
| 网卡 | 双千兆网卡 |
| IP地址 | ************** |

**应用服务器2**
| 配置项 | 规格 |
|--------|------|
| 服务器型号 | Dell PowerEdge R340 |
| CPU | Intel Xeon E-2234 4核 3.6GHz |
| 内存 | 32GB DDR4 ECC |
| 硬盘 | 1TB SSD |
| 网卡 | 双千兆网卡 |
| IP地址 | ************** |

#### 2.1.4 数据库服务器
| 配置项 | 规格 |
|--------|------|
| 服务器型号 | Dell PowerEdge R440 |
| CPU | Intel Xeon Silver 4214 12核 2.2GHz |
| 内存 | 64GB DDR4 ECC |
| 硬盘 | 2TB SSD RAID1 |
| 网卡 | 双千兆网卡 |
| IP地址 | ************** |

#### 2.1.5 缓存服务器
| 配置项 | 规格 |
|--------|------|
| 服务器型号 | Dell PowerEdge R230 |
| CPU | Intel Xeon E3-1230 v6 4核 3.5GHz |
| 内存 | 16GB DDR4 ECC |
| 硬盘 | 500GB SSD |
| 网卡 | 双千兆网卡 |
| IP地址 | ************** |

### 2.2 网络设备配置

#### 2.2.1 核心交换机
| 配置项 | 规格 |
|--------|------|
| 设备型号 | Cisco Catalyst 2960-X |
| 端口数量 | 48个千兆端口 |
| 上联端口 | 4个万兆SFP+端口 |
| 管理IP | ************* |

#### 2.2.2 防火墙
| 配置项 | 规格 |
|--------|------|
| 设备型号 | SonicWall TZ570 |
| 吞吐量 | 1.5Gbps |
| 并发连接数 | 150,000 |
| 管理IP | ************* |

## 3. 软件环境配置

### 3.1 操作系统配置

#### 3.1.1 Linux服务器（CentOS 7.9）
**适用服务器：应用服务器、数据库服务器、缓存服务器**

| 配置项 | 配置值 |
|--------|--------|
| 操作系统 | CentOS Linux release 7.9.2009 |
| 内核版本 | 3.10.0-1160.el7.x86_64 |
| 时区设置 | Asia/Shanghai |
| 字符编码 | UTF-8 |
| 防火墙 | firewalld（已配置规则） |
| SELinux | Disabled |

**系统优化配置：**
```bash
# 内核参数优化
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
echo 'net.core.netdev_max_backlog = 32768' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_timestamps = 0' >> /etc/sysctl.conf

# 文件描述符限制
echo '* soft nofile 65535' >> /etc/security/limits.conf
echo '* hard nofile 65535' >> /etc/security/limits.conf
```

#### 3.1.2 Windows服务器（Windows Server 2019）
**适用服务器：负载均衡器、Web服务器**

| 配置项 | 配置值 |
|--------|--------|
| 操作系统 | Windows Server 2019 Standard |
| 版本号 | 1809 (OS Build 17763.3650) |
| 时区设置 | (UTC+08:00) 北京，重庆，香港特别行政区，乌鲁木齐 |
| Windows Update | 已安装最新补丁 |
| Windows Defender | 已启用并配置排除项 |

### 3.2 数据库环境配置

#### 3.2.1 MySQL 8.0.33配置
**安装路径：** /usr/local/mysql
**数据目录：** /var/lib/mysql
**配置文件：** /etc/my.cnf

**主要配置参数：**
```ini
[mysqld]
port = 3306
bind-address = **************
max_connections = 1000
innodb_buffer_pool_size = 32G
innodb_log_file_size = 2G
innodb_flush_log_at_trx_commit = 1
sync_binlog = 1
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
default-time-zone = '+8:00'
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 安全配置
skip-name-resolve
local-infile = 0
secure-file-priv = /var/lib/mysql-files/

# 性能优化
query_cache_type = 1
query_cache_size = 256M
tmp_table_size = 256M
max_heap_table_size = 256M
```

**用户权限配置：**
```sql
-- 应用连接用户
CREATE USER 'vpp_app'@'192.168.100.%' IDENTIFIED BY 'VppApp@2025!';
GRANT SELECT, INSERT, UPDATE, DELETE ON fiotcp_vpp.* TO 'vpp_app'@'192.168.100.%';

-- 只读用户（用于报表查询）
CREATE USER 'vpp_readonly'@'192.168.100.%' IDENTIFIED BY 'VppRead@2025!';
GRANT SELECT ON fiotcp_vpp.* TO 'vpp_readonly'@'192.168.100.%';
```

#### 3.2.2 数据库备份配置
**备份策略：**
- 全量备份：每日凌晨2:00
- 增量备份：每4小时一次
- 备份保留：30天

**备份脚本：**
```bash
#!/bin/bash
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u backup_user -p'BackupPass@2025!' --single-transaction --routines --triggers fiotcp_vpp > $BACKUP_DIR/fiotcp_vpp_$DATE.sql
```

### 3.3 应用服务器环境配置

#### 3.3.1 Java运行环境
**JDK版本：** OpenJDK 1.8.0_372
**安装路径：** /usr/local/java/jdk1.8.0_372
**环境变量：**
```bash
export JAVA_HOME=/usr/local/java/jdk1.8.0_372
export JRE_HOME=$JAVA_HOME/jre
export PATH=$JAVA_HOME/bin:$PATH
export CLASSPATH=.:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar
```

#### 3.3.2 Spring Boot应用配置
**应用部署路径：** /opt/vpp-system
**配置文件路径：** /opt/vpp-system/config
**日志路径：** /var/log/vpp-system

**JVM参数配置：**
```bash
JAVA_OPTS="-Xms4g -Xmx4g -XX:NewRatio=1 -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:+CMSParallelRemarkEnabled -XX:+UseCMSCompactAtFullCollection -XX:CMSFullGCsBeforeCompaction=0 -XX:+CMSClassUnloadingEnabled -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=80 -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+PrintClassHistogram -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintHeapAtGC -Xloggc:/var/log/vpp-system/gc.log"
```

**应用配置文件（application-test.yml）：**
```yaml
server:
  port: 9801
  servlet:
    context-path: /

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      master:
        url: ********************************************************************************************************************************************************
        username: vpp_app
        password: VppApp@2025!
      initialSize: 10
      minIdle: 20
      maxActive: 100
      maxWait: 60000

  redis:
    host: **************
    port: 6379
    password: Redis@2025!
    database: 0
    timeout: 10s
    lettuce:
      pool:
        min-idle: 5
        max-idle: 20
        max-active: 50
        max-wait: -1ms

logging:
  level:
    com.vpp: debug
  file:
    name: /var/log/vpp-system/application.log
  pattern:
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
```

### 3.4 缓存服务器环境配置

#### 3.4.1 Redis 6.2.7配置
**安装路径：** /usr/local/redis
**配置文件：** /etc/redis/redis.conf
**数据目录：** /var/lib/redis

**主要配置参数：**
```conf
bind **************
port 6379
requirepass Redis@2025!
maxmemory 8gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
```

### 3.5 Web服务器环境配置

#### 3.5.1 Nginx 1.20.2配置
**安装路径：** /usr/local/nginx
**配置文件：** /etc/nginx/nginx.conf
**日志路径：** /var/log/nginx

**主要配置：**
```nginx
upstream vpp_backend {
    server **************:9801 weight=1 max_fails=3 fail_timeout=30s;
    server **************:9801 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name test-vpp.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name test-vpp.example.com;
    
    ssl_certificate /etc/nginx/ssl/vpp.crt;
    ssl_certificate_key /etc/nginx/ssl/vpp.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    
    location / {
        proxy_pass http://vpp_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static/ {
        root /var/www/vpp-ui;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
```

## 4. 测试工具环境配置

### 4.1 性能测试工具

#### 4.1.1 Apache JMeter 5.5
**安装路径：** /opt/jmeter
**配置文件：** /opt/jmeter/bin/jmeter.properties

**主要配置：**
```properties
# 内存配置
HEAP="-Xms1g -Xmx4g -XX:MaxMetaspaceSize=256m"

# 结果文件配置
jmeter.save.saveservice.output_format=xml
jmeter.save.saveservice.response_data=false
jmeter.save.saveservice.samplerData=false
jmeter.save.saveservice.requestHeaders=false
jmeter.save.saveservice.responseHeaders=false
```

#### 4.1.2 测试数据准备
**测试数据路径：** /opt/testdata
**数据文件清单：**
- users.csv：1000条用户测试数据
- aggregators.csv：50条聚合商测试数据
- devices.csv：5000条设备测试数据
- transactions.csv：10000条交易测试数据

### 4.2 安全测试工具

#### 4.2.1 OWASP ZAP 2.12.0
**安装路径：** /opt/zaproxy
**配置文件：** /home/<USER>/.ZAP/config.xml

#### 4.2.2 Nessus Professional
**安装路径：** /opt/nessus
**Web界面：** https://192.168.100.60:8834

### 4.3 自动化测试工具

#### 4.3.1 Selenium Grid
**Hub节点：** 192.168.100.70:4444
**Chrome节点：** 192.168.100.71:5555
**Firefox节点：** 192.168.100.72:5555

## 5. 监控和日志配置

### 5.1 系统监控

#### 5.1.1 Prometheus + Grafana
**Prometheus：** http://192.168.100.80:9090
**Grafana：** http://192.168.100.80:3000

#### 5.1.2 监控指标
- 系统资源：CPU、内存、磁盘、网络
- 应用性能：响应时间、吞吐量、错误率
- 数据库性能：连接数、查询时间、锁等待
- 缓存性能：命中率、内存使用、连接数

### 5.2 日志管理

#### 5.2.1 ELK Stack
**Elasticsearch：** http://**************:9200
**Logstash：** **************:5044
**Kibana：** http://**************:5601

#### 5.2.2 日志收集配置
- 应用日志：/var/log/vpp-system/
- 系统日志：/var/log/messages
- 访问日志：/var/log/nginx/access.log
- 错误日志：/var/log/nginx/error.log

## 6. 安全配置

### 6.1 网络安全

#### 6.1.1 防火墙规则
```bash
# 允许SSH（仅管理网段）
firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='*************/24' port protocol='tcp' port='22' accept"

# 允许HTTP/HTTPS
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https

# 允许应用端口（仅内网）
firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='*************/24' port protocol='tcp' port='9801' accept"
```

#### 6.1.2 SSL证书配置
**证书类型：** RSA 2048位
**有效期：** 2025-01-01 至 2026-01-01
**证书路径：** /etc/nginx/ssl/

### 6.2 访问控制

#### 6.2.1 用户账户管理
**系统管理员：** admin
**应用管理员：** vpp-admin
**测试用户：** tester

#### 6.2.2 SSH密钥认证
**密钥类型：** RSA 4096位
**密钥路径：** /home/<USER>/.ssh/

## 7. 备份和恢复

### 7.1 数据备份策略
- 数据库备份：每日全量备份，每4小时增量备份
- 配置文件备份：每周备份
- 应用程序备份：版本发布时备份

### 7.2 恢复测试
- 数据库恢复测试：每月执行
- 系统恢复测试：每季度执行
- 灾难恢复演练：每年执行

## 8. 环境维护

### 8.1 定期维护任务
- 系统补丁更新：每月第二个周末
- 安全扫描：每周执行
- 性能监控：实时监控
- 日志清理：每周清理30天前的日志

### 8.2 变更管理
- 环境变更需要提交变更申请
- 重要变更需要在维护窗口执行
- 所有变更需要记录和备份

## 9. 故障处理

### 9.1 常见故障及处理方法
1. **应用无法启动**：检查JVM参数、端口占用、配置文件
2. **数据库连接失败**：检查网络连通性、用户权限、连接池配置
3. **性能问题**：检查系统资源、数据库性能、缓存命中率
4. **安全告警**：检查访问日志、系统日志、安全扫描结果

### 9.2 应急联系方式
- 系统管理员：张三（13800138001）
- 数据库管理员：李四（13800138002）
- 网络管理员：王五（13800138003）
- 安全管理员：赵六（13800138004）
