package com.vpp.re.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 运行概览表实体类（实现Serializable）
 */
@Data
@ToString
@ApiModel(value = "运行概览表实体", description = "聚合响应执行追踪对应的实体类")
public class VppExchangeOperationOverview implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "运行概览主键ID", example = "1")
    private Long overviewId;

    @ApiModelProperty(value = "事件名称（如：龙腾响应计划）", example = "龙腾响应计划-20250727")
    private String eventName;

    @ApiModelProperty(value = "持续时间（分钟）", example = "60")
    private Integer durationMinutes;

    @ApiModelProperty(value = "爬坡率 (%/min)", example = "5.00")
    private BigDecimal rampRate;

    @ApiModelProperty(value = "响应偏差率 (%)", example = "2.50")
    private BigDecimal responseDeviationRate;

    @ApiModelProperty(value = "响应完成率 (%)", example = "95.00")
    private BigDecimal responseCompletionRate;

    @ApiModelProperty(value = "响应速度 (KW/min)", example = "100.00")
    private BigDecimal responseSpeed;

    @ApiModelProperty(value = "实际容量 (KW)", example = "500.00")
    private BigDecimal actualCapacityKw;

    @ApiModelProperty(value = "中标容量 (KW)", example = "450.00")
    private BigDecimal bidWinCapacityKw;

    @ApiModelProperty(value = "向上调节能力 (KW)", example = "300.00")
    private BigDecimal upwardRegulationCapabilityKw;

    @ApiModelProperty(value = "总响应次数", example = "10")
    private Integer totalResponseCount;

    @ApiModelProperty(value = "关联邀约计划ID", example = "44")
    private Long invitationId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "统计数据时间", example = "2025-07-27 10:00:00")
    private LocalDateTime statisticsTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "2025-07-27 10:00:00")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间", example = "2025-07-27 10:30:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志（0-未删除，2-已删除）", example = "0")
    private String delFlag;
}