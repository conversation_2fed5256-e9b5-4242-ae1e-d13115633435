package com.vpp.dr.service.impl;

import com.vpp.dr.domain.VppUserType;
import com.vpp.dr.mapper.VppUserTypeMapper;
import com.vpp.dr.service.IVppUserTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class VppUserTypeServiceImpl implements IVppUserTypeService {

    @Autowired
    private VppUserTypeMapper userTypeMapper;

    @Override
    public List<VppUserType> getAllUserTypes() {
        return userTypeMapper.selectAll();
    }

    @Override
    public VppUserType getUserTypeById(Long id) {
        return userTypeMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addUserType(VppUserType userType) {
        // 校验编码唯一性（示例）
        VppUserType existType = userTypeMapper.selectByTypeCode(userType.getTypeCode());
        if (existType != null) {
            throw new RuntimeException("用户类型编码已存在：" + userType.getTypeCode());
        }
        return userTypeMapper.insert(userType) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserType(VppUserType userType) {
        // 校验编码唯一性（排除自身）
        VppUserType existType = userTypeMapper.selectByTypeCode(userType.getTypeCode());
        if (existType != null && !existType.getTypeId().equals(userType.getTypeId())) {
            throw new RuntimeException("用户类型编码已存在：" + userType.getTypeCode());
        }
        return userTypeMapper.update(userType) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUserType(Long id) {
        return userTypeMapper.deleteById(id) > 0;
    }

    @Override
    public VppUserType queryUsersByUserType(Long userId) {
        return userTypeMapper.queryUsersByUserType(userId);
    }
}