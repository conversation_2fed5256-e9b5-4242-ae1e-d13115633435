package com.vpp.web.controller.execute;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.execute.domain.ExecuteDeviceVoltage;
import com.vpp.execute.service.IExecuteDeviceVoltageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 设备实时检测电压Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/execute/deviceVoltage")
public class ExecuteDeviceVoltageController extends BaseController {
    @Autowired
    private IExecuteDeviceVoltageService executeDeviceVoltageService;

    /**
     * 查询设备实时检测电压列表
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceVoltage:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExecuteDeviceVoltage executeDeviceVoltage) {
        startPage();
        List<ExecuteDeviceVoltage> list = executeDeviceVoltageService.selectExecuteDeviceVoltageList(executeDeviceVoltage);
        return getDataTable(list);
    }

    /**
     * 导出设备实时检测电压列表
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceVoltage:export')")
    @Log(title = "设备实时检测电压", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExecuteDeviceVoltage executeDeviceVoltage) {
        List<ExecuteDeviceVoltage> list = executeDeviceVoltageService.selectExecuteDeviceVoltageList(executeDeviceVoltage);
        ExcelUtil<ExecuteDeviceVoltage> util = new ExcelUtil<ExecuteDeviceVoltage>(ExecuteDeviceVoltage.class);
        util.exportExcel(response, list, "设备实时检测电压数据");
    }

    /**
     * 获取设备实时检测电压详细信息
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceVoltage:query')")
    @GetMapping(value = "/{timestamp}")
    public AjaxResult getInfo(@PathVariable("timestamp") Date timestamp) {
        return success(executeDeviceVoltageService.selectExecuteDeviceVoltageByTimestamp(timestamp));
    }

    /**
     * 新增设备实时检测电压
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceVoltage:add')")
    @Log(title = "设备实时检测电压", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExecuteDeviceVoltage executeDeviceVoltage) {
        return toAjax(executeDeviceVoltageService.insertExecuteDeviceVoltage(executeDeviceVoltage));
    }

    /**
     * 修改设备实时检测电压
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceVoltage:edit')")
    @Log(title = "设备实时检测电压", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExecuteDeviceVoltage executeDeviceVoltage) {
        return toAjax(executeDeviceVoltageService.updateExecuteDeviceVoltage(executeDeviceVoltage));
    }

    /**
     * 删除设备实时检测电压
     */
    @PreAuthorize("@ss.hasPermi('execute:deviceVoltage:remove')")
    @Log(title = "设备实时检测电压", businessType = BusinessType.DELETE)
    @DeleteMapping("/{timestamps}")
    public AjaxResult remove(@PathVariable Date[] timestamps) {
        return toAjax(executeDeviceVoltageService.deleteExecuteDeviceVoltageByTimestamps(timestamps));
    }
}