<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.re.mapper.VppEventScoreMapper">

    <!-- 结果集映射（数据库列 → Entity属性） -->
    <resultMap id="BaseResultMap" type="com.vpp.re.domain.VppEventScore">
        <id column="item_id" jdbcType="BIGINT" property="itemId" /> <!-- 主键 -->
        <result column="invitation_id" jdbcType="BIGINT" property="invitationId" /> <!-- 邀约计划ID -->
        <result column="composite_grade" jdbcType="VARCHAR" property="compositeGrade" /> <!-- 综合等级 -->
        <result column="composite_score" jdbcType="DECIMAL" property="compositeScore" /> <!-- 综合得分 -->
        <result column="score_time" jdbcType="TIMESTAMP" property="scoreTime" /> <!-- 评分时间 -->
        <result column="operator_id" jdbcType="BIGINT" property="operatorId" /> <!-- 操作人ID -->
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName" /> <!-- 操作人姓名 -->
        <result column="item_name" jdbcType="VARCHAR" property="itemName" /> <!-- 项目名称 -->
        <result column="unit" jdbcType="VARCHAR" property="unit" /> <!-- 单位 -->
        <result column="target_value" jdbcType="DECIMAL" property="targetValue" /> <!-- 目标量 -->
        <result column="actual_value" jdbcType="DECIMAL" property="actualValue" /> <!-- 实际量 -->
        <result column="completion_rate" jdbcType="DECIMAL" property="completionRate" /> <!-- 完成率 -->
        <result column="grade" jdbcType="VARCHAR" property="grade" /> <!-- 评分等级 -->
        <result column="grade_rule" jdbcType="VARCHAR" property="gradeRule" /> <!-- 评分规则 -->
        <result column="item_time" jdbcType="TIMESTAMP" property="itemTime" /> <!-- 明细评分时间 -->
        <result column="create_by" jdbcType="VARCHAR" property="createBy" /> <!-- 创建者 -->
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" /> <!-- 创建时间 -->
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" /> <!-- 更新者 -->
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" /> <!-- 更新时间 -->
        <result column="del_flag" jdbcType="CHAR" property="delFlag" /> <!-- 删除标志 -->
    </resultMap>

    <!-- 插入评分记录 -->
    <insert id="insert" parameterType="com.vpp.re.domain.VppEventScore">
        INSERT INTO vpp_event_score (
            invitation_id, composite_grade, composite_score, score_time,
            operator_id, operator_name, item_name, unit, target_value,
            actual_value, completion_rate, grade, grade_rule, item_time,
            create_by, create_time, update_by, update_time, del_flag
        ) VALUES (
                     #{invitationId}, #{compositeGrade}, #{compositeScore}, #{scoreTime},
                     #{operatorId}, #{operatorName}, #{itemName}, #{unit}, #{targetValue},
                     #{actualValue}, #{completionRate}, #{grade}, #{gradeRule}, #{itemTime},
                     #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{delFlag}
                 )
    </insert>

    <!-- 根据ID更新评分记录 -->
    <update id="updateById" parameterType="com.vpp.re.domain.VppEventScore">
        UPDATE vpp_event_score
        SET
            invitation_id = #{invitationId},
            composite_grade = #{compositeGrade},
            composite_score = #{compositeScore},
            score_time = #{scoreTime},
            operator_id = #{operatorId},
            operator_name = #{operatorName},
            item_name = #{itemName},
            unit = #{unit},
            target_value = #{targetValue},
            actual_value = #{actualValue},
            completion_rate = #{completionRate},
            grade = #{grade},
            grade_rule = #{gradeRule},
            item_time = #{itemTime},
            update_by = #{updateBy},
            update_time = #{updateTime},
            del_flag = #{delFlag}
        WHERE item_id = #{itemId}
    </update>

    <!-- 根据ID删除评分记录（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE vpp_event_score
        SET del_flag = '2' -- 标记为已删除
        WHERE item_id = #{itemId}
    </update>

    <!-- 根据ID查询评分记录（未删除） -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT * FROM vpp_event_score
        WHERE item_id = #{itemId}
          AND del_flag = '0' -- 过滤未删除记录
    </select>

    <!-- 根据邀约计划ID分页查询评分记录（未删除） -->
    <select id="selectByInvitationId" parameterType="map" resultMap="BaseResultMap">
        SELECT * FROM vpp_event_score
        WHERE invitation_id = #{invitationId}
          AND del_flag = '0' -- 过滤未删除记录
        ORDER BY create_time DESC -- 按创建时间倒序
            LIMIT #{offset}, #{limit} -- 分页偏移量和数量
    </select>

</mapper>