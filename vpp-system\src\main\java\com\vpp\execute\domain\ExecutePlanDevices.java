package com.vpp.execute.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 执行计划关联的设备对象 execute_plan_devices
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public class ExecutePlanDevices extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id，
     */
    private Long executePlanDevicesId;

    /**
     * 关联vpp_base_device表设备id
     */
    @Excel(name = "关联vpp_base_device表设备id")
    private Long deviceId;

    /**
     * 额定电流
     */
    @Excel(name = "额定电流")
    private Long deviceRatedCurrent;

    /**
     * 额定功率
     */
    @Excel(name = "额定功率")
    private Long deviceRatedPower;

    /**
     * 额定电压
     */
    @Excel(name = "额定电压")
    private Long deviceRatedVoltage;

    public void setExecutePlanDevicesId(Long executePlanDevicesId) {
        this.executePlanDevicesId = executePlanDevicesId;
    }

    public Long getExecutePlanDevicesId() {
        return executePlanDevicesId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceRatedCurrent(Long deviceRatedCurrent) {
        this.deviceRatedCurrent = deviceRatedCurrent;
    }

    public Long getDeviceRatedCurrent() {
        return deviceRatedCurrent;
    }

    public void setDeviceRatedPower(Long deviceRatedPower) {
        this.deviceRatedPower = deviceRatedPower;
    }

    public Long getDeviceRatedPower() {
        return deviceRatedPower;
    }

    public void setDeviceRatedVoltage(Long deviceRatedVoltage) {
        this.deviceRatedVoltage = deviceRatedVoltage;
    }

    public Long getDeviceRatedVoltage() {
        return deviceRatedVoltage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("executePlanDevicesId", getExecutePlanDevicesId())
                .append("deviceId", getDeviceId())
                .append("deviceRatedCurrent", getDeviceRatedCurrent())
                .append("deviceRatedPower", getDeviceRatedPower())
                .append("deviceRatedVoltage", getDeviceRatedVoltage())
                .toString();
    }
}