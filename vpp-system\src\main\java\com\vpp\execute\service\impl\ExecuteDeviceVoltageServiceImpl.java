package com.vpp.execute.service.impl;

import com.vpp.execute.domain.ExecuteDeviceVoltage;
import com.vpp.execute.mapper.ExecuteDeviceVoltageMapper;
import com.vpp.execute.service.IExecuteDeviceVoltageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 设备实时检测电压Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class ExecuteDeviceVoltageServiceImpl implements IExecuteDeviceVoltageService {
    @Autowired
    private ExecuteDeviceVoltageMapper executeDeviceVoltageMapper;

    /**
     * 查询设备实时检测电压
     *
     * @param timestamp 设备实时检测电压主键
     * @return 设备实时检测电压
     */
    @Override
    public ExecuteDeviceVoltage selectExecuteDeviceVoltageByTimestamp(Date timestamp) {
        return executeDeviceVoltageMapper.selectExecuteDeviceVoltageByTimestamp(timestamp);
    }

    /**
     * 查询设备实时检测电压列表
     *
     * @param executeDeviceVoltage 设备实时检测电压
     * @return 设备实时检测电压
     */
    @Override
    public List<ExecuteDeviceVoltage> selectExecuteDeviceVoltageList(ExecuteDeviceVoltage executeDeviceVoltage) {
        return executeDeviceVoltageMapper.selectExecuteDeviceVoltageList(executeDeviceVoltage);
    }

    /**
     * 新增设备实时检测电压
     *
     * @param executeDeviceVoltage 设备实时检测电压
     * @return 结果
     */
    @Override
    public int insertExecuteDeviceVoltage(ExecuteDeviceVoltage executeDeviceVoltage) {
        return executeDeviceVoltageMapper.insertExecuteDeviceVoltage(executeDeviceVoltage);
    }

    /**
     * 修改设备实时检测电压
     *
     * @param executeDeviceVoltage 设备实时检测电压
     * @return 结果
     */
    @Override
    public int updateExecuteDeviceVoltage(ExecuteDeviceVoltage executeDeviceVoltage) {
        return executeDeviceVoltageMapper.updateExecuteDeviceVoltage(executeDeviceVoltage);
    }

    /**
     * 批量删除设备实时检测电压
     *
     * @param timestamps 需要删除的设备实时检测电压主键
     * @return 结果
     */
    @Override
    public int deleteExecuteDeviceVoltageByTimestamps(Date[] timestamps) {
        return executeDeviceVoltageMapper.deleteExecuteDeviceVoltageByTimestamps(timestamps);
    }

    /**
     * 删除设备实时检测电压信息
     *
     * @param timestamp 设备实时检测电压主键
     * @return 结果
     */
    @Override
    public int deleteExecuteDeviceVoltageByTimestamp(Date timestamp) {
        return executeDeviceVoltageMapper.deleteExecuteDeviceVoltageByTimestamp(timestamp);
    }
}