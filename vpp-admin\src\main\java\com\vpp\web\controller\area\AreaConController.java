package com.vpp.web.controller.area;

import com.vpp.area.domain.SysAddrProvince;
import com.vpp.area.domain.vo.SysTreeAreaVo;
import com.vpp.area.service.AreaConService;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.redis.RedisCache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * packageName com.vpp.web.controller.area
 *
 * <AUTHOR>
 * @version JDK 8
 * @className AreaConController
 * @date 2025/6/4
 * @description
 */
@RestController
@RequestMapping("/addr")
@Api(tags = "省市区接口")
public class AreaConController extends BaseController {
    @Autowired
    private AreaConService areaConService;

    @Autowired
    private RedisCache redisCache;

    @GetMapping("/getSysTreeArea")
    @ApiOperation(value = "获取省市区")
    // @Anonymous
    public AjaxResult getSysTreeArea() {
        List<SysTreeAreaVo> sysTreeArea = redisCache.getCacheList("sys:addrTreeArea");
        if (CollectionUtils.isEmpty(sysTreeArea)) {
            sysTreeArea = areaConService.getSysTreeArea();
            redisCache.setCacheList("sys:addrTreeArea", sysTreeArea);
        }
        return success(sysTreeArea);
    }

    @GetMapping("getProvinceList")
    @ApiOperation(value = "获取全部省")
    // @Anonymous
    public AjaxResult getProvinceList() {
        List<SysAddrProvince> provinceList = areaConService.getProvinceList();
        return success(provinceList);
    }

    // @GetMapping("getCityListByProvinceCode")
    // @ApiOperation(value = "根据省编码获取城市")
    // public AjaxResult getCityListByProvinceCode(){
    //     List<SysAddrProvince> provinceList = areaConService.getProvinceList();
    //     return success(provinceList);
    // }
    @GetMapping("getAreaInfoByCode")
    @ApiOperation(value = "根据类型和code获取地区信息")
    // @Anonymous
    public AjaxResult getAreaInfoByCode(@RequestParam String code, @RequestParam String searchType) {
        List<SysTreeAreaVo> sysTreeArea = areaConService.getAreaInfoByCode(code, searchType);
        return success(sysTreeArea);
    }
}