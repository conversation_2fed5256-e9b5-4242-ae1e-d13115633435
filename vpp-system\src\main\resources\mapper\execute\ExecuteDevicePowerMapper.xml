<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.execute.mapper.ExecuteDevicePowerMapper">

    <resultMap type="ExecuteDevicePower" id="ExecuteDevicePowerResult">
        <result property="timestamp"    column="timestamp"    />
        <result property="deviceId"    column="device_id"    />
        <result property="devicePower"    column="device_power"    />
    </resultMap>

    <sql id="selectExecuteDevicePowerVo">
        select timestamp, device_id, device_power from execute_device_power
    </sql>

    <select id="selectExecuteDevicePowerList" parameterType="ExecuteDevicePower" resultMap="ExecuteDevicePowerResult">
        <include refid="selectExecuteDevicePowerVo"/>
        <where>
            <if test="devicePower != null "> and device_power = #{devicePower}</if>
        </where>
    </select>

    <select id="selectExecuteDevicePowerByTimestamp" parameterType="Date" resultMap="ExecuteDevicePowerResult">
        <include refid="selectExecuteDevicePowerVo"/>
        where timestamp = #{timestamp}
    </select>

    <insert id="insertExecuteDevicePower" parameterType="ExecuteDevicePower">
        insert into execute_device_power
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">timestamp,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="devicePower != null">device_power,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">#{timestamp},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="devicePower != null">#{devicePower},</if>
        </trim>
    </insert>

    <update id="updateExecuteDevicePower" parameterType="ExecuteDevicePower">
        update execute_device_power
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="devicePower != null">device_power = #{devicePower},</if>
        </trim>
        where timestamp = #{timestamp}
    </update>

    <delete id="deleteExecuteDevicePowerByTimestamp" parameterType="Date">
        delete from execute_device_power where timestamp = #{timestamp}
    </delete>

    <delete id="deleteExecuteDevicePowerByTimestamps" parameterType="String">
        delete from execute_device_power where timestamp in
        <foreach item="timestamp" collection="array" open="(" separator="," close=")">
            #{timestamp}
        </foreach>
    </delete>
</mapper>