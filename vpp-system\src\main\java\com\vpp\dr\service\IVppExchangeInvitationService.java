package com.vpp.dr.service;

import com.vpp.dr.domain.VppExchangeInvitation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 邀约计划服务接口
 */
public interface IVppExchangeInvitationService {

    /**
     * 查询邀约计划列表（带分页和条件过滤）
     * @param params 查询参数（邀约计划名称、响应日、需求地区、事件状态等）
     * @return 邀约计划集合
     */
    List<VppExchangeInvitation> getInvitationList(Map<String, Object> params);

    /**
     * 根据ID查询邀约计划详情
     * @param invitationId 邀约计划ID
     * @return 邀约计划对象
     */
    VppExchangeInvitation getInvitationById(Long invitationId);

    /**
     * 新增邀约计划
     * @param invitation 邀约计划信息
     * @return 结果（1成功，0失败）
     */
    int insertInvitation(VppExchangeInvitation invitation);

    /**
     * 修改邀约计划
     * @param invitation 邀约计划信息
     * @return 结果（1成功，0失败）
     */
    int updateInvitation(VppExchangeInvitation invitation);

    /**
     * 批量删除邀约计划（根据ID数组）
     * @param invitationIds 邀约计划ID数组
     * @return 结果（1成功，0失败）
     */
    int deleteInvitationByIds(Long[] invitationIds);

    /**
     * 导入邀约计划数据（Excel批量导入）
     * @param invitations 邀约计划集合
     * @return 结果（影响行数）
     */
    int insertBatchInvitation(List<VppExchangeInvitation> invitations);

    /**
     * 发布邀约计划（更新状态为已发布）
     * @param invitationId 邀约计划ID（主键）
     * @return 是否发布成功（true=成功，false=失败）
     */
    boolean publishInvitation(Long invitationId);

    /**
     * 按名称或编号查询邀约计划
     * @param params 查询参数（邀约计划名称、邀约计划编号、聚合商ID）
     * @return 邀约计划集合
     */
    List<VppExchangeInvitation> queryByNameOrNo(Map<String, Object> params);

    /**
     * 根据邀约ID更新锁单状态和锁单时间
     * @param invitationId 邀约计划ID
     * @param lockStatus 锁单状态（枚举值：未锁单/已锁单）
     * @return 更新后的邀约计划实体
     */
    @Transactional(rollbackFor = Exception.class)
    VppExchangeInvitation updateLockStatus(Long invitationId, String lockStatus);
}