package com.vpp.re.service.impl;

import com.vpp.re.domain.VppEventScore;
import com.vpp.re.mapper.VppEventScoreMapper;
import com.vpp.re.service.VppEventScoreService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 评分综合与明细表 Service 实现
 */
@Service
public class VppEventScoreServiceImpl implements VppEventScoreService {

    private final VppEventScoreMapper scoreMapper;

    public VppEventScoreServiceImpl(VppEventScoreMapper scoreMapper) {
        this.scoreMapper = scoreMapper;
    }

    /**
     * 插入评分记录（含参数校验）
     * @param score 评分实体
     * @return 成功条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class) // 事务注解（异常回滚）
    public int insertScore(VppEventScore score) {
        validateScore(score); // 参数校验
        return scoreMapper.insert(score);
    }

    /**
     * 更新评分记录（含参数校验）
     * @param score 评分实体（需包含itemId）
     * @return 成功条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateScore(VppEventScore score) {
        if (score.getItemId() == null) { // 校验itemId是否存在
            throw new IllegalArgumentException("评分项目ID不能为空");
        }
        validateScore(score); // 参数校验
        return scoreMapper.updateById(score);
    }

    /**
     * 逻辑删除评分记录
     * @param itemId 评分项目ID
     * @return 成功条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteScore(Long itemId) {
        if (itemId == null) { // 校验itemId是否存在
            throw new IllegalArgumentException("评分项目ID不能为空");
        }
        return scoreMapper.deleteById(itemId);
    }

    /**
     * 根据ID查询评分记录
     * @param itemId 评分项目ID
     * @return 评分实体
     */
    @Override
    public VppEventScore getScoreById(Long itemId) {
        if (itemId == null) {
            return null;
        }
        return scoreMapper.selectById(itemId);
    }

    /**
     * 根据邀约计划ID分页查询评分记录
     * @param invitationId 邀约计划ID
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页数量
     * @return 评分记录列表
     */
    @Override
    public List<VppEventScore> getScoreListByInvitationId(Long invitationId, Integer pageNum, Integer pageSize) {
        if (pageNum == null || pageNum < 1) pageNum = 1; // 默认页码1
        if (pageSize == null || pageSize < 1) pageSize = 10; // 默认每页10条
        int offset = (pageNum - 1) * pageSize; // 计算偏移量
        return scoreMapper.selectByInvitationId(invitationId, offset, pageSize);
    }

    /**
     * 参数校验（通用方法）
     * @param score 评分实体
     */
    private void validateScore(VppEventScore score) {
        // 校验必填字段
        if (score.getInvitationId() == null) {
            throw new IllegalArgumentException("邀约计划ID不能为空");
        }
        if (score.getItemName() == null || score.getItemName().trim().isEmpty()) {
            throw new IllegalArgumentException("评分项目名称不能为空");
        }
        if (score.getTargetValue() == null || score.getTargetValue().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("目标量必须大于0");
        }
        if (score.getActualValue() == null) {
            throw new IllegalArgumentException("实际量不能为空");
        }
        if (score.getCompletionRate() == null
                || score.getCompletionRate().compareTo(BigDecimal.ZERO) < 0
                || score.getCompletionRate().compareTo(new BigDecimal("100")) > 0) {
            throw new IllegalArgumentException("完成率必须在0-100之间");
        }
        if (score.getGrade() == null || !score.getGrade().matches("[A-D]")) {
            throw new IllegalArgumentException("评分等级必须为A/B/C/D");
        }
    }
}