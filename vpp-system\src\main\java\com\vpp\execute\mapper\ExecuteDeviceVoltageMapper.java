package com.vpp.execute.mapper;

import com.vpp.execute.domain.ExecuteDeviceVoltage;

import java.util.Date;
import java.util.List;

/**
 * 设备实时检测电压Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ExecuteDeviceVoltageMapper {
    /**
     * 查询设备实时检测电压
     *
     * @param timestamp 设备实时检测电压主键
     * @return 设备实时检测电压
     */
    public ExecuteDeviceVoltage selectExecuteDeviceVoltageByTimestamp(Date timestamp);

    /**
     * 查询设备实时检测电压列表
     *
     * @param executeDeviceVoltage 设备实时检测电压
     * @return 设备实时检测电压集合
     */
    public List<ExecuteDeviceVoltage> selectExecuteDeviceVoltageList(ExecuteDeviceVoltage executeDeviceVoltage);

    /**
     * 新增设备实时检测电压
     *
     * @param executeDeviceVoltage 设备实时检测电压
     * @return 结果
     */
    public int insertExecuteDeviceVoltage(ExecuteDeviceVoltage executeDeviceVoltage);

    /**
     * 修改设备实时检测电压
     *
     * @param executeDeviceVoltage 设备实时检测电压
     * @return 结果
     */
    public int updateExecuteDeviceVoltage(ExecuteDeviceVoltage executeDeviceVoltage);

    /**
     * 删除设备实时检测电压
     *
     * @param timestamp 设备实时检测电压主键
     * @return 结果
     */
    public int deleteExecuteDeviceVoltageByTimestamp(Date timestamp);

    /**
     * 批量删除设备实时检测电压
     *
     * @param timestamps 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExecuteDeviceVoltageByTimestamps(Date[] timestamps);
}
