<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.dr.mapper.VppMarketClearanceRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="RecordBaseMap" type="VppMarketClearanceRecord">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="transaction_id" property="transactionId" jdbcType="VARCHAR"/>
        <result column="publish_id" property="publishId" jdbcType="BIGINT"/>
        <result column="clearance_time" property="clearanceTime" jdbcType="TIMESTAMP"/>
        <result column="real_cleared" property="realCleared" jdbcType="DECIMAL"/>
        <result column="recently_cleared" property="recentlyCleared" jdbcType="DECIMAL"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分页查询清算记录列表 -->
    <select id="selectRecordList" parameterType="map" resultMap="RecordBaseMap">
        SELECT * FROM vpp_market_clearance_record
        <where>
            <if test="transactionId != null and transactionId != ''">
                AND transaction_id LIKE CONCAT('%', #{transactionId}, '%')
            </if>
            <if test="publishId != null">
                AND publish_id = #{publishId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <!-- 新增时间区间条件 -->
            <if test="startTime != null">
                AND clearance_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND clearance_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY clearance_time DESC
    </select>

</mapper>