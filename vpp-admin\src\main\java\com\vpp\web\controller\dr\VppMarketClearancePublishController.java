package com.vpp.web.controller.dr;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.dr.domain.VppMarketClearancePublish;
import com.vpp.dr.service.IVppMarketClearancePublishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dr/market/clearance/publish")
@Api(tags = "市场出清发布配置管理", description = "虚拟电厂系统平台-市场出清发布配置管理接口")
public class VppMarketClearancePublishController extends BaseController {

    @Autowired
    private IVppMarketClearancePublishService publishService;

    /**
     * 查询发布配置列表（带分页和筛选）
     */
    @GetMapping("/list")
    @ApiOperation("查询发布配置列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "publishTimeStart", value = "发布时间开始（格式：yyyy-MM-dd HH:mm:ss）", dataType = "string"),
            @ApiImplicitParam(name = "publishTimeEnd", value = "发布时间结束（格式：yyyy-MM-dd HH:mm:ss）", dataType = "string"),
            @ApiImplicitParam(name = "sendUserTypeCodes", value = "发送用户类型编码列表（如：ALL_AGGREGATE_USER）", dataType = "array"),
            @ApiImplicitParam(name = "permissionStatus", value = "权限状态（0=关闭，1=开启）", dataType = "integer"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", dataType = "integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", dataType = "integer", defaultValue = "10")
    })
    public TableDataInfo list(
            @RequestParam(required = false) Date publishTimeStart,
            @RequestParam(required = false) Date publishTimeEnd,
            @RequestParam(required = false) List<String> sendUserTypeCodes,
            @RequestParam(required = false) Integer permissionStatus,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        Map<String, Object> params = new HashMap<>();
        params.put("publishTimeStart", publishTimeStart);
        params.put("publishTimeEnd", publishTimeEnd);
        params.put("sendUserTypeCodes", sendUserTypeCodes);
        params.put("permissionStatus", permissionStatus);

        List<VppMarketClearancePublish> list = publishService.getPublishList(params);
        return getDataTable(list);
    }

    /**
     * 获取发布配置详情（含关联用户选择）
     */
    @GetMapping("/{id}")
    @ApiOperation("获取发布配置详情")
    public AjaxResult getInfo(@PathVariable Long id) {
        VppMarketClearancePublish publish = publishService.getPublishDetail(id);
        return AjaxResult.success(publish);
    }

    /**
     * 新增发布配置
     */
    @Log(title = "市场出清发布配置", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增发布配置")
    public AjaxResult add(@RequestBody VppMarketClearancePublish publish) {
        // 实际开发中需从请求中获取selectedUserIds（如前端传递的用户类型ID列表）
        List<Long> selectedUserIds = null;
        publish.setIsDeleted("0");
        return toAjax(publishService.addPublish(publish, selectedUserIds));
    }

    /**
     * 编辑发布配置
     */
    @Log(title = "市场出清发布配置", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("编辑发布配置")
    public AjaxResult edit(@RequestBody VppMarketClearancePublish publish) {
        List<Long> selectedUserIds = null; // 实际需从前端获取
        return toAjax(publishService.updatePublish(publish, selectedUserIds));
    }

    /**
     * 删除发布配置
     */
    @Log(title = "市场出清发布配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @ApiOperation("删除发布配置")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(publishService.deletePublish(id));
    }
}