package com.vpp.execute.service.impl;

import com.vpp.execute.domain.ExecutePlanDevices;
import com.vpp.execute.mapper.ExecutePlanDevicesMapper;
import com.vpp.execute.service.IExecutePlanDevicesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 执行计划关联的设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class ExecutePlanDevicesServiceImpl implements IExecutePlanDevicesService {
    @Autowired
    private ExecutePlanDevicesMapper executePlanDevicesMapper;

    /**
     * 查询执行计划关联的设备
     *
     * @param executePlanDevicesId 执行计划关联的设备主键
     * @return 执行计划关联的设备
     */
    @Override
    public ExecutePlanDevices selectExecutePlanDevicesByExecutePlanDevicesId(Long executePlanDevicesId) {
        return executePlanDevicesMapper.selectExecutePlanDevicesByExecutePlanDevicesId(executePlanDevicesId);
    }

    /**
     * 查询执行计划关联的设备列表
     *
     * @param executePlanDevices 执行计划关联的设备
     * @return 执行计划关联的设备
     */
    @Override
    public List<ExecutePlanDevices> selectExecutePlanDevicesList(ExecutePlanDevices executePlanDevices) {
        return executePlanDevicesMapper.selectExecutePlanDevicesList(executePlanDevices);
    }

    /**
     * 新增执行计划关联的设备
     *
     * @param executePlanDevices 执行计划关联的设备
     * @return 结果
     */
    @Override
    public int insertExecutePlanDevices(ExecutePlanDevices executePlanDevices) {
        return executePlanDevicesMapper.insertExecutePlanDevices(executePlanDevices);
    }

    /**
     * 修改执行计划关联的设备
     *
     * @param executePlanDevices 执行计划关联的设备
     * @return 结果
     */
    @Override
    public int updateExecutePlanDevices(ExecutePlanDevices executePlanDevices) {
        return executePlanDevicesMapper.updateExecutePlanDevices(executePlanDevices);
    }

    /**
     * 批量删除执行计划关联的设备
     *
     * @param executePlanDevicesIds 需要删除的执行计划关联的设备主键
     * @return 结果
     */
    @Override
    public int deleteExecutePlanDevicesByExecutePlanDevicesIds(Long[] executePlanDevicesIds) {
        return executePlanDevicesMapper.deleteExecutePlanDevicesByExecutePlanDevicesIds(executePlanDevicesIds);
    }

    /**
     * 删除执行计划关联的设备信息
     *
     * @param executePlanDevicesId 执行计划关联的设备主键
     * @return 结果
     */
    @Override
    public int deleteExecutePlanDevicesByExecutePlanDevicesId(Long executePlanDevicesId) {
        return executePlanDevicesMapper.deleteExecutePlanDevicesByExecutePlanDevicesId(executePlanDevicesId);
    }
}