package com.vpp.aggregator.service.impl;

import com.vpp.aggregator.domain.VppBase;
import com.vpp.aggregator.mapper.VppBaseMapper;
import com.vpp.aggregator.service.IVppBaseService;
import com.vpp.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 虚拟电厂聚合商Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class VppBaseServiceImpl implements IVppBaseService {
    @Autowired
    private VppBaseMapper vppBaseMapper;

    /**
     * 查询虚拟电厂聚合商
     *
     * @param vppId 虚拟电厂聚合商主键
     * @return 虚拟电厂聚合商
     */
    @Override
    public VppBase selectVppBaseByVppId(Long vppId) {
        return vppBaseMapper.selectVppBaseByVppId(vppId);
    }

    /**
     * 查询虚拟电厂聚合商列表
     *
     * @param vppBase 虚拟电厂聚合商
     * @return 虚拟电厂聚合商
     */
    @Override
    public List<VppBase> selectVppBaseList(VppBase vppBase) {
        return vppBaseMapper.selectVppBaseList(vppBase);
    }

    /**
     * 新增虚拟电厂聚合商
     *
     * @param vppBase 虚拟电厂聚合商
     * @return 结果
     */
    @Override
    public int insertVppBase(VppBase vppBase) {
        vppBase.setCreateTime(DateUtils.getNowDate());
        return vppBaseMapper.insertVppBase(vppBase);
    }

    /**
     * 修改虚拟电厂聚合商
     *
     * @param vppBase 虚拟电厂聚合商
     * @return 结果
     */
    @Override
    public int updateVppBase(VppBase vppBase) {
        vppBase.setUpdateTime(DateUtils.getNowDate());
        return vppBaseMapper.updateVppBase(vppBase);
    }

    /**
     * 批量删除虚拟电厂聚合商
     *
     * @param vppIds 需要删除的虚拟电厂聚合商主键
     * @return 结果
     */
    @Override
    public int deleteVppBaseByVppIds(Long[] vppIds) {
        return vppBaseMapper.deleteVppBaseByVppIds(vppIds);
    }

    /**
     * 删除虚拟电厂聚合商信息
     *
     * @param vppId 虚拟电厂聚合商主键
     * @return 结果
     */
    @Override
    public int deleteVppBaseByVppId(Long vppId) {
        return vppBaseMapper.deleteVppBaseByVppId(vppId);
    }
}