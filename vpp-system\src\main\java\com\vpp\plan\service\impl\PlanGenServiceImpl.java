package com.vpp.plan.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.plan.domain.PlanGen;
import com.vpp.plan.mapper.PlanGenMapper;
import com.vpp.plan.service.IPlanGenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 自动生成的计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class PlanGenServiceImpl implements IPlanGenService {
    @Autowired
    private PlanGenMapper planGenMapper;

    /**
     * 查询自动生成的计划
     *
     * @param planGenId 自动生成的计划主键
     * @return 自动生成的计划
     */
    @Override
    public PlanGen selectPlanGenByPlanGenId(Long planGenId) {
        return planGenMapper.selectPlanGenByPlanGenId(planGenId);
    }

    /**
     * 查询自动生成的计划列表
     *
     * @param planGen 自动生成的计划
     * @return 自动生成的计划
     */
    @Override
    public List<PlanGen> selectPlanGenList(PlanGen planGen) {
        return planGenMapper.selectPlanGenList(planGen);
    }

    /**
     * 新增自动生成的计划
     *
     * @param planGen 自动生成的计划
     * @return 结果
     */
    @Override
    public int insertPlanGen(PlanGen planGen) {
        planGen.setCreateTime(DateUtils.getNowDate());
        return planGenMapper.insertPlanGen(planGen);
    }

    /**
     * 修改自动生成的计划
     *
     * @param planGen 自动生成的计划
     * @return 结果
     */
    @Override
    public int updatePlanGen(PlanGen planGen) {
        planGen.setUpdateTime(DateUtils.getNowDate());
        return planGenMapper.updatePlanGen(planGen);
    }

    /**
     * 批量删除自动生成的计划
     *
     * @param planGenIds 需要删除的自动生成的计划主键
     * @return 结果
     */
    @Override
    public int deletePlanGenByPlanGenIds(Long[] planGenIds) {
        return planGenMapper.deletePlanGenByPlanGenIds(planGenIds);
    }

    /**
     * 删除自动生成的计划信息
     *
     * @param planGenId 自动生成的计划主键
     * @return 结果
     */
    @Override
    public int deletePlanGenByPlanGenId(Long planGenId) {
        return planGenMapper.deletePlanGenByPlanGenId(planGenId);
    }
}