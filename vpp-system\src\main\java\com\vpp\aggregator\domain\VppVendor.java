package com.vpp.aggregator.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 虚拟电厂供应商，是vpp_base虚拟电厂的子关系对象 vpp_vendor
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public class VppVendor extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long vendorId;

    /**
     * 虚拟电厂供应商户号
     */
    @Excel(name = "虚拟电厂供应商户号")
    private String vendorCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String vendorName;

    /**
     * 供应商类型，主类型，0：源，即发电，1：储，即储电型，2：荷，负荷型
     */
    @Excel(name = "供应商类型，主类型，0：源，即发电，1：储，即储电型，2：荷，负荷型")
    private Long vendorType;

    /**
     * 二级类型，根据主分类创建，从1开始
     * 主分类2：负荷类，子分类有
     * 1. 充电桩
     * 2. 夜间工厂
     * 3. 日间工厂
     * 4. 学校
     * 5. 景区
     */
    @Excel(name = "二级类型，根据主分类创建，从1开始 主分类2：负荷类，子分类有 1. 充电桩 2. 夜间工厂 3. 日间工厂 4. 学校 5. 景区")
    private Long vendorType2;

    /**
     * 状态，0 正常
     */
    @Excel(name = "状态，0 正常")
    private Long vendorStatus;

    /**
     * 公司主体代码，对应corporation
     */
    @Excel(name = "公司主体代码，对应corporation")
    private String vendorCorporationCode;

    public void setVendorId(Long vendorId) {
        this.vendorId = vendorId;
    }

    public Long getVendorId() {
        return vendorId;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorType(Long vendorType) {
        this.vendorType = vendorType;
    }

    public Long getVendorType() {
        return vendorType;
    }

    public void setVendorType2(Long vendorType2) {
        this.vendorType2 = vendorType2;
    }

    public Long getVendorType2() {
        return vendorType2;
    }

    public void setVendorStatus(Long vendorStatus) {
        this.vendorStatus = vendorStatus;
    }

    public Long getVendorStatus() {
        return vendorStatus;
    }

    public void setVendorCorporationCode(String vendorCorporationCode) {
        this.vendorCorporationCode = vendorCorporationCode;
    }

    public String getVendorCorporationCode() {
        return vendorCorporationCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("vendorId", getVendorId())
                .append("vendorCode", getVendorCode())
                .append("vendorName", getVendorName())
                .append("vendorType", getVendorType())
                .append("vendorType2", getVendorType2())
                .append("vendorStatus", getVendorStatus())
                .append("vendorCorporationCode", getVendorCorporationCode())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}