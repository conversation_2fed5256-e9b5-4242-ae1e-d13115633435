package com.vpp.chart.service;

import com.vpp.chart.domain.AvailableResource;
import com.vpp.chart.domain.ResourceProvince;
import com.vpp.chart.domain.ResourceStatics;
import com.vpp.chart.mapper.ChartMapper;
import com.vpp.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ChartServiceImpl implements ChartService{
    @Autowired
    ChartMapper mapper;
    @Override
    public AjaxResult devicesCount(ChartType type, String deptid) {
        switch (type)
        {
            case CHUNENG:
                return this.devicesCountChuneng(deptid);
            case NOT_CONTROL:
                return this.devicesCountNotControl(deptid);
            case CONTROL:
                return this.devicesCountControl(deptid);
            case ONLINE:
                return this.devicesCountOnline(deptid);
            case RESOURCE_STATICS:
                return  this.devicesResourceStatics(deptid);
            case AVAILABLE:
                return this.devicesAvailable(deptid);
            case RESOURCE_PROVINCE:
                return this.devicesResourceProvince(deptid);
        }
        return null;
    }
    AjaxResult devicesResourceProvince(String deptid){
        List<ResourceProvince> resourceProvince = mapper.resourceProvince(deptid);
        AjaxResult result=new AjaxResult();
        result.put("code",200);
        result.put("msg","查询成功");
        result.put("data",resourceProvince);
        return result;
    }
    AjaxResult devicesAvailable(String deptid){
        AvailableResource availableResource = mapper.onlineStatis(deptid);
        AjaxResult result=new AjaxResult();
        result.put("code",200);
        result.put("msg","查询成功");
        result.put("data",availableResource);
        return result;
    }
    AjaxResult devicesResourceStatics(String deptid){

        ResourceStatics stringObjectMap = mapper.resourceDistStatistics(deptid);
        AjaxResult ajaxResult=new AjaxResult();
        ajaxResult.put("code",200);
        ajaxResult.put("msg","查询成功");
        ajaxResult.put("data",stringObjectMap);
        System.out.println(stringObjectMap);
        return ajaxResult;
    }
    AjaxResult devicesGroupResource(String deptid){
        return null;
    }
    AjaxResult devicesCountOnline(String deptid){
        int i=mapper.countOnlineDevices(deptid);
        AjaxResult ajaxResult=new AjaxResult();
        ajaxResult.put("code",200);
        ajaxResult.put("msg","查询成功");
        ajaxResult.put("data",i);
        return ajaxResult;
    }
    AjaxResult devicesCountControl(String deptid){
        int i=mapper.countControlDevices(deptid);
        AjaxResult ajaxResult=new AjaxResult();
        ajaxResult.put("code",200);
        ajaxResult.put("msg","查询成功");
        ajaxResult.put("data",i);
        return ajaxResult;
    }
    AjaxResult devicesCountNotControl(String deptid){
        int i=mapper.countNotControlDevices(deptid);
        AjaxResult ajaxResult=new AjaxResult();
        ajaxResult.put("code",200);
        ajaxResult.put("msg","查询成功");
        ajaxResult.put("data",i);
        return ajaxResult;
    }
    AjaxResult devicesCountChuneng(String deptid){
        /**
         * 获取部门下的储能设备数
         */
        int i = mapper.countChunentDevices(deptid);
        AjaxResult ajaxResult=new AjaxResult();
        ajaxResult.put("code",200);
        ajaxResult.put("msg","查询成功");
        ajaxResult.put("data",i);
        return ajaxResult;
    }
}
