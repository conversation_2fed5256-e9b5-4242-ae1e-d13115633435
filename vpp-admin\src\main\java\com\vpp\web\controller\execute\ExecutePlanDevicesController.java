package com.vpp.web.controller.execute;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.execute.domain.ExecutePlanDevices;
import com.vpp.execute.service.IExecutePlanDevicesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 执行计划关联的设备Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/execute/planDevices")
public class ExecutePlanDevicesController extends BaseController {
    @Autowired
    private IExecutePlanDevicesService executePlanDevicesService;

    /**
     * 查询执行计划关联的设备列表
     */
    @PreAuthorize("@ss.hasPermi('execute:planDevices:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExecutePlanDevices executePlanDevices) {
        startPage();
        List<ExecutePlanDevices> list = executePlanDevicesService.selectExecutePlanDevicesList(executePlanDevices);
        return getDataTable(list);
    }

    /**
     * 导出执行计划关联的设备列表
     */
    @PreAuthorize("@ss.hasPermi('execute:planDevices:export')")
    @Log(title = "执行计划关联的设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExecutePlanDevices executePlanDevices) {
        List<ExecutePlanDevices> list = executePlanDevicesService.selectExecutePlanDevicesList(executePlanDevices);
        ExcelUtil<ExecutePlanDevices> util = new ExcelUtil<ExecutePlanDevices>(ExecutePlanDevices.class);
        util.exportExcel(response, list, "执行计划关联的设备数据");
    }

    /**
     * 获取执行计划关联的设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('execute:planDevices:query')")
    @GetMapping(value = "/{executePlanDevicesId}")
    public AjaxResult getInfo(@PathVariable("executePlanDevicesId") Long executePlanDevicesId) {
        return success(executePlanDevicesService.selectExecutePlanDevicesByExecutePlanDevicesId(executePlanDevicesId));
    }

    /**
     * 新增执行计划关联的设备
     */
    @PreAuthorize("@ss.hasPermi('execute:planDevices:add')")
    @Log(title = "执行计划关联的设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExecutePlanDevices executePlanDevices) {
        return toAjax(executePlanDevicesService.insertExecutePlanDevices(executePlanDevices));
    }

    /**
     * 修改执行计划关联的设备
     */
    @PreAuthorize("@ss.hasPermi('execute:planDevices:edit')")
    @Log(title = "执行计划关联的设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExecutePlanDevices executePlanDevices) {
        return toAjax(executePlanDevicesService.updateExecutePlanDevices(executePlanDevices));
    }

    /**
     * 删除执行计划关联的设备
     */
    @PreAuthorize("@ss.hasPermi('execute:planDevices:remove')")
    @Log(title = "执行计划关联的设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{executePlanDevicesIds}")
    public AjaxResult remove(@PathVariable Long[] executePlanDevicesIds) {
        return toAjax(executePlanDevicesService.deleteExecutePlanDevicesByExecutePlanDevicesIds(executePlanDevicesIds));
    }
}