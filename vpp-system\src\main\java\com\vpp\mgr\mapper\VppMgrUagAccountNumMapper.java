package com.vpp.mgr.mapper;

import com.vpp.mgr.domain.VppMgrUagAccountNum;

import java.util.List;

/**
 * 聚合商用户-户号 关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface VppMgrUagAccountNumMapper {
    /**
     * 查询聚合商用户-户号 关联
     *
     * @param userId 聚合商用户-户号 关联主键
     * @return 聚合商用户-户号 关联
     */
    public VppMgrUagAccountNum selectVppMgrUagAccountNumByUserId(Long userId);

    /**
     * 查询聚合商用户-户号 关联列表
     *
     * @param vppMgrUagAccountNum 聚合商用户-户号 关联
     * @return 聚合商用户-户号 关联集合
     */
    public List<VppMgrUagAccountNum> selectVppMgrUagAccountNumList(VppMgrUagAccountNum vppMgrUagAccountNum);

    /**
     * 新增聚合商用户-户号 关联
     *
     * @param vppMgrUagAccountNum 聚合商用户-户号 关联
     * @return 结果
     */
    public int insertVppMgrUagAccountNum(VppMgrUagAccountNum vppMgrUagAccountNum);

    /**
     * 修改聚合商用户-户号 关联
     *
     * @param vppMgrUagAccountNum 聚合商用户-户号 关联
     * @return 结果
     */
    public int updateVppMgrUagAccountNum(VppMgrUagAccountNum vppMgrUagAccountNum);

    /**
     * 删除聚合商用户-户号 关联
     *
     * @param userId 聚合商用户-户号 关联主键
     * @return 结果
     */
    public int deleteVppMgrUagAccountNumByDeptId(Long userId);

    /**
     * 批量删除聚合商用户-户号 关联
     *
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVppMgrUagAccountNumByUserIds(Long[] userIds);

    void saveOrUpdateBatch(List<VppMgrUagAccountNum> list);

    List<VppMgrUagAccountNum> selectByDeptId(Long userId);
}
