package com.vpp.dr.service;

import com.vpp.dr.domain.VppMarketClearancePublish;

import java.util.List;
import java.util.Map;

public interface IVppMarketClearancePublishService {
    /**
     * 分页查询发布配置列表（带条件筛选）
     */
    List<VppMarketClearancePublish> getPublishList(Map<String, Object> params);

    /**
     * 根据ID获取发布配置详情（含关联用户选择）
     */
    VppMarketClearancePublish getPublishDetail(Long id);

    /**
     * 新增发布配置（含关联用户选择）
     */
    boolean addPublish(VppMarketClearancePublish publish, List<Long> selectedUserIds);

    /**
     * 编辑发布配置（含关联用户选择）
     */
    boolean updatePublish(VppMarketClearancePublish publish, List<Long> selectedUserIds);

    /**
     * 删除发布配置
     */
    boolean deletePublish(Long id);
}