package com.vpp.mgr.service;

import com.vpp.mgr.domain.VppMgrAggBase;

import java.util.List;

/**
 * 聚合商-基础信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IVppMgrAggBaseService {
    /**
     * 查询聚合商-基础信息
     *
     * @param userId 聚合商-基础信息主键
     * @return 聚合商-基础信息
     */
    public VppMgrAggBase selectVppMgrAggBaseByAggBaseId(Long userId);

    /**
     * 查询聚合商-基础信息列表
     *
     * @param vppMgrAggBase 聚合商-基础信息
     * @return 聚合商-基础信息集合
     */
    public List<VppMgrAggBase> selectVppMgrAggBaseList(VppMgrAggBase vppMgrAggBase);

    /**
     * 新增聚合商-基础信息
     *
     * @param vppMgrAggBase 聚合商-基础信息
     * @return 结果
     */
    public int insertVppMgrAggBase(VppMgrAggBase vppMgrAggBase);

    /**
     * 修改聚合商-基础信息
     *
     * @param vppMgrAggBase 聚合商-基础信息
     * @return 结果
     */
    public int updateVppMgrAggBase(VppMgrAggBase vppMgrAggBase);

    /**
     * 批量删除聚合商-基础信息
     *
     * @param aggBaseIds 需要删除的聚合商-基础信息主键集合
     * @return 结果
     */
    public int deleteVppMgrAggBaseByAggBaseIds(Long[] aggBaseIds);

    /**
     * 删除聚合商-基础信息信息
     *
     * @param aggBaseId 聚合商-基础信息主键
     * @return 结果
     */
    public int deleteVppMgrAggBaseByAggBaseId(Long aggBaseId);

    VppMgrAggBase selectVppMgrAggbaseByUagid(Long uagid);
}
