<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.aggregator.mapper.VppBaseMapper">

    <resultMap type="VppBase" id="VppBaseResult">
        <result property="vppId"    column="vpp_id"    />
        <result property="vppName"    column="vpp_name"    />
        <result property="vppCorporationCode"    column="vpp_corporation_code"    />
        <result property="vppStatus"    column="vpp_status"    />
        <result property="vppType"    column="vpp_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVppBaseVo">
        select vpp_id, vpp_name, vpp_corporation_code, vpp_status, vpp_type, create_by, create_time, update_by, update_time, remark from vpp_base
    </sql>

    <select id="selectVppBaseList" parameterType="VppBase" resultMap="VppBaseResult">
        <include refid="selectVppBaseVo"/>
        <where>
            <if test="vppName != null  and vppName != ''"> and vpp_name like concat('%', #{vppName}, '%')</if>
            <if test="vppCorporationCode != null  and vppCorporationCode != ''"> and vpp_corporation_code = #{vppCorporationCode}</if>
            <if test="vppStatus != null "> and vpp_status = #{vppStatus}</if>
            <if test="vppType != null "> and vpp_type = #{vppType}</if>
        </where>
    </select>

    <select id="selectVppBaseByVppId" parameterType="Long" resultMap="VppBaseResult">
        <include refid="selectVppBaseVo"/>
        where vpp_id = #{vppId}
    </select>

    <insert id="insertVppBase" parameterType="VppBase" useGeneratedKeys="true" keyProperty="vppId">
        insert into vpp_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vppName != null">vpp_name,</if>
            <if test="vppCorporationCode != null">vpp_corporation_code,</if>
            <if test="vppStatus != null">vpp_status,</if>
            <if test="vppType != null">vpp_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vppName != null">#{vppName},</if>
            <if test="vppCorporationCode != null">#{vppCorporationCode},</if>
            <if test="vppStatus != null">#{vppStatus},</if>
            <if test="vppType != null">#{vppType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVppBase" parameterType="VppBase">
        update vpp_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="vppName != null">vpp_name = #{vppName},</if>
            <if test="vppCorporationCode != null">vpp_corporation_code = #{vppCorporationCode},</if>
            <if test="vppStatus != null">vpp_status = #{vppStatus},</if>
            <if test="vppType != null">vpp_type = #{vppType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where vpp_id = #{vppId}
    </update>

    <delete id="deleteVppBaseByVppId" parameterType="Long">
        delete from vpp_base where vpp_id = #{vppId}
    </delete>

    <delete id="deleteVppBaseByVppIds" parameterType="String">
        delete from vpp_base where vpp_id in
        <foreach item="vppId" collection="array" open="(" separator="," close=")">
            #{vppId}
        </foreach>
    </delete>
</mapper>