package com.vpp.web.controller.td;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.td.domain.VppInvitationDeclaration;
import com.vpp.td.service.IVppInvitationDeclarationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dr/invitation/declaration")
@Api(tags = "邀约反馈")
public class VppInvitationDeclarationController extends BaseController {

    @Autowired
    private IVppInvitationDeclarationService declarationService;

    /**
     * 查询申报反馈列表
     */
    @GetMapping("/list")
    @ApiOperation("查询申报反馈列表")
    public TableDataInfo list(
            @RequestParam(required = false) Long invitationId,
            @RequestParam(required = false) Long userId) {
        startPage();
        Map<String, Object> params = new HashMap<>();
        if (invitationId != null) params.put("invitationId", invitationId);
        if (userId != null) params.put("userId", userId);
        List<VppInvitationDeclaration> list = declarationService.selectList(params);
        return getDataTable(list);
    }

    /**
     * 获取申报反馈详情
     */
    @GetMapping("/{declarationId}")
    @ApiOperation("获取申报反馈详情")
    public AjaxResult getInfo(@PathVariable Long declarationId) {
        return AjaxResult.success(declarationService.selectById(declarationId));
    }

    /**
     * 新增申报反馈
     */
    @Log(title = "邀约申报反馈", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增申报反馈")
    public AjaxResult add(@RequestBody VppInvitationDeclaration declaration) {
        return toAjax(declarationService.insert(declaration));
    }

    /**
     * 修改申报反馈
     */
    @Log(title = "邀约申报反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改申报反馈")
    public AjaxResult edit(@RequestBody VppInvitationDeclaration declaration) {
        return toAjax(declarationService.update(declaration));
    }

    /**
     * 删除申报反馈
     */
    @Log(title = "邀约申报反馈", businessType = BusinessType.DELETE)
    @DeleteMapping("/{declarationId}")
    @ApiOperation("删除申报反馈")
    public AjaxResult remove(@PathVariable Long declarationId) {
        return toAjax(declarationService.deleteById(declarationId));
    }
}