<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.plan.mapper.PlanGenMapper">

    <resultMap type="PlanGen" id="PlanGenResult">
        <result property="planGenId"    column="plan_gen_id"    />
        <result property="planGenName"    column="plan_gen_name"    />
        <result property="planGenStatus"    column="plan_gen_status"    />
        <result property="planGenVpp"    column="plan_gen_vpp"    />
        <result property="planGenActivity"    column="plan_gen_activity"    />
        <result property="planGenDevicesTotal"    column="plan_gen_devices_total"    />
        <result property="planGenTime"    column="plan_gen_time"    />
        <result property="planGenExpireTime"    column="plan_gen_expire_time"    />
        <result property="planGenRatedPower"    column="plan_gen_rated_power"    />
        <result property="planGenRatedElectricity"    column="plan_gen_rated_electricity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlanGenVo">
        select plan_gen_id, plan_gen_name, plan_gen_status, plan_gen_vpp, plan_gen_activity, plan_gen_devices_total, plan_gen_time, plan_gen_expire_time, plan_gen_rated_power, plan_gen_rated_electricity, create_by, create_time, update_by, update_time, remark from plan_gen
    </sql>

    <select id="selectPlanGenList" parameterType="PlanGen" resultMap="PlanGenResult">
        <include refid="selectPlanGenVo"/>
        <where>
            <if test="planGenName != null  and planGenName != ''"> and plan_gen_name like concat('%', #{planGenName}, '%')</if>
            <if test="planGenStatus != null  and planGenStatus != ''"> and plan_gen_status = #{planGenStatus}</if>
            <if test="planGenVpp != null  and planGenVpp != ''"> and plan_gen_vpp = #{planGenVpp}</if>
            <if test="planGenActivity != null  and planGenActivity != ''"> and plan_gen_activity = #{planGenActivity}</if>
            <if test="planGenDevicesTotal != null "> and plan_gen_devices_total = #{planGenDevicesTotal}</if>
            <if test="planGenTime != null "> and plan_gen_time = #{planGenTime}</if>
            <if test="planGenExpireTime != null "> and plan_gen_expire_time = #{planGenExpireTime}</if>
            <if test="planGenRatedPower != null "> and plan_gen_rated_power = #{planGenRatedPower}</if>
            <if test="planGenRatedElectricity != null "> and plan_gen_rated_electricity = #{planGenRatedElectricity}</if>
        </where>
    </select>

    <select id="selectPlanGenByPlanGenId" parameterType="Long" resultMap="PlanGenResult">
        <include refid="selectPlanGenVo"/>
        where plan_gen_id = #{planGenId}
    </select>

    <insert id="insertPlanGen" parameterType="PlanGen" useGeneratedKeys="true" keyProperty="planGenId">
        insert into plan_gen
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planGenName != null">plan_gen_name,</if>
            <if test="planGenStatus != null">plan_gen_status,</if>
            <if test="planGenVpp != null">plan_gen_vpp,</if>
            <if test="planGenActivity != null">plan_gen_activity,</if>
            <if test="planGenDevicesTotal != null">plan_gen_devices_total,</if>
            <if test="planGenTime != null">plan_gen_time,</if>
            <if test="planGenExpireTime != null">plan_gen_expire_time,</if>
            <if test="planGenRatedPower != null">plan_gen_rated_power,</if>
            <if test="planGenRatedElectricity != null">plan_gen_rated_electricity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planGenName != null">#{planGenName},</if>
            <if test="planGenStatus != null">#{planGenStatus},</if>
            <if test="planGenVpp != null">#{planGenVpp},</if>
            <if test="planGenActivity != null">#{planGenActivity},</if>
            <if test="planGenDevicesTotal != null">#{planGenDevicesTotal},</if>
            <if test="planGenTime != null">#{planGenTime},</if>
            <if test="planGenExpireTime != null">#{planGenExpireTime},</if>
            <if test="planGenRatedPower != null">#{planGenRatedPower},</if>
            <if test="planGenRatedElectricity != null">#{planGenRatedElectricity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePlanGen" parameterType="PlanGen">
        update plan_gen
        <trim prefix="SET" suffixOverrides=",">
            <if test="planGenName != null">plan_gen_name = #{planGenName},</if>
            <if test="planGenStatus != null">plan_gen_status = #{planGenStatus},</if>
            <if test="planGenVpp != null">plan_gen_vpp = #{planGenVpp},</if>
            <if test="planGenActivity != null">plan_gen_activity = #{planGenActivity},</if>
            <if test="planGenDevicesTotal != null">plan_gen_devices_total = #{planGenDevicesTotal},</if>
            <if test="planGenTime != null">plan_gen_time = #{planGenTime},</if>
            <if test="planGenExpireTime != null">plan_gen_expire_time = #{planGenExpireTime},</if>
            <if test="planGenRatedPower != null">plan_gen_rated_power = #{planGenRatedPower},</if>
            <if test="planGenRatedElectricity != null">plan_gen_rated_electricity = #{planGenRatedElectricity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where plan_gen_id = #{planGenId}
    </update>

    <delete id="deletePlanGenByPlanGenId" parameterType="Long">
        delete from plan_gen where plan_gen_id = #{planGenId}
    </delete>

    <delete id="deletePlanGenByPlanGenIds" parameterType="String">
        delete from plan_gen where plan_gen_id in
        <foreach item="planGenId" collection="array" open="(" separator="," close=")">
            #{planGenId}
        </foreach>
    </delete>
</mapper>