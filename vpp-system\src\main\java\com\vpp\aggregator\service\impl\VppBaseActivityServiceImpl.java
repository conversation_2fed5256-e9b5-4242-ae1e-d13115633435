package com.vpp.aggregator.service.impl;

import com.vpp.aggregator.domain.VppBaseActivity;
import com.vpp.aggregator.mapper.VppBaseActivityMapper;
import com.vpp.aggregator.service.IVppBaseActivityService;
import com.vpp.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 交易中心下发活动Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class VppBaseActivityServiceImpl implements IVppBaseActivityService {
    @Autowired
    private VppBaseActivityMapper vppBaseActivityMapper;

    /**
     * 查询交易中心下发活动
     *
     * @param activityId 交易中心下发活动主键
     * @return 交易中心下发活动
     */
    @Override
    public VppBaseActivity selectVppBaseActivityByActivityId(Long activityId) {
        return vppBaseActivityMapper.selectVppBaseActivityByActivityId(activityId);
    }

    /**
     * 查询交易中心下发活动列表
     *
     * @param vppBaseActivity 交易中心下发活动
     * @return 交易中心下发活动
     */
    @Override
    public List<VppBaseActivity> selectVppBaseActivityList(VppBaseActivity vppBaseActivity) {
        return vppBaseActivityMapper.selectVppBaseActivityList(vppBaseActivity);
    }

    /**
     * 新增交易中心下发活动
     *
     * @param vppBaseActivity 交易中心下发活动
     * @return 结果
     */
    @Override
    public int insertVppBaseActivity(VppBaseActivity vppBaseActivity) {
        vppBaseActivity.setCreateTime(DateUtils.getNowDate());
        return vppBaseActivityMapper.insertVppBaseActivity(vppBaseActivity);
    }

    /**
     * 修改交易中心下发活动
     *
     * @param vppBaseActivity 交易中心下发活动
     * @return 结果
     */
    @Override
    public int updateVppBaseActivity(VppBaseActivity vppBaseActivity) {
        vppBaseActivity.setUpdateTime(DateUtils.getNowDate());
        return vppBaseActivityMapper.updateVppBaseActivity(vppBaseActivity);
    }

    /**
     * 批量删除交易中心下发活动
     *
     * @param activityIds 需要删除的交易中心下发活动主键
     * @return 结果
     */
    @Override
    public int deleteVppBaseActivityByActivityIds(Long[] activityIds) {
        return vppBaseActivityMapper.deleteVppBaseActivityByActivityIds(activityIds);
    }

    /**
     * 删除交易中心下发活动信息
     *
     * @param activityId 交易中心下发活动主键
     * @return 结果
     */
    @Override
    public int deleteVppBaseActivityByActivityId(Long activityId) {
        return vppBaseActivityMapper.deleteVppBaseActivityByActivityId(activityId);
    }
}