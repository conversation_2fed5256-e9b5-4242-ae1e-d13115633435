package com.vpp.re.service;

import com.vpp.re.domain.VppEventScore;

import java.util.List;

/**
 * 评分综合与明细表 Service
 */
public interface VppEventScoreService {

    /**
     * 插入评分记录（含参数校验）
     * @param score 评分实体（需包含所有必填字段）
     * @return 成功条数（1-成功，0-失败）
     */
    int insertScore(VppEventScore score);

    /**
     * 更新评分记录（含参数校验）
     * @param score 评分实体（需包含itemId和其他更新字段）
     * @return 成功条数（1-成功，0-失败）
     */
    int updateScore(VppEventScore score);

    /**
     * 逻辑删除评分记录
     * @param itemId 评分项目ID
     * @return 成功条数（1-成功，0-失败）
     */
    int deleteScore(Long itemId);

    /**
     * 根据ID查询评分记录
     * @param itemId 评分项目ID
     * @return 评分实体（不存在则返回null）
     */
    VppEventScore getScoreById(Long itemId);

    /**
     * 根据邀约计划ID分页查询评分记录
     * @param invitationId 邀约计划ID
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页数量
     * @return 评分记录列表
     */
    List<VppEventScore> getScoreListByInvitationId(Long invitationId, Integer pageNum, Integer pageSize);
}