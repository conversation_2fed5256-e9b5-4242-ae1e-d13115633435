package com.vpp.td.service.impl;

import com.vpp.td.domain.VppInvitationWinFeedback;
import com.vpp.td.mapper.VppInvitationWinFeedbackMapper;
import com.vpp.td.service.IVppInvitationWinFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class VppInvitationWinFeedbackServiceImpl implements IVppInvitationWinFeedbackService {

    @Autowired
    private VppInvitationWinFeedbackMapper winFeedbackMapper;

    @Override
    public List<VppInvitationWinFeedback> selectList(Map<String, Object> params) {
        if (params == null) params = new HashMap<>();
        return winFeedbackMapper.selectList(params);
    }

    @Override
    public VppInvitationWinFeedback selectById(Long winFeedbackId) {
        return winFeedbackMapper.selectById(winFeedbackId);
    }

    @Override
    public int insert(VppInvitationWinFeedback winFeedback) {
        return winFeedbackMapper.insert(winFeedback);
    }

    @Override
    public int update(VppInvitationWinFeedback winFeedback) {
        return winFeedbackMapper.update(winFeedback);
    }

    @Override
    public int deleteById(Long winFeedbackId) {
        return winFeedbackMapper.deleteById(winFeedbackId);
    }
}