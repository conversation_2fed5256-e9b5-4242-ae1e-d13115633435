package com.vpp.dr.mapper;

import com.vpp.dr.domain.VppUserType;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

public interface VppUserTypeMapper {

    /**
     * 查询所有用户类型（含层级结构）
     */
    List<VppUserType> selectAll();

    /**
     * 根据ID查询用户类型详情
     */
    VppUserType selectById(Long id);

    /**
     * 新增用户类型
     */
    int insert(VppUserType userType);

    /**
     * 更新用户类型
     */
    int update(VppUserType userType);

    /**
     * 删除用户类型（逻辑删除）
     */
    int deleteById(Long id);

    /**
     * 根据用户类型编码查询用户类型（用于校验唯一性）
     */
    VppUserType selectByTypeCode(@Param("typeCode") String typeCode);

    /**
     * 根据类型编码查询用户类型及关联用户（一对多）
     * @return 用户类型对象（包含用户列表）
     */
    VppUserType queryUsersByUserType(Long userId);
}