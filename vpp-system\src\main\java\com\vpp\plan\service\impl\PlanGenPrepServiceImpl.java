package com.vpp.plan.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.plan.domain.PlanGenPrep;
import com.vpp.plan.mapper.PlanGenPrepMapper;
import com.vpp.plan.service.IPlanGenPrepService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 预备设备，当出现异常时，使用预备设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class PlanGenPrepServiceImpl implements IPlanGenPrepService {
    @Autowired
    private PlanGenPrepMapper planGenPrepMapper;

    /**
     * 查询预备设备，当出现异常时，使用预备设备
     *
     * @param planGenPrepId 预备设备，当出现异常时，使用预备设备主键
     * @return 预备设备，当出现异常时，使用预备设备
     */
    @Override
    public PlanGenPrep selectPlanGenPrepByPlanGenPrepId(Long planGenPrepId) {
        return planGenPrepMapper.selectPlanGenPrepByPlanGenPrepId(planGenPrepId);
    }

    /**
     * 查询预备设备，当出现异常时，使用预备设备列表
     *
     * @param planGenPrep 预备设备，当出现异常时，使用预备设备
     * @return 预备设备，当出现异常时，使用预备设备
     */
    @Override
    public List<PlanGenPrep> selectPlanGenPrepList(PlanGenPrep planGenPrep) {
        return planGenPrepMapper.selectPlanGenPrepList(planGenPrep);
    }

    /**
     * 新增预备设备，当出现异常时，使用预备设备
     *
     * @param planGenPrep 预备设备，当出现异常时，使用预备设备
     * @return 结果
     */
    @Override
    public int insertPlanGenPrep(PlanGenPrep planGenPrep) {
        planGenPrep.setCreateTime(DateUtils.getNowDate());
        return planGenPrepMapper.insertPlanGenPrep(planGenPrep);
    }

    /**
     * 修改预备设备，当出现异常时，使用预备设备
     *
     * @param planGenPrep 预备设备，当出现异常时，使用预备设备
     * @return 结果
     */
    @Override
    public int updatePlanGenPrep(PlanGenPrep planGenPrep) {
        planGenPrep.setUpdateTime(DateUtils.getNowDate());
        return planGenPrepMapper.updatePlanGenPrep(planGenPrep);
    }

    /**
     * 批量删除预备设备，当出现异常时，使用预备设备
     *
     * @param planGenPrepIds 需要删除的预备设备，当出现异常时，使用预备设备主键
     * @return 结果
     */
    @Override
    public int deletePlanGenPrepByPlanGenPrepIds(Long[] planGenPrepIds) {
        return planGenPrepMapper.deletePlanGenPrepByPlanGenPrepIds(planGenPrepIds);
    }

    /**
     * 删除预备设备，当出现异常时，使用预备设备信息
     *
     * @param planGenPrepId 预备设备，当出现异常时，使用预备设备主键
     * @return 结果
     */
    @Override
    public int deletePlanGenPrepByPlanGenPrepId(Long planGenPrepId) {
        return planGenPrepMapper.deletePlanGenPrepByPlanGenPrepId(planGenPrepId);
    }
}