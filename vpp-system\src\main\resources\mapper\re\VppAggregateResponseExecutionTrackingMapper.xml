<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.re.mapper.VppAggregateResponseExecutionTrackingMapper">

    <!-- resultMap：数据库字段与Entity属性映射 -->
    <resultMap id="operationOverviewMap" type="com.vpp.re.domain.VppExchangeOperationOverview">
        <id column="overview_id" property="overviewId" jdbcType="BIGINT"/>
        <result column="event_name" property="eventName" jdbcType="VARCHAR"/>
        <result column="duration_minutes" property="durationMinutes" jdbcType="INTEGER"/>
        <result column="ramp_rate" property="rampRate" jdbcType="DECIMAL"/>
        <result column="response_deviation_rate" property="responseDeviationRate" jdbcType="DECIMAL"/>
        <result column="response_completion_rate" property="responseCompletionRate" jdbcType="DECIMAL"/>
        <result column="response_speed" property="responseSpeed" jdbcType="DECIMAL"/>
        <result column="actual_capacity_kw" property="actualCapacityKw" jdbcType="DECIMAL"/>
        <result column="bid_win_capacity_kw" property="bidWinCapacityKw" jdbcType="DECIMAL"/>
        <result column="upward_regulation_capability_kw" property="upwardRegulationCapabilityKw" jdbcType="DECIMAL"/>
        <result column="total_response_count" property="totalResponseCount" jdbcType="INTEGER"/>
        <result column="invitation_id" property="invitationId" jdbcType="BIGINT"/>
        <result column="invitation_name" property="invitationName" jdbcType="VARCHAR"/>
        <result column="invitation_no" property="invitationNo" jdbcType="VARCHAR"/>
        <result column="statistics_time" property="statisticsTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del_flag" property="delFlag" jdbcType="CHAR"/>
    </resultMap>

    <resultMap id="operationMonitoringMap" type="com.vpp.re.domain.VppOperationMonitoring">
        <!-- 主键映射 -->
        <id column="monitoring_id" property="monitoringId" jdbcType="BIGINT"/>

        <!-- 基础字段映射 -->
        <result column="invitation_id" property="invitationId" jdbcType="BIGINT"/>
        <result column="monitoring_time" property="monitoringTime" jdbcType="TIMESTAMP"/>
        <result column="actual_load" property="actualLoad" jdbcType="DECIMAL"/>
        <result column="effective_response_upper_limit" property="effectiveResponseUpperLimit" jdbcType="DECIMAL"/>
        <result column="effective_response_lower_limit" property="effectiveResponseLowerLimit" jdbcType="DECIMAL"/>
        <result column="load_direction" property="loadDirection" jdbcType="VARCHAR"/>
        <result column="response_type" property="responseType" jdbcType="VARCHAR"/>
        <result column="monitoring_status" property="monitoringStatus" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="basicInfoMap" type="com.vpp.re.domain.VppBasicInformation">
        <!-- 主键映射 -->
        <id column="info_id" property="infoId" jdbcType="BIGINT"/>

        <!-- 基础字段映射 -->
        <result column="invitation_id" property="invitationId" jdbcType="BIGINT"/>
        <result column="actual_load" property="actualLoad" jdbcType="DECIMAL"/>
        <result column="actual_electricity" property="actualElectricity" jdbcType="DECIMAL"/>
        <result column="actual_capacity" property="actualCapacity" jdbcType="DECIMAL"/>
        <result column="regulation_capacity" property="regulationCapacity" jdbcType="DECIMAL"/>
        <result column="regulation_speed" property="regulationSpeed" jdbcType="DECIMAL"/>
        <result column="regulation_deviation_rate" property="regulationDeviationRate" jdbcType="DECIMAL"/>
        <result column="number_of_participating_users" property="numberOfParticipatingUsers" jdbcType="INTEGER"/>
        <result column="number_of_devices" property="numberOfDevices" jdbcType="INTEGER"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del_flag" property="delFlag" jdbcType="CHAR"/>
    </resultMap>

    <!-- 根据邀约计划ID查询运行概览列表 -->
    <select id="selectByInvitationId" parameterType="Map" resultMap="operationOverviewMap">
        SELECT * FROM vpp_exchange_operation_overview
        WHERE invitation_id = #{invitationId}
        AND del_flag = '0' <!-- 逻辑删除过滤 -->
    </select>

    <!-- 根据邀约计划ID查询运行监控表 -->
    <select id="selectOperationByInvitationId" parameterType="Map" resultMap="operationMonitoringMap">
        SELECT * FROM vpp_operation_monitoring
        WHERE invitation_id = #{invitationId}
        AND del_flag = '0' <!-- 逻辑删除过滤 -->
    </select>

    <!-- 根据邀约计划ID查询基本信息表 -->
    <select id="selectBasicInfoByInvitationId" parameterType="Map" resultMap="basicInfoMap">
        SELECT * FROM vpp_basic_information
        WHERE invitation_id = #{invitationId}
        AND del_flag = '0' <!-- 逻辑删除过滤 -->
    </select>
</mapper>