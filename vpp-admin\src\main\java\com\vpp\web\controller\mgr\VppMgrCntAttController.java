package com.vpp.web.controller.mgr;

import com.vpp.common.core.controller.BaseController;
import com.vpp.mgr.service.IVppMgrCntAttService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 合同附件(聚合商-聚合用户)Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/mgr/cnt/att")
public class VppMgrCntAttController extends BaseController {
    @Autowired
    private IVppMgrCntAttService vppMgrCntAttService;

    /**
     * 查询合同附件(聚合商-聚合用户)列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:att:list')")
    // @GetMapping("/list")
    // public TableDataInfo list(VppMgrCntAtt vppMgrCntAtt) {
    //     startPage();
    //     List<VppMgrCntAtt> list = vppMgrCntAttService.selectVppMgrCntAttList(vppMgrCntAtt);
    //     return getDataTable(list);
    // }

    /**
     * 导出合同附件(聚合商-聚合用户)列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:att:export')")
    // @Log(title = "合同附件(聚合商-聚合用户)", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, VppMgrCntAtt vppMgrCntAtt) {
    //     List<VppMgrCntAtt> list = vppMgrCntAttService.selectVppMgrCntAttList(vppMgrCntAtt);
    //     ExcelUtil<VppMgrCntAtt> util = new ExcelUtil<VppMgrCntAtt>(VppMgrCntAtt.class);
    //     util.exportExcel(response, list, "合同附件(聚合商-聚合用户)数据");
    // }

    /**
     * 获取合同附件(聚合商-聚合用户)详细信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:att:query')")
    // @GetMapping(value = "/{uagCntId}")
    // public AjaxResult getInfo(@PathVariable("uagCntId") Long uagCntId) {
    //     return success(vppMgrCntAttService.selectVppMgrCntAttByUagCntId(uagCntId));
    // }

    /**
     * 新增合同附件(聚合商-聚合用户)
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:att:add')")
    // @Log(title = "合同附件(聚合商-聚合用户)", businessType = BusinessType.INSERT)
    // @PostMapping
    // public AjaxResult add(@RequestBody VppMgrCntAtt vppMgrCntAtt) {
    //     return toAjax(vppMgrCntAttService.insertVppMgrCntAtt(vppMgrCntAtt));
    // }

    /**
     * 修改合同附件(聚合商-聚合用户)
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:att:edit')")
    // @Log(title = "合同附件(聚合商-聚合用户)", businessType = BusinessType.UPDATE)
    // @PutMapping
    // public AjaxResult edit(@RequestBody VppMgrCntAtt vppMgrCntAtt) {
    //     return toAjax(vppMgrCntAttService.updateVppMgrCntAtt(vppMgrCntAtt));
    // }

    /**
     * 删除合同附件(聚合商-聚合用户)
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:att:remove')")
    // @Log(title = "合同附件(聚合商-聚合用户)", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{uagCntIds}")
    // public AjaxResult remove(@PathVariable Long[] uagCntIds) {
    //     return toAjax(vppMgrCntAttService.deleteVppMgrCntAttByUagCntIds(uagCntIds));
    // }
}