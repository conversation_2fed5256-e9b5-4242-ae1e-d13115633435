package com.vpp.td.mapper;

import com.vpp.td.domain.VppInvitationDeclaration;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

@Mapper
public interface VppInvitationDeclarationMapper {
    /**
     * 分页查询申报列表（带筛选）
     */
    List<VppInvitationDeclaration> selectList(Map<String, Object> params);

    /**
     * 查询申报总记录数（带筛选）
     */
    int selectCount(Map<String, Object> params);

    /**
     * 根据邀约计划ID查询申报反馈列表（可能关联多个用户申报）
     */
    List<VppInvitationDeclaration> selectByInvitationId(Long invitationId);

    /**
     * 根据申报反馈ID查询单条记录（根据id查找）
     */
    VppInvitationDeclaration selectById(Long declarationId);

    /**
     * 新增申报反馈记录
     */
    int insert(VppInvitationDeclaration declaration);

    /**
     * 根据申报反馈ID更新记录（全量更新）
     */
    int update(VppInvitationDeclaration declaration);

    /**
     * 根据申报反馈ID删除记录
     */
    int deleteById(Long declarationId);
}