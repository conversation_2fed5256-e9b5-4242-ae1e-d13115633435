<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.aggregator.mapper.VppBaseActivityMapper">

    <resultMap type="VppBaseActivity" id="VppBaseActivityResult">
        <result property="activityId"    column="activity_id"    />
        <result property="activityName"    column="activity_name"    />
        <result property="activityStartTime"    column="activity_start_time"    />
        <result property="activityEndTime"    column="activity_end_time"    />
        <result property="activityStatus"    column="activity_status"    />
        <result property="activityCapacity"    column="activity_capacity"    />
        <result property="activityCode"    column="activity_code"    />
        <result property="activityProvince"    column="activity_province"    />
        <result property="activityMunicipality"    column="activity_municipality"    />
        <result property="activityRegional"    column="activity_regional"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVppBaseActivityVo">
        select activity_id, activity_name, activity_start_time, activity_end_time, activity_status, activity_capacity, activity_code, activity_province, activity_municipality, activity_regional, create_by, create_time, update_by, update_time, remark from vpp_base_activity
    </sql>

    <select id="selectVppBaseActivityList" parameterType="VppBaseActivity" resultMap="VppBaseActivityResult">
        <include refid="selectVppBaseActivityVo"/>
        <where>
            <if test="activityName != null  and activityName != ''"> and activity_name like concat('%', #{activityName}, '%')</if>
            <if test="activityStartTime != null "> and activity_start_time = #{activityStartTime}</if>
            <if test="activityEndTime != null "> and activity_end_time = #{activityEndTime}</if>
            <if test="activityStatus != null "> and activity_status = #{activityStatus}</if>
            <if test="activityCapacity != null "> and activity_capacity = #{activityCapacity}</if>
            <if test="activityCode != null  and activityCode != ''"> and activity_code = #{activityCode}</if>
            <if test="activityProvince != null "> and activity_province = #{activityProvince}</if>
            <if test="activityMunicipality != null "> and activity_municipality = #{activityMunicipality}</if>
            <if test="activityRegional != null "> and activity_regional = #{activityRegional}</if>
        </where>
    </select>

    <select id="selectVppBaseActivityByActivityId" parameterType="Long" resultMap="VppBaseActivityResult">
        <include refid="selectVppBaseActivityVo"/>
        where activity_id = #{activityId}
    </select>

    <insert id="insertVppBaseActivity" parameterType="VppBaseActivity" useGeneratedKeys="true" keyProperty="activityId">
        insert into vpp_base_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityName != null">activity_name,</if>
            <if test="activityStartTime != null">activity_start_time,</if>
            <if test="activityEndTime != null">activity_end_time,</if>
            <if test="activityStatus != null">activity_status,</if>
            <if test="activityCapacity != null">activity_capacity,</if>
            <if test="activityCode != null">activity_code,</if>
            <if test="activityProvince != null">activity_province,</if>
            <if test="activityMunicipality != null">activity_municipality,</if>
            <if test="activityRegional != null">activity_regional,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityName != null">#{activityName},</if>
            <if test="activityStartTime != null">#{activityStartTime},</if>
            <if test="activityEndTime != null">#{activityEndTime},</if>
            <if test="activityStatus != null">#{activityStatus},</if>
            <if test="activityCapacity != null">#{activityCapacity},</if>
            <if test="activityCode != null">#{activityCode},</if>
            <if test="activityProvince != null">#{activityProvince},</if>
            <if test="activityMunicipality != null">#{activityMunicipality},</if>
            <if test="activityRegional != null">#{activityRegional},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVppBaseActivity" parameterType="VppBaseActivity">
        update vpp_base_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityName != null">activity_name = #{activityName},</if>
            <if test="activityStartTime != null">activity_start_time = #{activityStartTime},</if>
            <if test="activityEndTime != null">activity_end_time = #{activityEndTime},</if>
            <if test="activityStatus != null">activity_status = #{activityStatus},</if>
            <if test="activityCapacity != null">activity_capacity = #{activityCapacity},</if>
            <if test="activityCode != null">activity_code = #{activityCode},</if>
            <if test="activityProvince != null">activity_province = #{activityProvince},</if>
            <if test="activityMunicipality != null">activity_municipality = #{activityMunicipality},</if>
            <if test="activityRegional != null">activity_regional = #{activityRegional},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where activity_id = #{activityId}
    </update>

    <delete id="deleteVppBaseActivityByActivityId" parameterType="Long">
        delete from vpp_base_activity where activity_id = #{activityId}
    </delete>

    <delete id="deleteVppBaseActivityByActivityIds" parameterType="String">
        delete from vpp_base_activity where activity_id in
        <foreach item="activityId" collection="array" open="(" separator="," close=")">
            #{activityId}
        </foreach>
    </delete>
</mapper>