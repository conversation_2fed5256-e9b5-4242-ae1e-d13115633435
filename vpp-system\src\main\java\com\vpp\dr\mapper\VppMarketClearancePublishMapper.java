package com.vpp.dr.mapper;

import com.vpp.dr.domain.VppMarketClearancePublish;

import java.util.List;
import java.util.Map;

public interface VppMarketClearancePublishMapper {

    /**
     * 分页查询发布配置列表（带条件筛选）
     */
    List<VppMarketClearancePublish> selectPublishList(Map<String, Object> params);

    /**
     * 根据ID查询发布配置详情（含关联用户选择）
     */
    VppMarketClearancePublish selectPublishWithUserSelection(Long id);

    /**
     * 新增发布配置
     */
    int insertPublish(VppMarketClearancePublish publish);

    /**
     * 更新发布配置
     */
    int updatePublish(VppMarketClearancePublish publish);

    /**
     * 删除发布配置（逻辑删除）
     */
    int deletePublish(Long id);
}