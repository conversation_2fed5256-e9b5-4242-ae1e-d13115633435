package com.vpp.chart.domain;

import lombok.Data;
import lombok.Setter;

@Setter
public class ResourceStatics {
    private Long offline;
    private Long total;
    private Long chuneng;
    private Long fadian;
    private Long fuhe;

    public Long getOffline() {
        return offline==null?0:offline;
    }

    public Long getTotal() {
        return total==null?0:total;
    }

    public Long getChuneng() {
        return chuneng==null?0:chuneng;
    }

    public Long getFadian() {
        return fadian==null?0:fadian;
    }

    public Long getFuhe() {
        return fuhe==null?0:fuhe;
    }
}
