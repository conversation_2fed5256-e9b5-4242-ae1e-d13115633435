package com.vpp.td.service.impl;

import com.vpp.td.domain.VppUserInvitation;
import com.vpp.td.mapper.VppUserInvitationMapper;
import com.vpp.td.service.IVppUserInvitationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VppUserInvitationServiceImpl implements IVppUserInvitationService {
    @Autowired
    private VppUserInvitationMapper userInvitationMapper;

    @Override
    public List<VppUserInvitation> selectList(VppUserInvitation invitation) {
        return userInvitationMapper.selectList(invitation);
    }

    @Override
    public VppUserInvitation selectById(Long userInvitationId) {
        return userInvitationMapper.selectById(userInvitationId);
    }

    @Override
    public int insert(VppUserInvitation invitation) {
        return userInvitationMapper.insert(invitation);
    }

    @Override
    public int update(VppUserInvitation invitation) {
        return userInvitationMapper.update(invitation);
    }

    @Override
    public int deleteById(Long userInvitationId) {
        return userInvitationMapper.deleteById(userInvitationId);
    }
}
