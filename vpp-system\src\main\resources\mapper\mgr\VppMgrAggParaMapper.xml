<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.mgr.mapper.VppMgrAggParaMapper">

    <resultMap type="VppMgrAggPara" id="VppMgrAggParaResult">
        <result property="aggParaId"    column="agg_para_id"    />
        <result property="aggCap"    column="agg_cap"    />
        <result property="regCap"    column="reg_cap"    />
        <result property="maxUp"    column="max_up"    />
        <result property="maxDown"    column="max_down"    />
        <result property="regDur"    column="reg_dur"    />
        <result property="maxUpDur"    column="max_up_dur"    />
        <result property="maxDownDur"    column="max_down_dur"    />
        <result property="maxUpRel"    column="max_up_rel"    />
        <result property="maxDownRel"    column="max_down_rel"    />
        <result property="regRate"    column="reg_rate"    />
        <result property="upRate"    column="up_rate"    />
        <result property="downRate"    column="down_rate"    />
        <result property="regPrec"    column="reg_prec"    />
        <result property="rampspd"    column="rampspd"    />
        <result property="rampdur"    column="rampdur"    />
        <result property="upRampspd"    column="up_rampspd"    />
        <result property="upRampdur"    column="up_rampdur"    />
        <result property="downRampspd"    column="down_rampspd"    />
        <result property="downRampdur"    column="down_rampdur"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVppMgrAggParaVo">
        select agg_para_id, agg_cap, reg_cap, max_up, max_down, reg_dur, max_up_dur, max_down_dur, max_up_rel, max_down_rel, reg_rate, up_rate, down_rate, reg_prec, rampspd, rampdur, up_rampspd, up_rampdur, down_rampspd, down_rampdur, dept_id, user_id, del_flag, create_by, create_time, update_by, update_time, remark from vpp_mgr_agg_para
    </sql>

    <select id="selectVppMgrAggParaList" parameterType="VppMgrAggPara" resultMap="VppMgrAggParaResult">
        <include refid="selectVppMgrAggParaVo"/>
        <where>
            <if test="aggCap != null "> and agg_cap = #{aggCap}</if>
            <if test="regCap != null "> and reg_cap = #{regCap}</if>
            <if test="maxUp != null "> and max_up = #{maxUp}</if>
            <if test="maxDown != null "> and max_down = #{maxDown}</if>
            <if test="regDur != null "> and reg_dur = #{regDur}</if>
            <if test="maxUpDur != null "> and max_up_dur = #{maxUpDur}</if>
            <if test="maxDownDur != null "> and max_down_dur = #{maxDownDur}</if>
            <if test="maxUpRel != null "> and max_up_rel = #{maxUpRel}</if>
            <if test="maxDownRel != null "> and max_down_rel = #{maxDownRel}</if>
            <if test="regRate != null "> and reg_rate = #{regRate}</if>
            <if test="upRate != null "> and up_rate = #{upRate}</if>
            <if test="downRate != null "> and down_rate = #{downRate}</if>
            <if test="regPrec != null "> and reg_prec = #{regPrec}</if>
            <if test="rampspd != null "> and rampspd = #{rampspd}</if>
            <if test="rampdur != null "> and rampdur = #{rampdur}</if>
            <if test="upRampspd != null "> and up_rampspd = #{upRampspd}</if>
            <if test="upRampdur != null "> and up_rampdur = #{upRampdur}</if>
            <if test="downRampspd != null "> and down_rampspd = #{downRampspd}</if>
            <if test="downRampdur != null "> and down_rampdur = #{downRampdur}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectVppMgrAggParaByAggParaId" parameterType="Long" resultMap="VppMgrAggParaResult">
        <include refid="selectVppMgrAggParaVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertVppMgrAggPara" parameterType="VppMgrAggPara" useGeneratedKeys="true" keyProperty="aggParaId">
        insert into vpp_mgr_agg_para
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aggCap != null">agg_cap,</if>
            <if test="regCap != null">reg_cap,</if>
            <if test="maxUp != null">max_up,</if>
            <if test="maxDown != null">max_down,</if>
            <if test="regDur != null">reg_dur,</if>
            <if test="maxUpDur != null">max_up_dur,</if>
            <if test="maxDownDur != null">max_down_dur,</if>
            <if test="maxUpRel != null">max_up_rel,</if>
            <if test="maxDownRel != null">max_down_rel,</if>
            <if test="regRate != null">reg_rate,</if>
            <if test="upRate != null">up_rate,</if>
            <if test="downRate != null">down_rate,</if>
            <if test="regPrec != null">reg_prec,</if>
            <if test="rampspd != null">rampspd,</if>
            <if test="rampdur != null">rampdur,</if>
            <if test="upRampspd != null">up_rampspd,</if>
            <if test="upRampdur != null">up_rampdur,</if>
            <if test="downRampspd != null">down_rampspd,</if>
            <if test="downRampdur != null">down_rampdur,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aggCap != null">#{aggCap},</if>
            <if test="regCap != null">#{regCap},</if>
            <if test="maxUp != null">#{maxUp},</if>
            <if test="maxDown != null">#{maxDown},</if>
            <if test="regDur != null">#{regDur},</if>
            <if test="maxUpDur != null">#{maxUpDur},</if>
            <if test="maxDownDur != null">#{maxDownDur},</if>
            <if test="maxUpRel != null">#{maxUpRel},</if>
            <if test="maxDownRel != null">#{maxDownRel},</if>
            <if test="regRate != null">#{regRate},</if>
            <if test="upRate != null">#{upRate},</if>
            <if test="downRate != null">#{downRate},</if>
            <if test="regPrec != null">#{regPrec},</if>
            <if test="rampspd != null">#{rampspd},</if>
            <if test="rampdur != null">#{rampdur},</if>
            <if test="upRampspd != null">#{upRampspd},</if>
            <if test="upRampdur != null">#{upRampdur},</if>
            <if test="downRampspd != null">#{downRampspd},</if>
            <if test="downRampdur != null">#{downRampdur},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVppMgrAggPara" parameterType="VppMgrAggPara">
        update vpp_mgr_agg_para
        <trim prefix="SET" suffixOverrides=",">
            <if test="aggCap != null">agg_cap = #{aggCap},</if>
            <if test="regCap != null">reg_cap = #{regCap},</if>
            <if test="maxUp != null">max_up = #{maxUp},</if>
            <if test="maxDown != null">max_down = #{maxDown},</if>
            <if test="regDur != null">reg_dur = #{regDur},</if>
            <if test="maxUpDur != null">max_up_dur = #{maxUpDur},</if>
            <if test="maxDownDur != null">max_down_dur = #{maxDownDur},</if>
            <if test="maxUpRel != null">max_up_rel = #{maxUpRel},</if>
            <if test="maxDownRel != null">max_down_rel = #{maxDownRel},</if>
            <if test="regRate != null">reg_rate = #{regRate},</if>
            <if test="upRate != null">up_rate = #{upRate},</if>
            <if test="downRate != null">down_rate = #{downRate},</if>
            <if test="regPrec != null">reg_prec = #{regPrec},</if>
            <if test="rampspd != null">rampspd = #{rampspd},</if>
            <if test="rampdur != null">rampdur = #{rampdur},</if>
            <if test="upRampspd != null">up_rampspd = #{upRampspd},</if>
            <if test="upRampdur != null">up_rampdur = #{upRampdur},</if>
            <if test="downRampspd != null">down_rampspd = #{downRampspd},</if>
            <if test="downRampdur != null">down_rampdur = #{downRampdur},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where agg_para_id = #{aggParaId}
    </update>

    <delete id="deleteVppMgrAggParaByAggParaId" parameterType="Long">
        delete from vpp_mgr_agg_para where agg_para_id = #{aggParaId}
    </delete>

    <delete id="deleteVppMgrAggParaByAggParaIds" parameterType="String">
        delete from vpp_mgr_agg_para where agg_para_id in
        <foreach item="aggParaId" collection="array" open="(" separator="," close=")">
            #{aggParaId}
        </foreach>
    </delete>
</mapper>