package com.vpp.web.controller.area;

import com.vpp.area.domain.SysAddrStreet;
import com.vpp.area.service.ISysAddrStreetService;
import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 街道设置Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/system/street")
public class SysAddrStreetController extends BaseController {
    @Autowired
    private ISysAddrStreetService sysAddrStreetService;

    /**
     * 查询街道设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:street:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAddrStreet sysAddrStreet) {
        startPage();
        List<SysAddrStreet> list = sysAddrStreetService.selectSysAddrStreetList(sysAddrStreet);
        return getDataTable(list);
    }

    /**
     * 导出街道设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:street:export')")
    @Log(title = "街道设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAddrStreet sysAddrStreet) {
        List<SysAddrStreet> list = sysAddrStreetService.selectSysAddrStreetList(sysAddrStreet);
        ExcelUtil<SysAddrStreet> util = new ExcelUtil<SysAddrStreet>(SysAddrStreet.class);
        util.exportExcel(response, list, "街道设置数据");
    }

    /**
     * 获取街道设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:street:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(sysAddrStreetService.selectSysAddrStreetById(id));
    }

    /**
     * 新增街道设置
     */
    @PreAuthorize("@ss.hasPermi('system:street:add')")
    @Log(title = "街道设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAddrStreet sysAddrStreet) {
        return toAjax(sysAddrStreetService.insertSysAddrStreet(sysAddrStreet));
    }

    /**
     * 修改街道设置
     */
    @PreAuthorize("@ss.hasPermi('system:street:edit')")
    @Log(title = "街道设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAddrStreet sysAddrStreet) {
        return toAjax(sysAddrStreetService.updateSysAddrStreet(sysAddrStreet));
    }

    /**
     * 删除街道设置
     */
    @PreAuthorize("@ss.hasPermi('system:street:remove')")
    @Log(title = "街道设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysAddrStreetService.deleteSysAddrStreetByIds(ids));
    }
}