package com.vpp.re.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 基本信息表实体类（实现Serializable）
 */
@Data
@ToString
@ApiModel(value = "基本信息表实体", description = "聚合响应执行追踪对应的实体类")
public class VppBasicInformation implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "基本信息主键ID", example = "1")
    private Long infoId;

    @ApiModelProperty(value = "关联邀约计划ID", example = "44")
    private Long invitationId;

    @ApiModelProperty(value = "实际负荷 (KW)", example = "500.00")
    private BigDecimal actualLoad;

    @ApiModelProperty(value = "实际电量 (KW)", example = "1000.00")
    private BigDecimal actualElectricity;

    @ApiModelProperty(value = "实际容量 (KW)", example = "600.00")
    private BigDecimal actualCapacity;

    @ApiModelProperty(value = "调节容量 (KW)", example = "200.00")
    private BigDecimal regulationCapacity;

    @ApiModelProperty(value = "调节速度 (%/min)", example = "5.00")
    private BigDecimal regulationSpeed;

    @ApiModelProperty(value = "调节偏差率 (%)", example = "1.00")
    private BigDecimal regulationDeviationRate;

    @ApiModelProperty(value = "聚合用户参与数量（户）", example = "50")
    private Integer numberOfParticipatingUsers;

    @ApiModelProperty(value = "聚合用户设备数量（台）", example = "100")
    private Integer numberOfDevices;

    @ApiModelProperty(value = "创建者（系统用户账号）", example = "admin")
    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "2025-07-27 10:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "最后更新者（系统用户账号）", example = "admin")
    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后更新时间", example = "2025-07-27 10:30:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标志（0-未删除，2-已删除）", example = "0")
    private String delFlag;
}