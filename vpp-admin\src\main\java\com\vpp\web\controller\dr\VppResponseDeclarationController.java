package com.vpp.web.controller.dr;

import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.dr.domain.VppResponseDeclaration;
import com.vpp.dr.service.IVppResponseDeclarationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.vpp.framework.datasource.DynamicDataSourceContextHolder.log;

@Api(tags = "响应申报")
@RestController
@RequestMapping("/dr/responseDeclaration")
public class VppResponseDeclarationController extends BaseController {

    @Autowired
    private IVppResponseDeclarationService service;

    @ApiOperation("分页查询响应申报列表")
    @GetMapping("/list")
    public TableDataInfo list(
            @ApiParam("邀约计划名称") @RequestParam(required = false) String invitationName,
            @ApiParam("响应日") @RequestParam(required = false) String responseDate,
            @ApiParam("需求地区") @RequestParam(required = false) String demandRegion,
            @ApiParam("事件状态") @RequestParam(required = false) String eventStatus,
            @ApiParam("发布来源") @RequestParam(required = false) String sourceRelease,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer limit
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("invitationName", invitationName != null ? invitationName.trim() : "");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (responseDate != null) {
            try {
                responseDate = String.valueOf(LocalDateTime.parse(responseDate, dateFormatter));
            } catch (DateTimeParseException e) {
                // 处理解析失败（可选：返回错误信息）
                log.error("日期格式错误，期望格式：yyyy-MM-dd HH:mm:ss，实际值：{}", responseDate);
                throw new IllegalArgumentException("日期格式错误");
            }
        }
        params.put("responseDate", responseDate);
        params.put("demandRegion", demandRegion);
        params.put("eventStatus", eventStatus);
        params.put("sourceRelease", sourceRelease != null ? sourceRelease.trim() : "");
        // 分页参数（实际项目中建议使用PageHelper）
        params.put("offset", (page - 1) * limit);
        params.put("limit", limit);
        List<VppResponseDeclaration> list = service.queryList(params);
        return getDataTable(list);
    }

    @ApiOperation("查询总记录数")
    @GetMapping("/total")
    public Object total(
            @ApiParam("邀约计划名称") @RequestParam(required = false) String invitationName,
            @ApiParam("响应日") @RequestParam(required = false) String responseDate,
            @ApiParam("需求地区") @RequestParam(required = false) String demandRegion,
            @ApiParam("事件状态") @RequestParam(required = false) String eventStatus
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("invitationName", invitationName);
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (responseDate != null) {
            try {
                responseDate = String.valueOf(LocalDateTime.parse(responseDate, dateFormatter));
            } catch (DateTimeParseException e) {
                // 处理解析失败（可选：返回错误信息）
                log.error("日期格式错误，期望格式：yyyy-MM-dd HH:mm:ss，实际值：{}", responseDate);
                throw new IllegalArgumentException("日期格式错误");
            }
        }
        params.put("responseDate", responseDate);
        params.put("demandRegion", demandRegion);
        params.put("eventStatus", eventStatus);
        return service.queryTotal(params);
    }

    @ApiOperation("获取响应申报详情")
    @GetMapping("/detail/{id}")
    public AjaxResult detail(@ApiParam("申报ID") @PathVariable Long id) {
        return success(service.getById(id));
    }

    @ApiOperation("根据邀约计划ID获取申报记录")
    @GetMapping("/invitationDetail/{invitationId}")
    public AjaxResult invitationDetail(@ApiParam("邀约计划ID") @PathVariable Long invitationId) {
        return success(service.getByInvitationId(invitationId));
    }

    @ApiOperation("新增响应申报")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody VppResponseDeclaration declaration) {
        return success(service.save(declaration));
    }

    @ApiOperation("更新响应申报")
    @PutMapping("/update")
    public AjaxResult update(@RequestBody VppResponseDeclaration declaration) {
        return success(service.update(declaration));
    }

    @ApiOperation("删除响应申报")
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@ApiParam("申报ID") @PathVariable Long id) {
        return success(service.remove(id));
    }

    @ApiOperation(value = "修改响应申报状态", notes = "根据申报ID修改申报状态（枚举值：未申报/已申报")
    @PutMapping("/updateDeclarationStatus/{id}")
    public AjaxResult updateDeclarationStatus(@ApiParam("申报ID") @PathVariable Long id) {
        return success(service.updateDeclarationStatus(id));
    }

    @ApiOperation("批量导入响应申报")
    @PostMapping("/import")
    public Object importData(@RequestParam("file") MultipartFile file) throws IOException {
        // 实际项目中需使用Excel解析工具（如EasyExcel）
        List<VppResponseDeclaration> list = parseExcel(file);
        return service.importData(list);
    }

    @ApiOperation("下载Excel模板")
    @GetMapping("/download/template")
    public ResponseEntity<byte[]> downloadTemplate(HttpServletResponse response) throws IOException {
        // 生成包含邀约计划关联字段的模板
        byte[] bytes = generateTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "response_declaration_template.xlsx");
        return ResponseEntity.ok().headers(headers).body(bytes);
    }

    @ApiOperation("导出响应申报数据")
    @GetMapping("/export")
    public void export(
            @ApiParam("邀约计划名称") @RequestParam(required = false) String invitationName,
            @ApiParam("响应日") @RequestParam(required = false) String responseDate,
            @ApiParam("需求地区") @RequestParam(required = false) String demandRegion,
            @ApiParam("事件状态") @RequestParam(required = false) String eventStatus,
            HttpServletResponse response
    ) throws IOException {
        Map<String, Object> params = new HashMap<>();
        params.put("invitationName", invitationName);
        params.put("responseDate", responseDate != null ? LocalDateTime.parse(responseDate) : null);
        params.put("demandRegion", demandRegion);
        params.put("eventStatus", eventStatus);
        List<VppResponseDeclaration> dataList = service.queryList(params);
        byte[] bytes = generateExport(dataList);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "response_declaration_data.xlsx");
        response.getOutputStream().write(bytes);
    }

    // 辅助方法（实际项目中需完善Excel解析和生成逻辑）
    private List<VppResponseDeclaration> parseExcel(MultipartFile file) {
        // TODO 实现Excel解析逻辑
        return null;
    }

    private byte[] generateTemplate() {
        // TODO 实现模板生成逻辑
        return new byte[0];
    }

    private byte[] generateExport(List<VppResponseDeclaration> list) {
        // TODO 实现数据导出逻辑
        return new byte[0];
    }
}