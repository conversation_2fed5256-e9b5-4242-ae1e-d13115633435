<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.dr.mapper.VppUserTypeMapper">

    <!-- 结果映射 -->
    <resultMap id="UserTypeBaseMap" type="com.vpp.dr.domain.VppUserType">
        <result column="type_id" property="typeId" jdbcType="BIGINT"/>
        <result column="type_name" property="typeName" jdbcType="VARCHAR"/>
        <result column="type_code" property="typeCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>

        <!-- 关联聚合商用户表字段 -->
        <collection
                property="mgrUags"
                ofType="com.vpp.mgr.domain.VppMgrUag"
                columnPrefix="uaq_">

            <!-- 从表用户字段映射（与 SQL 别名一一对应） -->
            <id property="uagId" column="uaq_uag_id"/>
            <result property="uagName" column="uaq_uag_name"/>
            <result property="uagType" column="uaq_uag_type"/>
        </collection>

    </resultMap>

    <select id="queryUsersByUserType" parameterType="Long" resultMap="UserTypeBaseMap">
        SELECT
        ut.type_code,
        ut.type_name,
        uaq.uag_id AS uaq_uag_id,
        uaq.uag_name AS uaq_uag_name,
        uaq.uag_type AS uaq_uag_type
        FROM
        vpp_user_type ut
        LEFT JOIN
        vpp_mgr_uaq uaq ON ut.type_code = uaq.uag_type
        WHERE
            uaq.user_id = #{userId}
        ORDER BY
        uaq.uag_id ASC;
    </select>

    <!-- 查询所有用户类型（含层级结构） -->
    <select id="selectAll" resultMap="UserTypeBaseMap">
        SELECT * FROM vpp_user_type
        ORDER BY sort_order ASC, type_code ASC
    </select>

    <!-- 根据ID查询用户类型详情 -->
    <select id="selectById" parameterType="Long" resultMap="UserTypeBaseMap">
        SELECT * FROM vpp_user_type WHERE type_id = #{id}
    </select>

    <!-- 新增用户类型 -->
    <insert id="insert" parameterType="VppUserType" useGeneratedKeys="true" keyProperty="typeId">
        INSERT INTO vpp_user_type
            (type_name, type_code, parent_id, sort_order)
        VALUES
            (#{typeName}, #{typeCode}, #{parentId}, #{sortOrder})
    </insert>

    <!-- 更新用户类型 -->
    <update id="update" parameterType="VppUserType">
        UPDATE vpp_user_type
        SET
            type_name = #{typeName},
            type_code = #{typeCode},
            parent_id = #{parentId},
            sort_order = #{sortOrder}
        WHERE type_id = #{typeId}
    </update>

    <!-- 删除用户类型（逻辑删除，假设添加了is_deleted字段） -->
    <update id="deleteById" parameterType="Long">
        UPDATE vpp_user_type
        SET is_deleted = 1  -- 逻辑删除标记
        WHERE type_id = #{id}
    </update>

    <!-- 根据用户类型编码查询用户类型 -->
    <select id="selectByTypeCode" parameterType="string" resultMap="UserTypeBaseMap">
        SELECT * FROM vpp_user_type
        WHERE type_code = #{typeCode}
    </select>
</mapper>