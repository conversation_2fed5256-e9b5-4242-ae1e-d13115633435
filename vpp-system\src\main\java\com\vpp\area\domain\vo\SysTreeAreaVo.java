package com.vpp.area.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * packageName com.vpp.area.domain.vo
 *
 * <AUTHOR>
 * @version JDK 8
 * @className SysTreeAreaVo
 * @date 2025/6/4
 * @description TODO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysTreeAreaVo implements Serializable {
    private static final long serialVersionUID = -9174499354003634540L;
    private String code;
    private String name;
    private List<SysTreeAreaVo> children;


}