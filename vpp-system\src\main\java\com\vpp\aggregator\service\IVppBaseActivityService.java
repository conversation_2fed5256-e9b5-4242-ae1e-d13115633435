package com.vpp.aggregator.service;

import com.vpp.aggregator.domain.VppBaseActivity;

import java.util.List;

/**
 * 交易中心下发活动Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IVppBaseActivityService {
    /**
     * 查询交易中心下发活动
     *
     * @param activityId 交易中心下发活动主键
     * @return 交易中心下发活动
     */
    public VppBaseActivity selectVppBaseActivityByActivityId(Long activityId);

    /**
     * 查询交易中心下发活动列表
     *
     * @param vppBaseActivity 交易中心下发活动
     * @return 交易中心下发活动集合
     */
    public List<VppBaseActivity> selectVppBaseActivityList(VppBaseActivity vppBaseActivity);

    /**
     * 新增交易中心下发活动
     *
     * @param vppBaseActivity 交易中心下发活动
     * @return 结果
     */
    public int insertVppBaseActivity(VppBaseActivity vppBaseActivity);

    /**
     * 修改交易中心下发活动
     *
     * @param vppBaseActivity 交易中心下发活动
     * @return 结果
     */
    public int updateVppBaseActivity(VppBaseActivity vppBaseActivity);

    /**
     * 批量删除交易中心下发活动
     *
     * @param activityIds 需要删除的交易中心下发活动主键集合
     * @return 结果
     */
    public int deleteVppBaseActivityByActivityIds(Long[] activityIds);

    /**
     * 删除交易中心下发活动信息
     *
     * @param activityId 交易中心下发活动主键
     * @return 结果
     */
    public int deleteVppBaseActivityByActivityId(Long activityId);
}
