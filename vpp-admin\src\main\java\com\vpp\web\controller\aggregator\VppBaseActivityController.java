package com.vpp.web.controller.aggregator;

import com.vpp.aggregator.domain.VppBaseActivity;
import com.vpp.aggregator.service.IVppBaseActivityService;
import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.utils.GenerateOrderNo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 交易中心下发活动Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/vpp/baseActivity")
// @Api(tags = "交易中心下发活动")
public class VppBaseActivityController extends BaseController {
    @Autowired
    private IVppBaseActivityService vppBaseActivityService;

    /**
     * 查询交易中心下发活动列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseActivity:list')")
    @GetMapping("/list")
    public TableDataInfo list(VppBaseActivity vppBaseActivity) {
        startPage();
        List<VppBaseActivity> list = vppBaseActivityService.selectVppBaseActivityList(vppBaseActivity);
        return getDataTable(list);
    }

    /**
     * 导出交易中心下发活动列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseActivity:export')")
    @Log(title = "交易中心下发活动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VppBaseActivity vppBaseActivity) {
        List<VppBaseActivity> list = vppBaseActivityService.selectVppBaseActivityList(vppBaseActivity);
        ExcelUtil<VppBaseActivity> util = new ExcelUtil<VppBaseActivity>(VppBaseActivity.class);
        util.exportExcel(response, list, "交易中心下发活动数据");
    }

    /**
     * 获取交易中心下发活动详细信息
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseActivity:query')")
    @GetMapping(value = "/{activityId}")
    public AjaxResult getInfo(@PathVariable("activityId") Long activityId) {
        return success(vppBaseActivityService.selectVppBaseActivityByActivityId(activityId));
    }

    /**
     * 新增交易中心下发活动
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseActivity:add')")
    @Log(title = "交易中心下发活动", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VppBaseActivity vppBaseActivity) {
        return toAjax(vppBaseActivityService.insertVppBaseActivity(vppBaseActivity));
    }

    /**
     * 修改交易中心下发活动
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseActivity:edit')")
    @Log(title = "交易中心下发活动", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VppBaseActivity vppBaseActivity) {
        return toAjax(vppBaseActivityService.updateVppBaseActivity(vppBaseActivity));
    }

    /**
     * 删除交易中心下发活动
     */
    @PreAuthorize("@ss.hasPermi('vpp:baseActivity:remove')")
    @Log(title = "交易中心下发活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{activityIds}")
    public AjaxResult remove(@PathVariable Long[] activityIds) {
        return toAjax(vppBaseActivityService.deleteVppBaseActivityByActivityIds(activityIds));
    }

    @Autowired
    private GenerateOrderNo generateOrderNo;

    @GetMapping("/generateCustomOrderNo")
    // @ApiOperation(value = "测试生成订单号")
    public AjaxResult generateCustomOrderNo() {
        String gys = generateOrderNo.generateCustomOrderNo("GYS");
        System.out.println("===============================" + gys);
        return success();
    }
}