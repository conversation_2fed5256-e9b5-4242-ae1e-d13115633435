package com.vpp.td.service.impl;

import com.vpp.td.domain.VppInvitationScheme;
import com.vpp.td.mapper.VppInvitationSchemeMapper;
import com.vpp.td.service.ISchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class SchemeServiceImpl implements ISchemeService {

    @Autowired
    private VppInvitationSchemeMapper schemeMapper;

    @Override
    public boolean saveScheme(VppInvitationScheme scheme) {
        // 校验方案名称唯一性
        int count = schemeMapper.checkSchemeNameUnique(scheme.getSchemeName(), scheme.getInvitationId());
        if (count > 0) {
            throw new RuntimeException("方案名称已存在");
        }
        return schemeMapper.insertScheme(scheme) > 0;
    }

    @Override
    public List<VppInvitationScheme> getSchemeList(Map<String, Object> params, Integer pageNum, Integer pageSize) {
        // 计算分页偏移量
        int offset = (pageNum - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        return schemeMapper.selectSchemeList(params);
    }

    @Override
    public int getSchemeCount(Map<String, Object> params) {
        return schemeMapper.selectSchemeCount(params);
    }

    /**
     * 修改方案的最优状态（核心逻辑）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VppInvitationScheme updateIsOptimal(Long schemeId, Integer isOptimal) {
        // 参数校验
        if (schemeId == null || schemeId <= 0) {
            throw new IllegalArgumentException("方案ID无效");
        }
        if (isOptimal == null || (isOptimal != 0 && isOptimal != 1)) {
            throw new IllegalArgumentException("最优状态枚举值错误（仅支持0-是，1-否）");
        }

        // 查询方案是否存在
        VppInvitationScheme scheme = schemeMapper.selectById(schemeId);
        if (scheme == null) {
            throw new RuntimeException("方案不存在（ID：" + schemeId + "）");
        }

        // 更新最优状态
        scheme.setIsOptimal(isOptimal.toString()); // 转换为 char(1) 类型存储
        int rows = schemeMapper.updateIsOptimal(scheme);

        if (rows == 0) {
            throw new RuntimeException("更新失败：数据已变更或不存在");
        }

        return scheme;
    }
}