package com.vpp.mgr.service;

import com.vpp.mgr.domain.VppMgrUag;

import java.util.List;

/**
 * 聚合商用户-信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IVppMgrUagService {
    /**
     * 查询聚合商用户-信息
     *
     * @param uagId 聚合商用户-信息主键
     * @return 聚合商用户-信息
     */
    public VppMgrUag selectVppMgrUagByUagId(Long uagId);

    /**
     * 查询聚合商用户-信息列表
     *
     * @param vppMgrUag 聚合商用户-信息
     * @return 聚合商用户-信息集合
     */
    public List<VppMgrUag> selectVppMgrUagList(VppMgrUag vppMgrUag);

    /**
     * 新增聚合商用户-信息
     *
     * @param vppMgrUag 聚合商用户-信息
     * @return 结果
     */
    public int insertVppMgrUag(VppMgrUag vppMgrUag);

    /**
     * 修改聚合商用户-信息
     *
     * @param vppMgrUag 聚合商用户-信息
     * @return 结果
     */
    public int updateVppMgrUag(VppMgrUag vppMgrUag);

    /**
     * 批量删除聚合商用户-信息
     *
     * @param uagIds 需要删除的聚合商用户-信息主键集合
     * @return 结果
     */
    public int deleteVppMgrUagByUagIds(Long[] uagIds);

    /**
     * 删除聚合商用户-信息信息
     *
     * @param uagId 聚合商用户-信息主键
     * @return 结果
     */
    public int deleteVppMgrUagByUagId(Long uagId);

    List<VppMgrUag> listByUserId(Long userId);

    VppMgrUag selectVppMgrUagByDeptId(Long deptId,Long userId);
}