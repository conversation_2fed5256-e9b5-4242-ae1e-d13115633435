package com.vpp.aggregator.service;

import com.vpp.aggregator.domain.VppBaseDevice;

import java.util.List;

/**
 * 聚合用户设备Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IVppBaseDeviceService {
    /**
     * 查询聚合用户设备
     *
     * @param deviceId 聚合用户设备主键
     * @return 聚合用户设备
     */
    public VppBaseDevice selectVppBaseDeviceByDeviceId(Long deviceId);

    /**
     * 查询聚合用户设备列表
     *
     * @param vppBaseDevice 聚合用户设备
     * @return 聚合用户设备集合
     */
    public List<VppBaseDevice> selectVppBaseDeviceList(VppBaseDevice vppBaseDevice);

    /**
     * 新增聚合用户设备
     *
     * @param vppBaseDevice 聚合用户设备
     * @return 结果
     */
    public int insertVppBaseDevice(VppBaseDevice vppBaseDevice);

    /**
     * 修改聚合用户设备
     *
     * @param vppBaseDevice 聚合用户设备
     * @return 结果
     */
    public int updateVppBaseDevice(VppBaseDevice vppBaseDevice);

    /**
     * 批量删除聚合用户设备
     *
     * @param deviceIds 需要删除的聚合用户设备主键集合
     * @return 结果
     */
    public int deleteVppBaseDeviceByDeviceIds(Long[] deviceIds);

    /**
     * 删除聚合用户设备信息
     *
     * @param deviceId 聚合用户设备主键
     * @return 结果
     */
    public int deleteVppBaseDeviceByDeviceId(Long deviceId);
}
