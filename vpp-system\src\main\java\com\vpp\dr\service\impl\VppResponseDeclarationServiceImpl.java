package com.vpp.dr.service.impl;

import com.vpp.dr.domain.VppResponseDeclaration;
import com.vpp.dr.mapper.VppResponseDeclarationMapper;
import com.vpp.dr.service.IVppResponseDeclarationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class VppResponseDeclarationServiceImpl implements IVppResponseDeclarationService {

    @Autowired
    private VppResponseDeclarationMapper mapper;

    @Override
    public List<VppResponseDeclaration> queryList(Map<String, Object> params) {
        return mapper.selectList(params);
    }

    @Override
    public int queryTotal(Map<String, Object> params) {
        return mapper.selectCount(params);
    }

    @Override
    public VppResponseDeclaration getById(Long declarationId) {
        return mapper.selectById(declarationId);
    }

    @Override
    public VppResponseDeclaration getByInvitationId(Long invitationId) {
        return mapper.selectByInvitationId(invitationId);
    }

    @Transactional
    @Override
    public boolean save(VppResponseDeclaration declaration) {
        return mapper.insert(declaration) > 0;
    }

    @Transactional
    @Override
    public boolean update(VppResponseDeclaration declaration) {
        return mapper.update(declaration) > 0;
    }

    @Transactional
    @Override
    public boolean remove(Long declarationId) {
        return mapper.deleteById(declarationId) > 0;
    }

    @Transactional
    @Override
    public boolean updateDeclarationStatus(Long declarationId) {
        VppResponseDeclaration declaration = new VppResponseDeclaration();
        declaration.setDeclarationId(declarationId);
        declaration.setDeclarationStatus("已申报");
        return mapper.updateDeclarationStatus(declaration) > 0;
    }

    @Transactional
    @Override
    public boolean importData(List<VppResponseDeclaration> list) {
        return mapper.batchImport(list) > 0;
    }
}