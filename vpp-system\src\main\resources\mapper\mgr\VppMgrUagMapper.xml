<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.mgr.mapper.VppMgrUagMapper">

    <resultMap type="VppMgrUag" id="VppMgrUagResult">
        <result property="uagId"    column="uag_id"    />
        <result property="uagName"    column="uag_name"    />
        <result property="uagCode"    column="uag_code"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
        <result property="deviceCount"    column="device_count"    />
        <result property="region"    column="region"    />
        <result property="dayBefore"    column="day_before"    />
        <result property="dayIn"    column="day_in"    />
        <result property="uagType"    column="uag_type"    />
        <result property="deviceType"    column="device_type"    />
        <result property="maxUp"    column="max_up"    />
        <result property="maxDown"    column="max_down"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>


    <sql id="selectVppMgrUagVo">
        select uag_id, uag_name, uag_code, contact, phone, device_count, region, day_before, day_in, uag_type, device_type, max_up, max_down, img_url, dept_id, user_id, create_by, create_time, update_by, update_time, remark from vpp_mgr_uag
    </sql>
    <!--    根据合约构建,合约未到期,只要签订,这个关系就存在,所以查找一个聚合商下的聚合用户
            需要根据合约合同,不是dept表
            一个聚合用户只关联一个登陆帐号
            一个聚合商也只关联一个登陆帐号
            查找一个聚合商下的所有聚合用户
            1. 要先根据聚合商id,查找所有聚合商下的未过期合约
            2. 根据未过期合约,找到关联的代理用户
    -->
    <select id="selectVppMgrUagList" parameterType="VppMgrUag" resultMap="VppMgrUagResult">
        select * from (select * from vpp_mgr_uag where uag_id in (
        select proxy_uag_id from vpp_mgr_cnt where dept_id=#{deptId}
        )union all
        select * from vpp_mgr_uag where uag_id not in (select proxy_uag_id from vpp_mgr_cnt) and create_by=#{deptId}
        )a
            <where>
            <if test="uagName != null  and uagName != ''"> and uag_name like concat('%', #{uagName}, '%')</if>
            <if test="uagCode != null  and uagCode != ''"> and uag_code = #{uagCode}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="deviceCount != null "> and device_count = #{deviceCount}</if>
            <if test="region != null  and region != ''"> and region = #{region}</if>
            <if test="dayBefore != null "> and day_before = #{dayBefore}</if>
            <if test="dayIn != null "> and day_in = #{dayIn}</if>
            <if test="uagType != null  and uagType != ''"> and uag_type = #{uagType}</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="maxUp != null "> and max_up = #{maxUp}</if>
            <if test="maxDown != null "> and max_down = #{maxDown}</if>
            <if test="imgUrl != null  and imgUrl != ''"> and img_url = #{imgUrl}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            </where>
    </select>

    <select id="selectVppMgrUagByUagId" parameterType="Long" resultMap="VppMgrUagResult">
        <include refid="selectVppMgrUagVo"/>
        where uag_id = #{uagId}
    </select>


    <insert id="insertVppMgrUag" parameterType="VppMgrUag" useGeneratedKeys="true" keyProperty="uagId">
        insert into vpp_mgr_uag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uagName != null and uagName != ''">uag_name,</if>
            <if test="uagCode != null and uagCode != ''">uag_code,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="deviceCount != null">device_count,</if>
            <if test="region != null">region,</if>
            <if test="dayBefore != null">day_before,</if>
            <if test="dayIn != null">day_in,</if>
            <if test="uagType != null and uagType != ''">uag_type,</if>
            <if test="deviceType != null and deviceType != ''">device_type,</if>
            <if test="maxUp != null">max_up,</if>
            <if test="maxDown != null">max_down,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uagName != null and uagName != ''">#{uagName},</if>
            <if test="uagCode != null and uagCode != ''">#{uagCode},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="deviceCount != null">#{deviceCount},</if>
            <if test="region != null">#{region},</if>
            <if test="dayBefore != null">#{dayBefore},</if>
            <if test="dayIn != null">#{dayIn},</if>
            <if test="uagType != null and uagType != ''">#{uagType},</if>
            <if test="deviceType != null and deviceType != ''">#{deviceType},</if>
            <if test="maxUp != null">#{maxUp},</if>
            <if test="maxDown != null">#{maxDown},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVppMgrUag" parameterType="VppMgrUag">
        update vpp_mgr_uag
        <trim prefix="SET" suffixOverrides=",">
            <if test="uagName != null and uagName != ''">uag_name = #{uagName},</if>
            <if test="uagCode != null and uagCode != ''">uag_code = #{uagCode},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="deviceCount != null">device_count = #{deviceCount},</if>
            <if test="region != null">region = #{region},</if>
            <if test="dayBefore != null">day_before = #{dayBefore},</if>
            <if test="dayIn != null">day_in = #{dayIn},</if>
            <if test="uagType != null and uagType != ''">uag_type = #{uagType},</if>
            <if test="deviceType != null and deviceType != ''">device_type = #{deviceType},</if>
            <if test="maxUp != null">max_up = #{maxUp},</if>
            <if test="maxDown != null">max_down = #{maxDown},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where uag_id = #{uagId}
    </update>

    <delete id="deleteVppMgrUagByUagId" parameterType="Long">
        delete from vpp_mgr_uag where uag_id = #{uagId}
    </delete>

    <delete id="deleteVppMgrUagByUagIds" parameterType="String">
        delete from vpp_mgr_uag where uag_id in
        <foreach item="uagId" collection="array" open="(" separator="," close=")">
            #{uagId}
        </foreach>
    </delete>

<!-- ================================================================================== -->
    <select id="listByUserIds" parameterType="java.util.List" resultMap="VppMgrUagResult">
        select * from vpp_mgr_uag where user_id in
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="listByUserId" parameterType="Long" resultMap="VppMgrUagResult">
        <include refid="selectVppMgrUagVo"/>
         where user_id = #{userId}
    </select>
<!--改成以userid获取数据,一个userid对应一个vpp-mgr-uag -->
    <select id="selectVppMgrUagByDeptId" resultMap="VppMgrUagResult">
<!--        <include refid="selectVppMgrUagVo"/>-->
        select * from vpp_mgr_uag
        where user_id = #{deptId}
    </select>
    <select id="selectVppMgrUagNoCnt" resultMap="VppMgrUagResult">
        select * from vpp_mgr_uag where uag_id not in (select proxy_uag_id from vpp_mgr_cnt) and create_by=#{deptId};
    </select>
</mapper>