package com.vpp.web.controller.dr;

import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.dr.domain.VppExchangeInvitation;
import com.vpp.dr.service.IVppExchangeInvitationService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邀约计划控制器（修正后）
 */
@RestController
@RequestMapping("/dr/invitation")
@Api(tags = "邀约计划管理", description = "虚拟电厂系统平台-邀约计划管理接口")
public class VppExchangeInvitationController extends BaseController {

    @Autowired
    private IVppExchangeInvitationService invitationService;

    /**
     * 查询邀约计划列表（支持分页+多条件过滤）
     */
    @GetMapping("/list")
    @ApiOperation("查询邀约计划列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invitationName", value = "邀约计划名称（模糊匹配）", dataType = "string"),
            @ApiImplicitParam(name = "responseDate", value = "响应执行日（格式：yyyy-MM-dd HH:mm:ss）", dataType = "string"),
            @ApiImplicitParam(name = "deadlineTime", value = "邀约截止时间（格式：yyyy-MM-dd HH:mm:ss）", dataType = "string"),
            @ApiImplicitParam(name = "demandRegion", value = "需求地区（精确匹配）", dataType = "string"),
            @ApiImplicitParam(name = "eventStatus", value = "事件状态（0=未开始，1=已开始，2=已结束，3=已取消）", dataType = "integer"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", dataType = "integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", dataType = "integer", defaultValue = "10"),
            @ApiImplicitParam(name = "userId", value = "聚合商ID", dataType = "integer", defaultValue = "10")
    })
    public TableDataInfo list(
            @RequestParam(required = false) String invitationName,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date responseDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date deadlineTime,
            @RequestParam(required = false) String demandRegion,
            @RequestParam(required = false) Integer eventStatus,
            @RequestParam(required = false) Integer userId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {

        Map<String, Object> params = new HashMap<>();
        params.put("invitationName", invitationName != null ? invitationName.trim() : "");
        params.put("responseDate", responseDate);
        params.put("deadlineTime", deadlineTime);
        params.put("demandRegion", demandRegion);
        params.put("eventStatus", eventStatus);
        params.put("userId", userId);

        List<VppExchangeInvitation> list = invitationService.getInvitationList(params);
        return getDataTable(list);
    }

    /**
     * 获取邀约计划详情（根据ID查询）
     */
    @GetMapping("/{invitationId}")
    @ApiOperation("获取邀约计划详情")
    @ApiImplicitParam(name = "invitationId", value = "邀约计划ID（主键）", required = true, dataType = "long")
    public AjaxResult getInfo(@PathVariable Long invitationId) {
        return success(invitationService.getInvitationById(invitationId));
    }

    /**
     * 新增邀约计划
     */
    @PostMapping
    @ApiOperation("新增邀约计划")
    public AjaxResult add(@RequestBody VppExchangeInvitation invitation) {
        return toAjax(invitationService.insertInvitation(invitation));
    }

    /**
     * 修改邀约计划
     */
    @PutMapping
    @ApiOperation("修改邀约计划")
    public AjaxResult edit(@RequestBody VppExchangeInvitation invitation) {
        return toAjax(invitationService.updateInvitation(invitation));
    }

    /**
     * 删除邀约计划（批量）
     */
    @DeleteMapping("/{invitationIds}")
    @ApiOperation("删除邀约计划")
    @ApiImplicitParam(name = "invitationIds", value = "邀约计划ID数组（支持批量删除）", required = true, dataType = "long[]")
    public AjaxResult remove(@PathVariable Long[] invitationIds) {
        return toAjax(invitationService.deleteInvitationByIds(invitationIds));
    }

    /**
     * 下载邀约计划导入模板（Excel）
     */
    @GetMapping("/importTemplate")
    @ApiOperation("下载导入模板")
    public AjaxResult importTemplate() {
        // 创建ExcelUtil实例（指定实体类类型）
        ExcelUtil<VppExchangeInvitation> excelUtil = new ExcelUtil<>(VppExchangeInvitation.class);
        // 生成模板文件名（可自定义）
        String fileName = "邀约计划导入模板.xlsx";
        // 创建临时文件（实际开发中建议使用固定模板路径）
        try {
            // 写入表头（可选，若模板已有固定表头可跳过）
            // 这里直接返回空模板，实际可根据需求填充示例数据
            return AjaxResult.success(fileName);
        } catch (Exception e) {
            return AjaxResult.error("模板生成失败：" + e.getMessage());
        }
    }

    /**
     * 导入邀约计划数据（Excel批量导入）
     */
    @PostMapping("/importData")
    @ApiOperation("批量导入邀约计划")
    @ApiImplicitParam(name = "file", value = "Excel文件（需符合模板格式）", required = true, dataType = "MultipartFile")
    public AjaxResult importData(@RequestParam("file") MultipartFile file) throws Exception {
        // 创建ExcelUtil实例（指定实体类类型）
        ExcelUtil<VppExchangeInvitation> excelUtil = new ExcelUtil<>(VppExchangeInvitation.class);
        // 解析Excel数据（返回实体类列表）
        List<VppExchangeInvitation> invitations = excelUtil.importExcel(file.getInputStream(), VppExchangeInvitation.class);
        // 批量插入数据库
        int successCount = invitationService.insertBatchInvitation(invitations);
        return success("导入成功，共导入" + successCount + "条数据");
    }

    /**
     * 导出邀约计划数据（Excel）
     */
    @PostMapping("/export")
    @ApiOperation("导出邀约计划数据")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) String invitationName,
                       @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date responseDate,
                       @RequestParam(required = false) String demandRegion,
                       @RequestParam(required = false) Integer eventStatus) {

        Map<String, Object> params = new HashMap<>();
        params.put("invitationName", invitationName);
        params.put("responseDate", responseDate);
        params.put("demandRegion", demandRegion);
        params.put("eventStatus", eventStatus);

        List<VppExchangeInvitation> list = invitationService.getInvitationList(params);

        // 创建ExcelUtil实例（指定实体类类型）
        ExcelUtil<VppExchangeInvitation> excelUtil = new ExcelUtil<>(VppExchangeInvitation.class);
        // 导出Excel（设置工作表名称）
        excelUtil.exportExcel(response, list, "邀约计划数据");
    }

    /**
     * 发布邀约计划（将邀约状态从未发布改为已发布）
     * @param invitationId 邀约计划ID（主键）
     * @return 操作结果（成功/失败）
     */
    @PutMapping("/{invitationId}/publish")
    @ApiOperation(value = "发布邀约计划", notes = "将指定邀约计划的状态设置为'已发布'，仅允许未发布的邀约操作")
    public AjaxResult publish(
            @ApiParam(value = "邀约计划ID（主键）", required = true, example = "123")
            @PathVariable Long invitationId) {
        boolean success = invitationService.publishInvitation(invitationId);
        return AjaxResult.success(success ? "发布成功" : "发布失败");
    }

    /**
     * 查询邀约计划列表（支持分页+多条件过滤）
     */
    @GetMapping("/queryByNameOrNo")
    @ApiOperation("按名称或编号查询邀约计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invitationName", value = "邀约计划名称（模糊匹配）", dataType = "string"),
            @ApiImplicitParam(name = "invitationNo", value = "邀约计划编号（模糊匹配）", dataType = "string"),
            @ApiImplicitParam(name = "userId", value = "聚合商ID", dataType = "integer", defaultValue = "10")
    })
    public List<VppExchangeInvitation> queryByNameOrNo(
            @RequestParam(required = false) String invitationName,
            @RequestParam(required = false) String invitationNo,
            @RequestParam(required = false) Integer userId) {

        Map<String, Object> params = new HashMap<>();
        params.put("invitationName", invitationName != null ? invitationName.trim() : "");
        params.put("invitationNo", invitationNo != null ? invitationNo.trim() : "");
        params.put("userId", userId);

        return invitationService.queryByNameOrNo(params);
    }

    /**
     * 根据邀约ID更新锁单状态和锁单时间
     */
    @PutMapping("/lock/{invitationId}")
    @ApiOperation("更新邀约锁单状态")
    public AjaxResult updateLockStatus(
            @PathVariable Long invitationId,
            @RequestBody Map<String, String> params) {
        try {
            // 校验请求体参数
            if (!params.containsKey("lockStatus")) {
                return AjaxResult.error("锁单状态参数缺失");
            }
            String lockStatus = params.get("lockStatus");

            // 调用Service更新
            VppExchangeInvitation updatedInvitation = invitationService.updateLockStatus(invitationId, lockStatus);
            return AjaxResult.success(updatedInvitation);
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("参数错误：" + e.getMessage());
        } catch (RuntimeException e) {
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }
}