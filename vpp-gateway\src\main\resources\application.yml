server:
  port: 8090
# 需要注册到nacos或
spring:
  cloud:
    service-registry:
      auto-registration:
        enabled: true
        register-management: true
      enabled: true
    gateway:
      server:
        webflux:
          routes:
          - id: vpp-service
            uri: 'http://localhost:9801'
            predicates:
              - Path=/** # v0 版本
            filters:
              - StripPrefix=0
          discovery:
            locator:
              enabled: true
          globalcors:
            cors-configurations:
              '[/**]':
                allowedOrigins:
                  - '*'
                allowedMethods:
                  - '*'
                maxAge: 180
management:
  endpoints:
    web:
      exposure:
        include:
          - '*'