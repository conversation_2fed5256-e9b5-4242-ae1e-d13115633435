package com.vpp.execute.service;

import com.vpp.execute.domain.ExecutePlan;

import java.util.List;

/**
 * 执行计划，中标后的执行计划Service接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface IExecutePlanService {
    /**
     * 查询执行计划，中标后的执行计划
     *
     * @param executePlanId 执行计划，中标后的执行计划主键
     * @return 执行计划，中标后的执行计划
     */
    public ExecutePlan selectExecutePlanByExecutePlanId(Long executePlanId);

    /**
     * 查询执行计划，中标后的执行计划列表
     *
     * @param executePlan 执行计划，中标后的执行计划
     * @return 执行计划，中标后的执行计划集合
     */
    public List<ExecutePlan> selectExecutePlanList(ExecutePlan executePlan);

    /**
     * 新增执行计划，中标后的执行计划
     *
     * @param executePlan 执行计划，中标后的执行计划
     * @return 结果
     */
    public int insertExecutePlan(ExecutePlan executePlan);

    /**
     * 修改执行计划，中标后的执行计划
     *
     * @param executePlan 执行计划，中标后的执行计划
     * @return 结果
     */
    public int updateExecutePlan(ExecutePlan executePlan);

    /**
     * 批量删除执行计划，中标后的执行计划
     *
     * @param executePlanIds 需要删除的执行计划，中标后的执行计划主键集合
     * @return 结果
     */
    public int deleteExecutePlanByExecutePlanIds(Long[] executePlanIds);

    /**
     * 删除执行计划，中标后的执行计划信息
     *
     * @param executePlanId 执行计划，中标后的执行计划主键
     * @return 结果
     */
    public int deleteExecutePlanByExecutePlanId(Long executePlanId);
}
