package com.vpp.web.controller.sm;

import com.vpp.sm.domain.VppSettlementRecord;
import com.vpp.sm.service.IVppSettlementRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 结算记录控制器（Swagger接口）
 */
@RestController
@RequestMapping("/api/settlement")
@Api(tags = "结算管理-市场结算接口", description = "提供市场结算的查询、插入等服务")
public class VppSettlementRecordController {

    @Autowired
    private IVppSettlementRecordService settlementRecordService;

    /**
     * 根据邀约计划ID查询结算记录列表
     * @param invitationId 邀约计划ID（示例：1）
     * @return 结算记录列表（JSON格式）
     */
    @GetMapping("/byInvitation")
    @ApiOperation(value = "根据邀约计划ID查询结算记录", notes = "参数：invitationId（邀约计划ID）")
    public List<VppSettlementRecord> getRecordsByInvitationId(@RequestParam Long invitationId) {
        return settlementRecordService.getRecordsByInvitationId(invitationId);
    }

    /**
     * 插入新的结算记录
     * @param record 结算记录实体（JSON格式）
     * @return 成功插入的记录数（正常为1）
     */
    @PostMapping("/insert")
    @ApiOperation(value = "插入结算记录", notes = "参数：VppSettlementRecord实体（需包含invitationId、settlementTime等）")
    public int insertRecord(@RequestBody VppSettlementRecord record) {
        return settlementRecordService.insertRecord(record);
    }

    /**
     * 获取所有结算记录（用于统计概览）
     * @return 结算记录列表（JSON格式）
     */
    @GetMapping("/all")
    @ApiOperation(value = "获取所有结算记录（统计概览用）", notes = "返回所有结算记录，用于前端统计展示")
    public List<VppSettlementRecord> getAllRecords() {
        return settlementRecordService.getAllRecords();
    }

}