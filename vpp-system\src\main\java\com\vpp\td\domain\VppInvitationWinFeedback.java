package com.vpp.td.domain;

import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 邀约中标反馈表
 */
@Data
public class VppInvitationWinFeedback extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long winFeedbackId;

    /** 关联邀约计划ID */
    private Long invitationId;

    /** 关联申报反馈ID */
    private Long declarationId;

    /** 用户ID（关联vpp_user_type.type_id） */
    private Long userId;

    /** 中标总响应量（kWh） */
    private BigDecimal totalWinQuantity;

    /** 分段中标响应量（kWh） */
    private BigDecimal segmentWinQuantity;

    /** 发送状态（0-未发送，1-已发送） */
    private Integer sendStatus;

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("winFeedbackId", getWinFeedbackId())
                .append("invitationId", getInvitationId())
                .append("declarationId", getDeclarationId())
                .append("userId", getUserId())
                .append("totalWinQuantity", getTotalWinQuantity())
                .append("segmentWinQuantity", getSegmentWinQuantity())
                .append("sendStatus", getSendStatus())
                .toString();
    }
}