package com.vpp.execute.service;

import com.vpp.execute.domain.ExecuteDevicePower;

import java.util.Date;
import java.util.List;

/**
 * 设备实时检测功率Service接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface IExecuteDevicePowerService {
    /**
     * 查询设备实时检测功率
     *
     * @param timestamp 设备实时检测功率主键
     * @return 设备实时检测功率
     */
    public ExecuteDevicePower selectExecuteDevicePowerByTimestamp(Date timestamp);

    /**
     * 查询设备实时检测功率列表
     *
     * @param executeDevicePower 设备实时检测功率
     * @return 设备实时检测功率集合
     */
    public List<ExecuteDevicePower> selectExecuteDevicePowerList(ExecuteDevicePower executeDevicePower);

    /**
     * 新增设备实时检测功率
     *
     * @param executeDevicePower 设备实时检测功率
     * @return 结果
     */
    public int insertExecuteDevicePower(ExecuteDevicePower executeDevicePower);

    /**
     * 修改设备实时检测功率
     *
     * @param executeDevicePower 设备实时检测功率
     * @return 结果
     */
    public int updateExecuteDevicePower(ExecuteDevicePower executeDevicePower);

    /**
     * 批量删除设备实时检测功率
     *
     * @param timestamps 需要删除的设备实时检测功率主键集合
     * @return 结果
     */
    public int deleteExecuteDevicePowerByTimestamps(Date[] timestamps);

    /**
     * 删除设备实时检测功率信息
     *
     * @param timestamp 设备实时检测功率主键
     * @return 结果
     */
    public int deleteExecuteDevicePowerByTimestamp(Date timestamp);
}
