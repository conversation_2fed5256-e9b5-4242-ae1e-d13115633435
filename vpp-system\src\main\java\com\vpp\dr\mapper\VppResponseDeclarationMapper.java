package com.vpp.dr.mapper;

import com.vpp.dr.domain.VppResponseDeclaration;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface VppResponseDeclarationMapper {
    /**
     * 关联查询响应申报列表（含邀约计划信息）
     * @param params 查询条件
     * @return 列表
     */
    List<VppResponseDeclaration> selectList(@Param("params") Map<String, Object> params);

    /**
     * 查询总记录数
     * @param params 查询条件
     * @return 总数
     */
    int selectCount(@Param("params") Map<String, Object> params);

    /**
     * 根据申报ID查询详情（含邀约计划信息）
     * @param declarationId 申报ID
     * @return 实体
     */
    VppResponseDeclaration selectById(Long declarationId);

    /**
     * 根据邀约计划ID查询申报记录
     * @param invitationId 邀约计划ID
     * @return 实体
     */
    VppResponseDeclaration selectByInvitationId(Long invitationId);

    /**
     * 新增响应申报
     * @param declaration 实体
     * @return 影响行数
     */
    int insert(VppResponseDeclaration declaration);

    /**
     * 更新响应申报
     * @param declaration 实体
     * @return 影响行数
     */
    int update(VppResponseDeclaration declaration);

    /**
     * 逻辑删除响应申报
     * @param declarationId 申报ID
     * @return 影响行数
     */
    int deleteById(Long declarationId);

    /**
     * 修改申报状态
     * @param declaration
     * @return 影响行数
     */
    int updateDeclarationStatus(VppResponseDeclaration declaration);

    /**
     * 批量导入响应申报
     * @param list 数据列表
     * @return 影响行数
     */
    int batchImport(List<VppResponseDeclaration> list);
}