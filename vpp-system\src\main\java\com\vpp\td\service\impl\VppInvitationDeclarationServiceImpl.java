package com.vpp.td.service.impl;

import com.vpp.td.domain.VppInvitationDeclaration;
import com.vpp.td.mapper.VppInvitationDeclarationMapper;
import com.vpp.td.service.IVppInvitationDeclarationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class VppInvitationDeclarationServiceImpl implements IVppInvitationDeclarationService {

    @Autowired
    private VppInvitationDeclarationMapper declarationMapper;

    @Override
    public List<VppInvitationDeclaration> selectList(Map<String, Object> params) {
        if (params == null) params = new HashMap<>();
        return declarationMapper.selectList(params);
    }

    @Override
    public VppInvitationDeclaration selectById(Long declarationId) {
        return declarationMapper.selectById(declarationId);
    }

    @Override
    public int insert(VppInvitationDeclaration declaration) {
        return declarationMapper.insert(declaration);
    }

    @Override
    public int update(VppInvitationDeclaration declaration) {
        return declarationMapper.update(declaration);
    }

    @Override
    public int deleteById(Long declarationId) {
        return declarationMapper.deleteById(declarationId);
    }
}