package com.vpp.mgr.mapper;

import com.vpp.mgr.domain.VppMgrUag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聚合商用户-信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface VppMgrUagMapper {
    /**
     * 查询聚合商用户-信息
     *
     * @param uagId 聚合商用户-信息主键
     * @return 聚合商用户-信息
     */
    public VppMgrUag selectVppMgrUagByUagId(Long uagId);

    /**
     * 查询聚合商用户-信息列表
     *
     * @param vppMgrUag 聚合商用户-信息
     * @return 聚合商用户-信息集合
     */
    public List<VppMgrUag> selectVppMgrUagList(VppMgrUag vppMgrUag);

    /**
     * 新增聚合商用户-信息
     *
     * @param vppMgrUag 聚合商用户-信息
     * @return 结果
     */
    public int insertVppMgrUag(VppMgrUag vppMgrUag);

    /**
     * 修改聚合商用户-信息
     *
     * @param vppMgrUag 聚合商用户-信息
     * @return 结果
     */
    public int updateVppMgrUag(VppMgrUag vppMgrUag);

    /**
     * 删除聚合商用户-信息
     *
     * @param uagId 聚合商用户-信息主键
     * @return 结果
     */
    public int deleteVppMgrUagByUagId(Long uagId);

    /**
     * 批量删除聚合商用户-信息
     *
     * @param uagIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVppMgrUagByUagIds(Long[] uagIds);

    List<VppMgrUag> listByUserIds(@Param("userIds") List<Long> userIds);

    VppMgrUag selectVppMgrUagByDeptId(Long deptId);

    List<VppMgrUag> listByUserId(Long userId);

    List<VppMgrUag> selectVppMgrUagNoCnt(@Param("deptId") Long deptId);
}