<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.plan.mapper.PlanGenDevicesMapper">

    <resultMap type="PlanGenDevices" id="PlanGenDevicesResult">
        <result property="planGenDevicesId"    column="plan_gen_devices_id"    />
        <result property="planGenId"    column="plan_gen_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="planGenRatedPower"    column="plan_gen_rated_power"    />
        <result property="planGenRatedVoltage"    column="plan_gen_rated_voltage"    />
        <result property="planGenRatedCurrent"    column="plan_gen_rated_current"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlanGenDevicesVo">
        select plan_gen_devices_id, plan_gen_id, device_id, plan_gen_rated_power, plan_gen_rated_voltage, plan_gen_rated_current, create_by, create_time, update_by, update_time, remark from plan_gen_devices
    </sql>

    <select id="selectPlanGenDevicesList" parameterType="PlanGenDevices" resultMap="PlanGenDevicesResult">
        <include refid="selectPlanGenDevicesVo"/>
        <where>
            <if test="planGenId != null "> and plan_gen_id = #{planGenId}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="planGenRatedPower != null "> and plan_gen_rated_power = #{planGenRatedPower}</if>
            <if test="planGenRatedVoltage != null "> and plan_gen_rated_voltage = #{planGenRatedVoltage}</if>
            <if test="planGenRatedCurrent != null "> and plan_gen_rated_current = #{planGenRatedCurrent}</if>
        </where>
    </select>

    <select id="selectPlanGenDevicesByPlanGenDevicesId" parameterType="Long" resultMap="PlanGenDevicesResult">
        <include refid="selectPlanGenDevicesVo"/>
        where plan_gen_devices_id = #{planGenDevicesId}
    </select>

    <insert id="insertPlanGenDevices" parameterType="PlanGenDevices" useGeneratedKeys="true" keyProperty="planGenDevicesId">
        insert into plan_gen_devices
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planGenId != null">plan_gen_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="planGenRatedPower != null">plan_gen_rated_power,</if>
            <if test="planGenRatedVoltage != null">plan_gen_rated_voltage,</if>
            <if test="planGenRatedCurrent != null">plan_gen_rated_current,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planGenId != null">#{planGenId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="planGenRatedPower != null">#{planGenRatedPower},</if>
            <if test="planGenRatedVoltage != null">#{planGenRatedVoltage},</if>
            <if test="planGenRatedCurrent != null">#{planGenRatedCurrent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePlanGenDevices" parameterType="PlanGenDevices">
        update plan_gen_devices
        <trim prefix="SET" suffixOverrides=",">
            <if test="planGenId != null">plan_gen_id = #{planGenId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="planGenRatedPower != null">plan_gen_rated_power = #{planGenRatedPower},</if>
            <if test="planGenRatedVoltage != null">plan_gen_rated_voltage = #{planGenRatedVoltage},</if>
            <if test="planGenRatedCurrent != null">plan_gen_rated_current = #{planGenRatedCurrent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where plan_gen_devices_id = #{planGenDevicesId}
    </update>

    <delete id="deletePlanGenDevicesByPlanGenDevicesId" parameterType="Long">
        delete from plan_gen_devices where plan_gen_devices_id = #{planGenDevicesId}
    </delete>

    <delete id="deletePlanGenDevicesByPlanGenDevicesIds" parameterType="String">
        delete from plan_gen_devices where plan_gen_devices_id in
        <foreach item="planGenDevicesId" collection="array" open="(" separator="," close=")">
            #{planGenDevicesId}
        </foreach>
    </delete>
</mapper>