package com.vpp.execute.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.execute.domain.ExecutePlan;
import com.vpp.execute.mapper.ExecutePlanMapper;
import com.vpp.execute.service.IExecutePlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 执行计划，中标后的执行计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class ExecutePlanServiceImpl implements IExecutePlanService {
    @Autowired
    private ExecutePlanMapper executePlanMapper;

    /**
     * 查询执行计划，中标后的执行计划
     *
     * @param executePlanId 执行计划，中标后的执行计划主键
     * @return 执行计划，中标后的执行计划
     */
    @Override
    public ExecutePlan selectExecutePlanByExecutePlanId(Long executePlanId) {
        return executePlanMapper.selectExecutePlanByExecutePlanId(executePlanId);
    }

    /**
     * 查询执行计划，中标后的执行计划列表
     *
     * @param executePlan 执行计划，中标后的执行计划
     * @return 执行计划，中标后的执行计划
     */
    @Override
    public List<ExecutePlan> selectExecutePlanList(ExecutePlan executePlan) {
        return executePlanMapper.selectExecutePlanList(executePlan);
    }

    /**
     * 新增执行计划，中标后的执行计划
     *
     * @param executePlan 执行计划，中标后的执行计划
     * @return 结果
     */
    @Override
    public int insertExecutePlan(ExecutePlan executePlan) {
        executePlan.setCreateTime(DateUtils.getNowDate());
        return executePlanMapper.insertExecutePlan(executePlan);
    }

    /**
     * 修改执行计划，中标后的执行计划
     *
     * @param executePlan 执行计划，中标后的执行计划
     * @return 结果
     */
    @Override
    public int updateExecutePlan(ExecutePlan executePlan) {
        executePlan.setUpdateTime(DateUtils.getNowDate());
        return executePlanMapper.updateExecutePlan(executePlan);
    }

    /**
     * 批量删除执行计划，中标后的执行计划
     *
     * @param executePlanIds 需要删除的执行计划，中标后的执行计划主键
     * @return 结果
     */
    @Override
    public int deleteExecutePlanByExecutePlanIds(Long[] executePlanIds) {
        return executePlanMapper.deleteExecutePlanByExecutePlanIds(executePlanIds);
    }

    /**
     * 删除执行计划，中标后的执行计划信息
     *
     * @param executePlanId 执行计划，中标后的执行计划主键
     * @return 结果
     */
    @Override
    public int deleteExecutePlanByExecutePlanId(Long executePlanId) {
        return executePlanMapper.deleteExecutePlanByExecutePlanId(executePlanId);
    }
}