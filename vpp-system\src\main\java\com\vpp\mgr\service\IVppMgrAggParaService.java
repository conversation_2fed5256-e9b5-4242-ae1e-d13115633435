package com.vpp.mgr.service;

import com.vpp.mgr.domain.VppMgrAggPara;

import java.util.List;

/**
 * 聚合商-技术参数Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IVppMgrAggParaService {
    /**
     * 查询聚合商-技术参数
     *
     * @param userId 聚合商-技术参数主键
     * @return 聚合商-技术参数
     */
    public VppMgrAggPara selectVppMgrAggParaByAggParaId(Long userId);

    /**
     * 查询聚合商-技术参数列表
     *
     * @param vppMgrAggPara 聚合商-技术参数
     * @return 聚合商-技术参数集合
     */
    public List<VppMgrAggPara> selectVppMgrAggParaList(VppMgrAggPara vppMgrAggPara);

    /**
     * 新增聚合商-技术参数
     *
     * @param vppMgrAggPara 聚合商-技术参数
     * @return 结果
     */
    public int insertVppMgrAggPara(VppMgrAggPara vppMgrAggPara);

    /**
     * 修改聚合商-技术参数
     *
     * @param vppMgrAggPara 聚合商-技术参数
     * @return 结果
     */
    public int updateVppMgrAggPara(VppMgrAggPara vppMgrAggPara);

    /**
     * 批量删除聚合商-技术参数
     *
     * @param aggParaIds 需要删除的聚合商-技术参数主键集合
     * @return 结果
     */
    public int deleteVppMgrAggParaByAggParaIds(Long[] aggParaIds);

    /**
     * 删除聚合商-技术参数信息
     *
     * @param aggParaId 聚合商-技术参数主键
     * @return 结果
     */
    public int deleteVppMgrAggParaByAggParaId(Long aggParaId);
}
