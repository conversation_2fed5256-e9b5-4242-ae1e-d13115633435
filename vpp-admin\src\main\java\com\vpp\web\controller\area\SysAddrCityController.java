package com.vpp.web.controller.area;

import com.vpp.area.domain.SysAddrCity;
import com.vpp.area.service.ISysAddrCityService;
import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 城市设置Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/system/city")
public class SysAddrCityController extends BaseController
{
    @Autowired
    private ISysAddrCityService sysAddrCityService;

    /**
     * 查询城市设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:city:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAddrCity sysAddrCity)
    {
        startPage();
        List<SysAddrCity> list = sysAddrCityService.selectSysAddrCityList(sysAddrCity);
        return getDataTable(list);
    }

    /**
     * 导出城市设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:city:export')")
    @Log(title = "城市设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAddrCity sysAddrCity)
    {
        List<SysAddrCity> list = sysAddrCityService.selectSysAddrCityList(sysAddrCity);
        ExcelUtil<SysAddrCity> util = new ExcelUtil<SysAddrCity>(SysAddrCity.class);
        util.exportExcel(response, list, "城市设置数据");
    }

    /**
     * 获取城市设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:city:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sysAddrCityService.selectSysAddrCityById(id));
    }

    /**
     * 新增城市设置
     */
    @PreAuthorize("@ss.hasPermi('system:city:add')")
    @Log(title = "城市设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAddrCity sysAddrCity)
    {
        return toAjax(sysAddrCityService.insertSysAddrCity(sysAddrCity));
    }

    /**
     * 修改城市设置
     */
    @PreAuthorize("@ss.hasPermi('system:city:edit')")
    @Log(title = "城市设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAddrCity sysAddrCity)
    {
        return toAjax(sysAddrCityService.updateSysAddrCity(sysAddrCity));
    }

    /**
     * 删除城市设置
     */
    @PreAuthorize("@ss.hasPermi('system:city:remove')")
    @Log(title = "城市设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(sysAddrCityService.deleteSysAddrCityByIds(ids));
    }
}