package com.vpp.re.mapper;

import com.vpp.re.domain.VppEventScore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评分综合与明细表 Mapper
 */
@Mapper
public interface VppEventScoreMapper {

    /**
     * 插入评分记录
     * @param score 评分实体
     * @return 影响行数
     */
    int insert(VppEventScore score);

    /**
     * 根据ID更新评分记录
     * @param score 评分实体（需包含itemId）
     * @return 影响行数
     */
    int updateById(VppEventScore score);

    /**
     * 根据ID删除评分记录（逻辑删除）
     * @param itemId 评分项目ID
     * @return 影响行数
     */
    int deleteById(@Param("itemId") Long itemId);

    /**
     * 根据ID查询评分记录（未删除）
     * @param itemId 评分项目ID
     * @return 评分实体（不存在则返回null）
     */
    VppEventScore selectById(@Param("itemId") Long itemId);

    /**
     * 根据邀约计划ID分页查询评分记录（未删除）
     * @param invitationId 邀约计划ID
     * @param offset 起始偏移量
     * @param limit 每页数量
     * @return 评分记录列表
     */
    List<VppEventScore> selectByInvitationId(
            @Param("invitationId") Long invitationId,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );
}