package com.vpp.td.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 邀约申报反馈表
 */
@Data
public class VppInvitationDeclaration extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long declarationId;

    /** 关联邀约计划ID */
    private Long invitationId;

    /** 用户ID（关联vpp_user_type.type_id） */
    private Long userId;

    /** 用户类型编码（关联vpp_user_type.type_code，如：PV_USER） */
    private String userTypeCode;

    /** 用户报价（元） */
    private BigDecimal quotePrice;

    /** 用户响应量（kWh） */
    private BigDecimal responseQuantity;

    /** 最大可调节功率（kW） */
    private BigDecimal maxAdjustPower;

    /** 调节用户响应量（kWh） */
    private BigDecimal adjustQuantity;

    /** 申报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date declarationTime;

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("declarationId", getDeclarationId())
                .append("invitationId", getInvitationId())
                .append("userId", getUserId())
                .append("userTypeCode", getUserTypeCode())
                .append("quotePrice", getQuotePrice())
                .append("responseQuantity", getResponseQuantity())
                .append("maxAdjustPower", getMaxAdjustPower())
                .append("adjustQuantity", getAdjustQuantity())
                .append("declarationTime", getDeclarationTime())
                .toString();
    }
}