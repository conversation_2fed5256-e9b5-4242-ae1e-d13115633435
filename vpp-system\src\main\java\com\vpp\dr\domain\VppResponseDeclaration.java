package com.vpp.dr.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 响应申报表实体类（Swagger注解版）
 */
@Data
@ApiModel(value = "VppResponseDeclaration", description = "响应申报表实体，用于存储用户对邀约计划的响应申报信息")
public class VppResponseDeclaration implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 关联的邀约计划对象 */
    private VppExchangeInvitation invitation; // 新增属性

    /**
     * 响应申报ID（主键）
     */
    @ApiModelProperty(value = "响应申报ID（主键）", example = "123456789", required = true)
    private Long declarationId;

    /**
     * 关联邀约计划ID（来自vpp_exchange_invitation表）
     */
    @ApiModelProperty(value = "关联邀约计划ID", example = "987654321", required = true)
    private Long invitationId;

    /**
     * 邀约计划名称（来自vpp_exchange_invitation表）
     */
    @ApiModelProperty(value = "邀约计划名称", example = "夏季尖峰响应计划", required = true)
    private String invitationName;

    /**
     * 邀约计划编号（来自vpp_exchange_invitation表）
     */
    @ApiModelProperty(value = "邀约计划编号", example = "XP20240701-001", required = true)
    private String invitationNo;

    /**
     * 发布来源（邀约发布/交易中心）
     */
    @ApiModelProperty(value = "发布来源（邀约发布/交易中心）", example = "交易中心", required = true)
    private String sourceRelease;

    /**
     * 响应日（YYYY-MM-DD格式）
     */
    @ApiModelProperty(value = "响应日（YYYY-MM-DD格式）", example = "2024-07-15", required = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String responseDate;

    /**
     * 需求时段（如：08:00-12:00）
     */
    @ApiModelProperty(value = "需求时段（如：08:00-12:00）", example = "08:00-12:00", required = true)
    private String demandTimeSlots;

    /**
     * 需求地区（如：华东电网）
     */
    @ApiModelProperty(value = "需求地区（如：华东电网）", example = "华东电网", required = true)
    private String demandRegion;

    /**
     * 响应容量（MW，用户申报可响应容量）
     */
    @ApiModelProperty(value = "响应容量（MW）", example = "100.5", required = true)
    private BigDecimal responseCapacity;

    /**
     * 用户申报总功率（MW，用户实际申报功率总和）
     */
    @ApiModelProperty(value = "用户申报总功率（MW）", example = "95.2", required = true)
    private BigDecimal userTotalPower;

    /**
     * 聚合商报价（元/MW，聚合商提交的响应价格）
     */
    @ApiModelProperty(value = "聚合商报价（元/MW）", example = "0.35", required = false)
    private BigDecimal aggregatorBid;

    /**
     * 用户平均报价（元/MW，用户申报价格的平均值）
     */
    @ApiModelProperty(value = "用户平均报价（元/MW）", example = "0.38", required = false)
    private BigDecimal userAvgBid;

    /**
     * 聚合商响应状态（枚举值：待命/运行/完成/失败）
     */
    @ApiModelProperty(value = "聚合商响应状态（枚举值：待命/运行/完成/失败）", example = "运行", required = true)
    private String aggregatorStatus;

    /**
     * 事件状态（枚举值：未开始/已开始/已结束/已取消）
     */
    @ApiModelProperty(value = "事件状态（枚举值：未开始/已开始/已结束/已取消）", example = "已开始", required = true)
    private String eventStatus;

    /**
     * 受邀用户数量（实际参与响应的用户数）
     */
    @ApiModelProperty(value = "受邀用户数量", example = "50", required = true)
    private Integer invitedUserCount;

    /**
     * 受邀用户数量（实际参与响应的用户数）
     */
    @ApiModelProperty(value = "响应类型（枚举值：日前响应/日内响应）", example = "日前响应", required = true)
    private String responseType;

    /**
     * 受邀用户数量（实际参与响应的用户数）
     */
    @ApiModelProperty(value = "负荷方向（枚举值：填谷响应/削峰响应）", example = "填谷响应", required = true)
    private String loadDirection;

    /**
     * 受邀用户数量（实际参与响应的用户数）
     */
    @ApiModelProperty(value = "市场邀约容量 单位：MW", example = "600000.00", required = true)
    private double marketCapacity;

    /**
     * 锁单状态（枚举值：未锁单/已锁单）
     */
    @ApiModelProperty(value = "锁单状态（枚举值：未锁单/已锁单）", example = "未锁单", required = true)
    private String lockedFlag;

    /**
     * 申报状态（枚举值：未申报/已申报/部分申报）
     */
    @ApiModelProperty(value = "申报状态（枚举值：未申报/已申报/部分申报）", example = "已申报", required = true)
    private String declarationStatus;

    /**
     * 申报时间（用户/聚合商提交申报的时间）
     */
    @ApiModelProperty(value = "申报时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-07-15 10:00:00", required = true)
    private LocalDateTime declarationTime;

    /**
     * 创建者（系统用户账号）
     */
    @ApiModelProperty(value = "创建者（系统用户账号）", example = "admin", required = true)
    private String createBy;

    /**
     * 创建时间（系统自动生成）
     */
    @ApiModelProperty(value = "创建时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-07-15 09:59:00", required = true)
    private LocalDateTime createTime;

    /**
     * 更新者（系统用户账号）
     */
    @ApiModelProperty(value = "更新者（系统用户账号）", example = "operator", required = false)
    private String updateBy;

    /**
     * 更新时间（系统自动更新）
     */
    @ApiModelProperty(value = "更新时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-07-15 10:05:00", required = false)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0-未删除，2-已删除）
     */
    @ApiModelProperty(value = "删除标志（0-未删除，2-已删除）", example = "0", required = true)
    private String delFlag;

    @Override
    public String toString() {
        return "VppResponseDeclaration{" +
                "declarationId=" + declarationId +
                ", invitationId=" + invitationId +
                ", invitationName='" + invitationName + '\'' +
                ", invitationNo='" + invitationNo + '\'' +
                ", sourceRelease='" + sourceRelease + '\'' +
                ", responseDate='" + responseDate + '\'' +
                ", demandTimeSlots='" + demandTimeSlots + '\'' +
                ", demandRegion='" + demandRegion + '\'' +
                ", responseCapacity=" + responseCapacity +
                ", userTotalPower=" + userTotalPower +
                ", aggregatorBid=" + aggregatorBid +
                ", userAvgBid=" + userAvgBid +
                ", aggregatorStatus='" + aggregatorStatus + '\'' +
                ", eventStatus='" + eventStatus + '\'' +
                ", invitedUserCount=" + invitedUserCount +
                ", responseType=" + responseType +
                ", loadDirection=" + loadDirection +
                ", marketCapacity=" + marketCapacity +
                ", lockedFlag='" + lockedFlag + '\'' +
                ", declarationStatus='" + declarationStatus + '\'' +
                ", declarationTime=" + declarationTime +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }
}