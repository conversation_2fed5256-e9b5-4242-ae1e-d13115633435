package com.vpp.area.service.impl;

import com.vpp.area.domain.SysAddrCity;
import com.vpp.area.mapper.SysAddrCityMapper;
import com.vpp.area.service.ISysAddrCityService;
import com.vpp.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 城市设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class SysAddrCityServiceImpl implements ISysAddrCityService {
    @Autowired
    private SysAddrCityMapper sysAddrCityMapper;

    /**
     * 查询城市设置
     *
     * @param id 城市设置主键
     * @return 城市设置
     */
    @Override
    public SysAddrCity selectSysAddrCityById(String id) {
        return sysAddrCityMapper.selectSysAddrCityById(id);
    }

    /**
     * 查询城市设置列表
     *
     * @param sysAddrCity 城市设置
     * @return 城市设置
     */
    @Override
    public List<SysAddrCity> selectSysAddrCityList(SysAddrCity sysAddrCity) {
        return sysAddrCityMapper.selectSysAddrCityList(sysAddrCity);
    }

    /**
     * 新增城市设置
     *
     * @param sysAddrCity 城市设置
     * @return 结果
     */
    @Override
    public int insertSysAddrCity(SysAddrCity sysAddrCity) {
        sysAddrCity.setCreateTime(DateUtils.getNowDate());
        return sysAddrCityMapper.insertSysAddrCity(sysAddrCity);
    }

    /**
     * 修改城市设置
     *
     * @param sysAddrCity 城市设置
     * @return 结果
     */
    @Override
    public int updateSysAddrCity(SysAddrCity sysAddrCity) {
        sysAddrCity.setUpdateTime(DateUtils.getNowDate());
        return sysAddrCityMapper.updateSysAddrCity(sysAddrCity);
    }

    /**
     * 批量删除城市设置
     *
     * @param ids 需要删除的城市设置主键
     * @return 结果
     */
    @Override
    public int deleteSysAddrCityByIds(String[] ids) {
        return sysAddrCityMapper.deleteSysAddrCityByIds(ids);
    }

    /**
     * 删除城市设置信息
     *
     * @param id 城市设置主键
     * @return 结果
     */
    @Override
    public int deleteSysAddrCityById(String id) {
        return sysAddrCityMapper.deleteSysAddrCityById(id);
    }
}