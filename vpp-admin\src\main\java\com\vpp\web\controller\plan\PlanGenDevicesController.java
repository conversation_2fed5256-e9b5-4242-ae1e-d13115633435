package com.vpp.web.controller.plan;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import com.vpp.plan.domain.PlanGenDevices;
import com.vpp.plan.service.IPlanGenDevicesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 自动生成计划设备Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/vpp/genDevices")
public class PlanGenDevicesController extends BaseController {
    @Autowired
    private IPlanGenDevicesService planGenDevicesService;

    /**
     * 查询自动生成计划设备列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDevices:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanGenDevices planGenDevices) {
        startPage();
        List<PlanGenDevices> list = planGenDevicesService.selectPlanGenDevicesList(planGenDevices);
        return getDataTable(list);
    }

    /**
     * 导出自动生成计划设备列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDevices:export')")
    @Log(title = "自动生成计划设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanGenDevices planGenDevices) {
        List<PlanGenDevices> list = planGenDevicesService.selectPlanGenDevicesList(planGenDevices);
        ExcelUtil<PlanGenDevices> util = new ExcelUtil<PlanGenDevices>(PlanGenDevices.class);
        util.exportExcel(response, list, "自动生成计划设备数据");
    }

    /**
     * 获取自动生成计划设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDevices:query')")
    @GetMapping(value = "/{planGenDevicesId}")
    public AjaxResult getInfo(@PathVariable("planGenDevicesId") Long planGenDevicesId) {
        return success(planGenDevicesService.selectPlanGenDevicesByPlanGenDevicesId(planGenDevicesId));
    }

    /**
     * 新增自动生成计划设备
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDevices:add')")
    @Log(title = "自动生成计划设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanGenDevices planGenDevices) {
        return toAjax(planGenDevicesService.insertPlanGenDevices(planGenDevices));
    }

    /**
     * 修改自动生成计划设备
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDevices:edit')")
    @Log(title = "自动生成计划设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanGenDevices planGenDevices) {
        return toAjax(planGenDevicesService.updatePlanGenDevices(planGenDevices));
    }

    /**
     * 删除自动生成计划设备
     */
    @PreAuthorize("@ss.hasPermi('vpp:genDevices:remove')")
    @Log(title = "自动生成计划设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planGenDevicesIds}")
    public AjaxResult remove(@PathVariable Long[] planGenDevicesIds) {
        return toAjax(planGenDevicesService.deletePlanGenDevicesByPlanGenDevicesIds(planGenDevicesIds));
    }
}