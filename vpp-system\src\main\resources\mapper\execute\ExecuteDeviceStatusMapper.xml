<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.execute.mapper.ExecuteDeviceStatusMapper">

    <resultMap type="ExecuteDeviceStatus" id="ExecuteDeviceStatusResult">
        <result property="timestamp"    column="timestamp"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceStatus"    column="device_status"    />
    </resultMap>

    <sql id="selectExecuteDeviceStatusVo">
        select timestamp, device_id, device_status from execute_device_status
    </sql>

    <select id="selectExecuteDeviceStatusList" parameterType="ExecuteDeviceStatus" resultMap="ExecuteDeviceStatusResult">
        <include refid="selectExecuteDeviceStatusVo"/>
        <where>
            <if test="deviceStatus != null "> and device_status = #{deviceStatus}</if>
        </where>
    </select>

    <select id="selectExecuteDeviceStatusByTimestamp" parameterType="Date" resultMap="ExecuteDeviceStatusResult">
        <include refid="selectExecuteDeviceStatusVo"/>
        where timestamp = #{timestamp}
    </select>

    <insert id="insertExecuteDeviceStatus" parameterType="ExecuteDeviceStatus">
        insert into execute_device_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">timestamp,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="deviceStatus != null">device_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="timestamp != null">#{timestamp},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceStatus != null">#{deviceStatus},</if>
        </trim>
    </insert>

    <update id="updateExecuteDeviceStatus" parameterType="ExecuteDeviceStatus">
        update execute_device_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
        </trim>
        where timestamp = #{timestamp}
    </update>

    <delete id="deleteExecuteDeviceStatusByTimestamp" parameterType="Date">
        delete from execute_device_status where timestamp = #{timestamp}
    </delete>

    <delete id="deleteExecuteDeviceStatusByTimestamps" parameterType="String">
        delete from execute_device_status where timestamp in
        <foreach item="timestamp" collection="array" open="(" separator="," close=")">
            #{timestamp}
        </foreach>
    </delete>
</mapper>