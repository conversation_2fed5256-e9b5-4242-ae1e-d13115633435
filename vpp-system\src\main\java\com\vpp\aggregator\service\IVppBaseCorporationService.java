package com.vpp.aggregator.service;

import com.vpp.aggregator.domain.VppBaseCorporation;

import java.util.List;

/**
 * 公司主体Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IVppBaseCorporationService {
    /**
     * 查询公司主体
     *
     * @param corporationId 公司主体主键
     * @return 公司主体
     */
    public VppBaseCorporation selectVppBaseCorporationByCorporationId(Long corporationId);

    /**
     * 查询公司主体列表
     *
     * @param vppBaseCorporation 公司主体
     * @return 公司主体集合
     */
    public List<VppBaseCorporation> selectVppBaseCorporationList(VppBaseCorporation vppBaseCorporation);

    /**
     * 新增公司主体
     *
     * @param vppBaseCorporation 公司主体
     * @return 结果
     */
    public int insertVppBaseCorporation(VppBaseCorporation vppBaseCorporation);

    /**
     * 修改公司主体
     *
     * @param vppBaseCorporation 公司主体
     * @return 结果
     */
    public int updateVppBaseCorporation(VppBaseCorporation vppBaseCorporation);

    /**
     * 批量删除公司主体
     *
     * @param corporationIds 需要删除的公司主体主键集合
     * @return 结果
     */
    public int deleteVppBaseCorporationByCorporationIds(Long[] corporationIds);

    /**
     * 删除公司主体信息
     *
     * @param corporationId 公司主体主键
     * @return 结果
     */
    public int deleteVppBaseCorporationByCorporationId(Long corporationId);
}
