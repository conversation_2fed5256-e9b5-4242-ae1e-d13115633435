package com.vpp.web.controller.aggregator;

import com.vpp.aggregator.domain.VppVendor;
import com.vpp.aggregator.service.IVppVendorService;
import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 虚拟电厂供应商，是vpp_base虚拟电厂的子关系Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/vpp/vendor")
// @Api(tags = "虚拟电厂供应商，是vpp_base虚拟电厂的子关系")
public class VppVendorController extends BaseController {
    @Autowired
    private IVppVendorService vppVendorService;

    /**
     * 查询虚拟电厂供应商，是vpp_base虚拟电厂的子关系列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:vendor:list')")
    @GetMapping("/list")
    // @ApiOperation(value = "查询虚拟电厂供应商")
    public TableDataInfo list(VppVendor vppVendor) {
        startPage();
        List<VppVendor> list = vppVendorService.selectVppVendorList(vppVendor);
        return getDataTable(list);
    }

    /**
     * 导出虚拟电厂供应商，是vpp_base虚拟电厂的子关系列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:vendor:export')")
    @Log(title = "虚拟电厂供应商，是vpp_base虚拟电厂的子关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    // @ApiOperation(value = "导出虚拟电厂供应商")
    public void export(HttpServletResponse response, VppVendor vppVendor) {
        List<VppVendor> list = vppVendorService.selectVppVendorList(vppVendor);
        ExcelUtil<VppVendor> util = new ExcelUtil<VppVendor>(VppVendor.class);
        util.exportExcel(response, list, "虚拟电厂供应商，是vpp_base虚拟电厂的子关系数据");
    }

    /**
     * 获取虚拟电厂供应商，是vpp_base虚拟电厂的子关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('vpp:vendor:query')")
    @GetMapping(value = "/{vendorId}")
    // @ApiOperation(value = "获取虚拟电厂供应商详细信息")
    public AjaxResult getInfo(@PathVariable("vendorId") Long vendorId) {
        return success(vppVendorService.selectVppVendorByVendorId(vendorId));
    }

    /**
     * 新增虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     */
    @PreAuthorize("@ss.hasPermi('vpp:vendor:add')")
    @Log(title = "虚拟电厂供应商，是vpp_base虚拟电厂的子关系", businessType = BusinessType.INSERT)
    @PostMapping
    // @ApiOperation(value = "新增虚拟电厂供应商")
    public AjaxResult add(@RequestBody VppVendor vppVendor) {
        return toAjax(vppVendorService.insertVppVendor(vppVendor));
    }

    /**
     * 修改虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     */
    @PreAuthorize("@ss.hasPermi('vpp:vendor:edit')")
    @Log(title = "虚拟电厂供应商，是vpp_base虚拟电厂的子关系", businessType = BusinessType.UPDATE)
    @PutMapping
    // @ApiOperation(value = "修改虚拟电厂供应商")
    public AjaxResult edit(@RequestBody VppVendor vppVendor) {
        return toAjax(vppVendorService.updateVppVendor(vppVendor));
    }

    /**
     * 删除虚拟电厂供应商，是vpp_base虚拟电厂的子关系
     */
    @PreAuthorize("@ss.hasPermi('vpp:vendor:remove')")
    @Log(title = "虚拟电厂供应商，是vpp_base虚拟电厂的子关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{vendorIds}")
    // @ApiOperation(value = "删除虚拟电厂供应商")
    public AjaxResult remove(@PathVariable Long[] vendorIds) {
        return toAjax(vppVendorService.deleteVppVendorByVendorIds(vendorIds));
    }
}