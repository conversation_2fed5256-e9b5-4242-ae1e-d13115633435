package com.vpp.web.controller.aggregator;

import com.vpp.aggregator.domain.VppBase;
import com.vpp.aggregator.service.IVppBaseService;
import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 虚拟电厂聚合商Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/vpp/base")
// @Api(tags="虚拟电厂聚合商")
public class VppBaseController extends BaseController {
    @Autowired
    private IVppBaseService vppBaseService;

    /**
     * 查询虚拟电厂聚合商列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:base:list')")
    @GetMapping("/list")
    // @ApiOperation(value = "获取虚拟电厂聚合商列表")
    public TableDataInfo list(VppBase vppBase) {
        startPage();
        List<VppBase> list = vppBaseService.selectVppBaseList(vppBase);
        return getDataTable(list);
    }

    /**
     * 导出虚拟电厂聚合商列表
     */
    @PreAuthorize("@ss.hasPermi('vpp:base:export')")
    @Log(title = "虚拟电厂聚合商", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    // @ApiOperation(value = "导出虚拟电厂聚合商列表")
    public void export(HttpServletResponse response, VppBase vppBase) {
        List<VppBase> list = vppBaseService.selectVppBaseList(vppBase);
        ExcelUtil<VppBase> util = new ExcelUtil<VppBase>(VppBase.class);
        util.exportExcel(response, list, "虚拟电厂聚合商数据");
    }

    /**
     * 获取虚拟电厂聚合商详细信息
     */
    @PreAuthorize("@ss.hasPermi('vpp:base:query')")
    @GetMapping(value = "/{vppId}")
    // @ApiOperation(value = "获取虚拟电厂聚合商详细信息")
    public AjaxResult getInfo(@PathVariable("vppId") Long vppId) {
        return success(vppBaseService.selectVppBaseByVppId(vppId));
    }

    /**
     * 新增虚拟电厂聚合商
     */
    @PreAuthorize("@ss.hasPermi('vpp:base:add')")
    @Log(title = "虚拟电厂聚合商", businessType = BusinessType.INSERT)
    @PostMapping
    // @ApiOperation(value = "新增虚拟电厂聚合商")
    public AjaxResult add(@RequestBody VppBase vppBase) {
        return toAjax(vppBaseService.insertVppBase(vppBase));
    }

    /**
     * 修改虚拟电厂聚合商
     */
    @PreAuthorize("@ss.hasPermi('vpp:base:edit')")
    @Log(title = "虚拟电厂聚合商", businessType = BusinessType.UPDATE)
    @PutMapping
    // @ApiOperation(value = "修改虚拟电厂聚合商")
    public AjaxResult edit(@RequestBody VppBase vppBase) {
        return toAjax(vppBaseService.updateVppBase(vppBase));
    }

    /**
     * 删除虚拟电厂聚合商
     */
    @PreAuthorize("@ss.hasPermi('vpp:base:remove')")
    @Log(title = "虚拟电厂聚合商", businessType = BusinessType.DELETE)
    @DeleteMapping("/{vppIds}")
    // @ApiOperation(value = "删除虚拟电厂聚合商")
    public AjaxResult remove(@PathVariable Long[] vppIds) {
        return toAjax(vppBaseService.deleteVppBaseByVppIds(vppIds));
    }
}