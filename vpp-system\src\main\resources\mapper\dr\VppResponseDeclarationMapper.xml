<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.dr.mapper.VppResponseDeclarationMapper">
    <!-- 结果映射（主表+关联表） -->
    <resultMap id="BaseResultMap" type="com.vpp.dr.domain.VppResponseDeclaration">
        <!-- 主表字段 -->
        <id column="declaration_id" property="declarationId" jdbcType="BIGINT"/>
        <result column="invitation_id" property="invitationId" jdbcType="BIGINT"/>
        <result column="response_capacity" property="responseCapacity" jdbcType="DECIMAL"/>
        <result column="user_total_power" property="userTotalPower" jdbcType="DECIMAL"/>
        <result column="aggregator_bid" property="aggregatorBid" jdbcType="DECIMAL"/>
        <result column="user_avg_bid" property="userAvgBid" jdbcType="DECIMAL"/>
        <result column="aggregator_status" property="aggregatorStatus" jdbcType="VARCHAR"/>
        <result column="event_status" property="eventStatus" jdbcType="VARCHAR"/>
        <result column="invited_user_count" property="invitedUserCount" jdbcType="INTEGER"/>
        <result column="locked_flag" property="lockedFlag" jdbcType="VARCHAR"/>
        <result column="declaration_status" property="declarationStatus" jdbcType="VARCHAR"/>
        <result column="declaration_time" property="declarationTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del_flag" property="delFlag" jdbcType="CHAR"/>

        <!-- 关联邀约计划表字段 -->
        <association property="invitation" javaType="com.vpp.dr.domain.VppExchangeInvitation">
            <id column="invitation_id" property="invitationId" jdbcType="BIGINT"/>
            <result column="invitation_name" property="invitationName" jdbcType="VARCHAR"/>
            <result column="invitation_no" property="invitationNo" jdbcType="VARCHAR"/>
            <result column="source_release" property="sourceRelease" jdbcType="VARCHAR"/>
            <result column="response_date" property="responseDate" jdbcType="VARCHAR"/>
            <result column="demand_time_slots" property="demandTimeSlots" jdbcType="VARCHAR"/>
            <result column="demand_region" property="demandRegion" jdbcType="VARCHAR"/>
            <result column="market_capacity" property="marketCapacity" jdbcType="DECIMAL"/>
            <result column="response_type" property="responseType" jdbcType="VARCHAR"/>
            <result column="load_direction" property="loadDirection" jdbcType="VARCHAR"/>
        </association>
    </resultMap>

    <!-- 关联查询列表 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        d.*,
        i.invitation_name,
        i.invitation_no,
        i.source_release,
        i.response_date,
        i.demand_time_slots,
        i.demand_region,
        i.market_capacity,
        i.response_type,
        i.load_direction
        FROM vpp_response_declaration d
        LEFT JOIN vpp_exchange_invitation i ON d.invitation_id = i.invitation_id
        <where>
            <if test="params.invitationName != null and params.invitationName != ''">
                AND i.invitation_name LIKE CONCAT('%', #{params.invitationName}, '%')
            </if>
            <if test="params.responseDate != null">
                AND i.response_date = #{params.responseDate}
            </if>
            <if test="params.demandArea != null and params.demandArea != ''">
                AND i.demand_region = #{params.demandArea}
            </if>
            <if test="params.eventStatus != null and params.eventStatus != ''">
                AND d.event_status = #{params.eventStatus}
            </if>
            <if test="params.sourceRelease != null and params.sourceRelease != ''">
                AND i.source_release LIKE CONCAT('%', #{params.sourceRelease}, '%')
            </if>
            <if test="params.delFlag == null or params.delFlag == '0'">
                AND d.del_flag = '0'
            </if>
        </where>
        ORDER BY d.declaration_time DESC
    </select>

    <!-- 查询总数 -->
    <select id="selectCount" resultType="int">
        SELECT COUNT(*) FROM vpp_response_declaration d
        LEFT JOIN vpp_exchange_invitation i ON d.invitation_id = i.invitation_id
        <where>
            <if test="params.invitationName != null and params.invitationName != ''">
                AND i.invitation_name LIKE CONCAT('%', #{params.invitationName}, '%')
            </if>
            <if test="params.responseDate != null">
                AND i.response_date = #{params.responseDate}
            </if>
            <if test="params.demandArea != null and params.demandArea != ''">
                AND i.demand_region = #{params.demandArea}
            </if>
            <if test="params.eventStatus != null and params.eventStatus != ''">
                AND d.event_status = #{params.eventStatus}
            </if>
            <if test="params.delFlag == null or params.delFlag == '0'">
                AND d.del_flag = '0'
            </if>
        </where>
    </select>

    <!-- 其他CRUD方法略，按需补充 -->

    <!-- 批量导入 -->
    <insert id="batchImport" parameterType="java.util.List">
        INSERT INTO vpp_response_declaration (
        invitation_id, response_capacity, user_total_power, aggregator_bid, user_avg_bid,
        aggregator_status, event_status, invited_user_count, locked_flag, declaration_status,
        declaration_time, create_by, create_time, update_by, update_time, del_flag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.invitationId}, #{item.responseCapacity}, #{item.userTotalPower},
            #{item.aggregatorBid}, #{item.userAvgBid}, #{item.aggregatorStatus},
            #{item.eventStatus}, #{item.invitedUserCount}, #{item.lockedFlag},
            #{item.declarationStatus}, #{item.declarationTime}, #{item.createBy},
            #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.delFlag}
            )
        </foreach>
    </insert>

    <!-- 根据ID删除响应申报（逻辑删除，标记del_flag=1） -->
    <update id="deleteById" parameterType="Long">
        UPDATE vpp_response_declaration
        SET del_flag = '1' -- 逻辑删除标记
        WHERE declaration_id = #{declarationId}
    </update>

    <!-- 根据ID更新申报状态 -->
    <update id="updateDeclarationStatus" parameterType="com.vpp.dr.domain.VppResponseDeclaration">
        UPDATE vpp_response_declaration
        SET declaration_status = #{declarationStatus}
        WHERE declaration_id = #{declarationId}
    </update>

    <!-- 全量更新响应申报信息（排除创建时间和创建人） -->
    <update id="update" parameterType="com.vpp.dr.domain.VppResponseDeclaration">
        UPDATE vpp_response_declaration
        <set>
            <!-- 动态拼接SET子句，仅更新非空字段 -->
            <if test="invitationId != null">invitation_id = #{invitationId},</if>
            <if test="responseCapacity != null">response_capacity = #{responseCapacity},</if>
            <if test="userTotalPower != null">user_total_power = #{userTotalPower},</if>
            <if test="aggregatorBid != null">aggregator_bid = #{aggregatorBid},</if>
            <if test="userAvgBid != null">user_avg_bid = #{userAvgBid},</if>
            <if test="aggregatorStatus != null">aggregator_status = #{aggregatorStatus},</if>
            <if test="eventStatus != null">event_status = #{eventStatus},</if>
            <if test="invitedUserCount != null">invited_user_count = #{invitedUserCount},</if>
            <if test="lockedFlag != null">locked_flag = #{lockedFlag},</if>
            <if test="declarationStatus != null">declaration_status = #{declarationStatus},</if>
            <!-- 更新操作人信息（假设updateBy和updateTime由前端或服务层传入） -->
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <!-- del_flag通常通过deleteById方法修改，此处可选 -->
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </set>
        WHERE declaration_id = #{declarationId}  <!-- 主键条件 -->
    </update>
</mapper>