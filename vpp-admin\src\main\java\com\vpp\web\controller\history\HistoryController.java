package com.vpp.web.controller.history;

import com.vpp.common.core.domain.AjaxResult;
import com.vpp.history.service.HistoryEventService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/history")
@ApiOperation(value = "历史事件记录，对应调控结果分析",tags = {"历史事件记录"})
public class HistoryController {
    @Autowired
    HistoryEventService service;
    @ApiOperation(value = "历史事件记录",tags = {"历史事件记录"})
    @GetMapping("/event/{deptid}/invitation/{invitation_id}")
    public AjaxResult historyEvent(@PathVariable("deptid") Long deptid,@PathVariable("invitation_id") Long invitation_id){
        return service.HistoryEventService(invitation_id,deptid);
    }
    @ApiOperation(value = "历史事件状态",tags = {"历史事件记录"})
    @GetMapping("/event/{deptid}/status/{invitation_id}")
    public AjaxResult historyEventStatus(@PathVariable("deptid") Long deptid,@PathVariable("invitation_id") Long invitation_id){
        return service.HistoryStatus(invitation_id,deptid);
    }

}
