package com.vpp.web.controller.mgr;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.mgr.domain.VppMgrCnt;
import com.vpp.mgr.domain.VppMgrUag;
import com.vpp.mgr.service.IVppMgrCntService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同(聚合商-聚合用户)Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/mgr/cnt")
@Api(tags = "合同(聚合商-聚合用户)管理")
public class VppMgrCntController extends BaseController {
    @Autowired
    private IVppMgrCntService vppMgrCntService;

    /**
     * 查询合同(聚合商-聚合用户)列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:list')")
    @GetMapping("/list")
    @ApiOperation("查询合同(聚合商-聚合用户)列表")
    public TableDataInfo list(VppMgrCnt vppMgrCnt) {
        startPage();
        List<VppMgrCnt> list = vppMgrCntService.selectVppMgrCntList(vppMgrCnt);
        return getDataTable(list);
    }

    /**
     * 导出合同(聚合商-聚合用户)列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:export')")
    // @Log(title = "合同(聚合商-聚合用户)", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, VppMgrCnt vppMgrCnt) {
    //     List<VppMgrCnt> list = vppMgrCntService.selectVppMgrCntList(vppMgrCnt);
    //     ExcelUtil<VppMgrCnt> util = new ExcelUtil<VppMgrCnt>(VppMgrCnt.class);
    //     util.exportExcel(response, list, "合同(聚合商-聚合用户)数据");
    // }

    /**
     * 获取合同(聚合商-聚合用户)详细信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:query')")
    @GetMapping(value = "/{cntId}")
    @ApiOperation("获取合同(聚合商-聚合用户)详细信息")
    public AjaxResult getInfo(@PathVariable("cntId") Long cntId) {
        return success(vppMgrCntService.selectVppMgrCntByCntId(cntId));
    }

    /**
     * 新增合同(聚合商-聚合用户)
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:add')")
    @Log(title = "合同(聚合商-聚合用户)", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增合同(聚合商-聚合用户)")
    public AjaxResult add(@RequestBody VppMgrCnt vppMgrCnt) {
        return toAjax(vppMgrCntService.insertVppMgrCnt(vppMgrCnt));
    }
    /**
     * 新增合同时,查询所有未签订合同的聚合用户,
     */
    @GetMapping("/nocnt")
    @ApiOperation("查询所有未签订合同的聚合用户")
    public AjaxResult noCntUag(){
        List<VppMgrUag> vppMgrUags = vppMgrCntService.noCntUag();
        AjaxResult result = new AjaxResult();
        result.put("code",200);
        result.put("msg","查询成功");
        result.put("rows",vppMgrUags.size());
        result.put("data",vppMgrUags);
        return result;
    }
    /**
     * 修改合同(聚合商-聚合用户)
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:edit')")
    @Log(title = "合同(聚合商-聚合用户)", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改合同(聚合商-聚合用户)")
    public AjaxResult edit(@RequestBody VppMgrCnt vppMgrCnt) {
        return toAjax(vppMgrCntService.updateVppMgrCnt(vppMgrCnt));
    }

    /**
     * 删除合同(聚合商-聚合用户)
     */
    // @PreAuthorize("@ss.hasPermi('mgr:cnt:remove')")
    @Log(title = "合同(聚合商-聚合用户)", businessType = BusinessType.DELETE)
    @DeleteMapping("/{cntIds}")
    @ApiOperation("删除合同(聚合商-聚合用户)")
    public AjaxResult remove(@PathVariable Long[] cntIds) {
        return toAjax(vppMgrCntService.deleteVppMgrCntByCntIds(cntIds));
    }

    //=========================================================
    // 根据用户id 查询聚合商下的所有用户的合同
    // @GetMapping("/listByUserId")
    // @ApiOperation("查询聚合商合同列表(所有聚合用户)")
    // public TableDataInfo listByUserId(@PathVariable("userId") Long userId) {
    //     startPage();
    //     List<VppMgrCnt> list = vppMgrCntService.listByUserId(userId);
    //     return getDataTable(list);
    // }
    //
    // // 根据聚合商用户机构id 查询聚合商用户的合同
    // @GetMapping("/listByDeptId/{deptId}")
    // @ApiOperation("查询聚合用户合同列表")
    // public TableDataInfo listByDeptId(@PathVariable("deptId") Long deptId) {
    //     startPage();
    //     List<VppMgrCnt> list = vppMgrCntService.listByDeptId(deptId);
    //     return getDataTable(list);
    // }

}