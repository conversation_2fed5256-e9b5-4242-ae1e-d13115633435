<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.td.mapper.VppInvitationWinFeedbackMapper">

    <resultMap id="WinFeedbackMap" type="com.vpp.td.domain.VppInvitationWinFeedback">
        <result column="win_feedback_id" property="winFeedbackId" jdbcType="BIGINT"/>
        <result column="invitation_id" property="invitationId" jdbcType="BIGINT"/>
        <result column="declaration_id" property="declarationId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="total_win_quantity" property="totalWinQuantity" jdbcType="DECIMAL"/>
        <result column="segment_win_quantity" property="segmentWinQuantity" jdbcType="DECIMAL"/>
        <result column="send_status" property="sendStatus" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="upTIMESTAMP" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectList" parameterType="map" resultMap="WinFeedbackMap">
        SELECT * FROM vpp_invitation_win_feedback
        <where>
            <if test="invitationId != null">
                AND invitation_id = #{invitationId}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="sendStatus != null">
                AND send_status = #{sendStatus}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectById" parameterType="Long" resultMap="WinFeedbackMap">
        SELECT * FROM vpp_invitation_win_feedback WHERE win_feedback_id = #{winFeedbackId}
    </select>

    <insert id="insert" parameterType="com.vpp.td.domain.VppInvitationWinFeedback" useGeneratedKeys="true" keyProperty="winFeedbackId">
        INSERT INTO vpp_invitation_win_feedback
        (invitation_id, declaration_id, user_id, total_win_quantity, segment_win_quantity, send_status)
        VALUES
            (#{invitationId}, #{declarationId}, #{userId}, #{totalWinQuantity}, #{segmentWinQuantity}, #{sendStatus})
    </insert>

    <update id="update" parameterType="com.vpp.td.domain.VppInvitationWinFeedback">
        UPDATE vpp_invitation_win_feedback
        SET
            invitation_id = #{invitationId},
            declaration_id = #{declarationId},
            user_id = #{userId},
            total_win_quantity = #{totalWinQuantity},
            segment_win_quantity = #{segmentWinQuantity},
            send_status = #{sendStatus}
        WHERE win_feedback_id = #{winFeedbackId}
    </update>

    <delete id="deleteById" parameterType="Long">
        DELETE FROM vpp_invitation_win_feedback WHERE win_feedback_id = #{winFeedbackId}
    </delete>

</mapper>