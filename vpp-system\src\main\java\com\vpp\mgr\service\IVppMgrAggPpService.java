package com.vpp.mgr.service;

import com.vpp.mgr.domain.VppMgrAggPp;

import java.util.List;

/**
 * 聚合商-电厂信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IVppMgrAggPpService {
    /**
     * 查询聚合商-电厂信息
     *
     * @param userId 聚合商-电厂信息主键
     * @return 聚合商-电厂信息
     */
    public VppMgrAggPp selectVppMgrAggPpByAggPpId(Long userId);

    /**
     * 查询聚合商-电厂信息列表
     *
     * @param vppMgrAggPp 聚合商-电厂信息
     * @return 聚合商-电厂信息集合
     */
    public List<VppMgrAggPp> selectVppMgrAggPpList(VppMgrAggPp vppMgrAggPp);

    /**
     * 新增聚合商-电厂信息
     *
     * @param vppMgrAggPp 聚合商-电厂信息
     * @return 结果
     */
    public int insertVppMgrAggPp(VppMgrAggPp vppMgrAggPp);

    /**
     * 修改聚合商-电厂信息
     *
     * @param vppMgrAggPp 聚合商-电厂信息
     * @return 结果
     */
    public int updateVppMgrAggPp(VppMgrAggPp vppMgrAggPp);

    /**
     * 批量删除聚合商-电厂信息
     *
     * @param aggPpIds 需要删除的聚合商-电厂信息主键集合
     * @return 结果
     */
    public int deleteVppMgrAggPpByAggPpIds(Long[] aggPpIds);

    /**
     * 删除聚合商-电厂信息信息
     *
     * @param aggPpId 聚合商-电厂信息主键
     * @return 结果
     */
    public int deleteVppMgrAggPpByAggPpId(Long aggPpId);
}
