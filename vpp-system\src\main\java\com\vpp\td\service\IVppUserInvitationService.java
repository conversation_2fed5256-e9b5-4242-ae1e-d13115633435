package com.vpp.td.service;

import com.vpp.td.domain.VppUserInvitation;
import java.util.List;

/**
 * 聚合用户邀约业务接口
 */
public interface IVppUserInvitationService {
    List<VppUserInvitation> selectList(VppUserInvitation invitation);
    VppUserInvitation selectById(Long userInvitationId);
    int insert(VppUserInvitation invitation);
    int update(VppUserInvitation invitation);
    int deleteById(Long userInvitationId);
}