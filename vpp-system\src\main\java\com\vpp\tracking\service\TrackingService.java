package com.vpp.tracking.service;

import com.vpp.common.core.domain.AjaxResult;
import com.vpp.tracking.domain.TrackingEvent;
import com.vpp.tracking.mapper.TrackingMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
public class TrackingService {
    @Autowired
    private TrackingMapper mapper;

    public AjaxResult listNeedTrack(){
        List<TrackingEvent> trackingEvents = mapper.listTrackEvent();
        System.out.println(trackingEvents);
        AjaxResult result=new AjaxResult();
        result.put("code",200);
        result.put("data",trackingEvents);
        return result;
    }

    public AjaxResult climb(Long eventId) {
        /**
         * 搜索
         */
        List<TrackingEvent> trackingEvents = mapper.listTrackEvent();

        AjaxResult result=new AjaxResult();
        Float v = mapper.ClimbUphill(eventId);
        result.put("code",200);
        result.put("msg","查询成功");
        result.put("data",v);

        System.out.println(result);
        return result;
    }

    public AjaxResult completionRate(Long eventId) {
        Float v = mapper.completionRate(eventId);
        AjaxResult result=new AjaxResult();
        result.put("code",200);
        result.put("msg","查询成功");
        result.put("data",v);
        return result;
    }
}
