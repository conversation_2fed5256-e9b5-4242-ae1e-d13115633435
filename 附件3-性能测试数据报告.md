# 附件3：性能测试数据报告

## 泛物云虚拟电厂综合系统性能测试数据报告

### 测试概述
- 测试工具：Apache JMeter 5.5
- 测试环境：生产模拟环境
- 测试时间：2025年8月19日-21日
- 测试人员：钱七

## 1. 响应时间测试

### 1.1 关键功能响应时间测试结果

| 功能模块 | 测试场景 | 平均响应时间(ms) | 95%响应时间(ms) | 99%响应时间(ms) | 最大响应时间(ms) | 目标值(ms) | 测试结果 |
|----------|----------|------------------|-----------------|-----------------|------------------|------------|----------|
| 用户登录 | 正常登录 | 823 | 1,245 | 1,567 | 2,134 | ≤2000 | 通过 |
| 用户管理 | 用户列表查询 | 1,156 | 1,890 | 2,345 | 3,012 | ≤3000 | 通过 |
| 聚合商管理 | 聚合商列表查询 | 1,234 | 2,123 | 2,678 | 3,456 | ≤3000 | 通过 |
| 设备管理 | 设备状态查询 | 1,567 | 2,345 | 3,123 | 4,567 | ≤3000 | 通过 |
| 虚拟电厂 | VPP容量计算 | 2,345 | 3,456 | 4,234 | 5,678 | ≤5000 | 通过 |
| 市场交易 | 交易数据查询 | 1,890 | 2,890 | 3,567 | 4,234 | ≤3000 | 通过 |
| 需求响应 | 响应计划执行 | 3,234 | 4,567 | 5,678 | 7,890 | ≤5000 | 通过 |
| 报表生成 | 月度交易报表 | 4,234 | 6,567 | 8,234 | 12,345 | ≤5000 | 不通过 |
| 数据导出 | Excel导出 | 3,123 | 4,789 | 6,234 | 8,567 | ≤5000 | 通过 |
| 数据导入 | 批量用户导入 | 2,567 | 3,890 | 4,567 | 6,234 | ≤5000 | 通过 |

### 1.2 响应时间分布统计

#### 1.2.1 整体响应时间分布
| 响应时间区间 | 请求数量 | 占比 | 累计占比 |
|--------------|----------|------|----------|
| 0-500ms | 8,234 | 34.8% | 34.8% |
| 500ms-1s | 7,186 | 30.4% | 65.2% |
| 1s-2s | 5,680 | 24.0% | 89.2% |
| 2s-3s | 1,890 | 8.0% | 97.2% |
| 3s-5s | 520 | 2.2% | 99.4% |
| >5s | 140 | 0.6% | 100% |

#### 1.2.2 各模块响应时间分布
| 模块 | <1s | 1-2s | 2-3s | 3-5s | >5s |
|------|-----|------|------|------|-----|
| 用户管理 | 78% | 18% | 3% | 1% | 0% |
| 聚合商管理 | 72% | 22% | 5% | 1% | 0% |
| 设备管理 | 65% | 25% | 8% | 2% | 0% |
| 虚拟电厂 | 58% | 28% | 12% | 2% | 0% |
| 市场交易 | 62% | 26% | 10% | 2% | 0% |
| 需求响应 | 45% | 35% | 15% | 5% | 0% |
| 报表生成 | 35% | 30% | 20% | 12% | 3% |

## 2. 并发性能测试

### 2.1 并发用户测试结果

| 并发用户数 | 平均响应时间(ms) | 95%响应时间(ms) | 吞吐量(TPS) | 错误率(%) | CPU使用率(%) | 内存使用率(%) | 测试结果 |
|------------|------------------|-----------------|-------------|-----------|--------------|---------------|----------|
| 10 | 856 | 1,234 | 45.2 | 0 | 25 | 45 | 通过 |
| 20 | 923 | 1,456 | 78.5 | 0 | 32 | 52 | 通过 |
| 50 | 1,234 | 1,890 | 156.8 | 0 | 45 | 60 | 通过 |
| 100 | 2,156 | 3,234 | 234.5 | 0.5 | 65 | 75 | 通过 |
| 150 | 2,890 | 4,567 | 298.7 | 1.2 | 75 | 82 | 通过 |
| 200 | 3,789 | 5,678 | 345.6 | 2.1 | 85 | 88 | 通过 |
| 250 | 4,567 | 7,234 | 378.9 | 3.5 | 90 | 90 | 通过 |
| 300 | 6,234 | 9,567 | 389.2 | 5.3 | 95 | 92 | 不通过 |

### 2.2 负载测试详细数据

#### 2.2.1 50并发用户测试（1小时）
- 总请求数：281,280
- 成功请求数：281,280
- 失败请求数：0
- 平均响应时间：1,234ms
- 最小响应时间：234ms
- 最大响应时间：4,567ms
- 吞吐量：156.8 TPS

#### 2.2.2 100并发用户测试（1小时）
- 总请求数：843,620
- 成功请求数：839,404
- 失败请求数：4,216
- 平均响应时间：2,156ms
- 最小响应时间：345ms
- 最大响应时间：8,234ms
- 吞吐量：234.5 TPS

#### 2.2.3 200并发用户测试（30分钟）
- 总请求数：622,080
- 成功请求数：608,958
- 失败请求数：13,122
- 平均响应时间：3,789ms
- 最小响应时间：456ms
- 最大响应时间：12,345ms
- 吞吐量：345.6 TPS

## 3. 系统资源使用情况

### 3.1 服务器资源监控数据

#### 3.1.1 应用服务器资源使用
| 并发数 | CPU使用率(%) | 内存使用率(%) | 磁盘I/O(MB/s) | 网络I/O(MB/s) | JVM堆内存(MB) | GC频率(次/分钟) |
|--------|--------------|---------------|---------------|---------------|---------------|-----------------|
| 50 | 45 | 60 | 120 | 50 | 1,024 | 2 |
| 100 | 65 | 75 | 180 | 85 | 1,536 | 4 |
| 150 | 75 | 82 | 220 | 120 | 2,048 | 6 |
| 200 | 85 | 88 | 280 | 150 | 2,560 | 8 |
| 250 | 90 | 90 | 320 | 180 | 3,072 | 12 |
| 300 | 95 | 92 | 350 | 200 | 3,584 | 18 |

#### 3.1.2 数据库服务器资源使用
| 并发数 | CPU使用率(%) | 内存使用率(%) | 磁盘I/O(MB/s) | 连接数 | 慢查询数(个/分钟) | 锁等待时间(ms) |
|--------|--------------|---------------|---------------|--------|-------------------|----------------|
| 50 | 35 | 55 | 80 | 25 | 0 | 5 |
| 100 | 50 | 68 | 150 | 45 | 1 | 12 |
| 150 | 60 | 75 | 200 | 68 | 3 | 25 |
| 200 | 70 | 82 | 250 | 89 | 5 | 45 |
| 250 | 80 | 88 | 300 | 112 | 8 | 78 |
| 300 | 85 | 90 | 350 | 135 | 12 | 125 |

### 3.2 缓存系统性能
| 并发数 | Redis CPU(%) | Redis内存(%) | 命中率(%) | 响应时间(ms) | 连接数 |
|--------|--------------|--------------|-----------|--------------|--------|
| 50 | 15 | 25 | 95.2 | 2 | 25 |
| 100 | 25 | 35 | 94.8 | 3 | 45 |
| 150 | 35 | 45 | 94.5 | 4 | 68 |
| 200 | 45 | 55 | 94.1 | 6 | 89 |
| 250 | 55 | 65 | 93.8 | 8 | 112 |
| 300 | 65 | 75 | 93.2 | 12 | 135 |

## 4. 稳定性测试

### 4.1 长时间运行测试（72小时）
- 测试场景：50并发用户持续运行
- 开始时间：2025-08-19 00:00:00
- 结束时间：2025-08-22 00:00:00
- 总运行时间：72小时

#### 4.1.1 系统稳定性指标
| 指标 | 数值 |
|------|------|
| 系统可用率 | 99.8% |
| 平均响应时间 | 2,345ms |
| 最大响应时间 | 8,567ms |
| 总请求数 | 20,275,200 |
| 成功请求数 | 20,234,576 |
| 失败请求数 | 40,624 |
| 错误率 | 0.2% |

#### 4.1.2 资源使用趋势
| 时间段 | CPU平均使用率(%) | 内存平均使用率(%) | 磁盘I/O平均(MB/s) |
|--------|------------------|-------------------|-------------------|
| 0-24小时 | 42 | 58 | 115 |
| 24-48小时 | 45 | 62 | 125 |
| 48-72小时 | 47 | 65 | 135 |

#### 4.1.3 内存泄漏检测
- JVM堆内存使用趋势：稳定，无明显增长
- 老年代GC频率：平均每小时2次
- Full GC次数：72小时内共6次
- 内存泄漏检测结果：无内存泄漏

## 5. 压力测试

### 5.1 极限压力测试
- 测试目标：找到系统性能拐点
- 测试方法：逐步增加并发用户数直到系统崩溃

#### 5.1.1 压力测试结果
| 并发数 | 响应时间(ms) | 错误率(%) | TPS | 系统状态 |
|--------|--------------|-----------|-----|----------|
| 300 | 6,234 | 5.3 | 389.2 | 临界状态 |
| 350 | 8,567 | 12.5 | 356.8 | 性能下降 |
| 400 | 12,345 | 25.8 | 298.5 | 严重下降 |
| 450 | 18,567 | 45.2 | 234.7 | 接近崩溃 |
| 500 | 系统无响应 | 100 | 0 | 系统崩溃 |

#### 5.1.2 系统瓶颈分析
- 性能瓶颈：数据库连接池耗尽
- 崩溃原因：JVM内存溢出
- 建议优化：增加数据库连接池大小，优化JVM参数

## 6. 性能优化建议

### 6.1 数据库优化
1. 增加数据库连接池大小（当前20，建议50）
2. 优化慢查询SQL语句
3. 增加必要的数据库索引
4. 考虑读写分离架构

### 6.2 应用服务器优化
1. 调整JVM堆内存大小（建议4GB）
2. 优化GC参数配置
3. 增加应用服务器实例数量
4. 实施负载均衡

### 6.3 缓存优化
1. 扩大Redis缓存容量
2. 优化缓存策略，提高命中率
3. 考虑分布式缓存架构

### 6.4 代码优化
1. 优化报表生成算法
2. 实施异步处理机制
3. 减少不必要的数据库查询
4. 优化前端资源加载

## 7. 性能测试结论

### 7.1 测试结论
- 系统在200并发用户以下运行稳定
- 大部分功能响应时间满足要求
- 报表生成功能需要性能优化
- 系统具备良好的稳定性

### 7.2 发布建议
- 建议在完成性能优化后发布
- 生产环境建议配置负载均衡
- 需要建立完善的监控体系
- 制定性能调优计划
