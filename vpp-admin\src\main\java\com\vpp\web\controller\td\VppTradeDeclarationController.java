package com.vpp.web.controller.td;

import com.github.pagehelper.PageInfo;
import com.vpp.common.constant.HttpStatus;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.dr.domain.VppExchangeInvitation;
import com.vpp.td.domain.VppInvitationDeclaration;
import com.vpp.td.domain.VppInvitationScheme;
import com.vpp.td.service.ITradeDeclarationService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/trade/declaration")
@Api(tags = "交易申报管理", description = "电力交易申报管理接口")
public class VppTradeDeclarationController {

    @Autowired
    private ITradeDeclarationService declarationService;

    /**
     * 获取邀约计划事件信息（含需求时段、负荷方向等）
     */
    @GetMapping("/event/{invitationId}")
    @ApiOperation("获取邀约计划事件信息")
    public AjaxResult getEventInfo(@PathVariable Long invitationId) {
        VppExchangeInvitation invitation = declarationService.getEventInfo(invitationId);
        return AjaxResult.success("获取成功", invitation);
    }

    /**
     * 获取邀约计划关联的方案信息（含用户类型分布）
     */
    @GetMapping("/scheme/{invitationId}")
    @ApiOperation("获取邀约计划方案信息")
    public AjaxResult getSchemeInfo(@PathVariable Long invitationId) {
        List<VppInvitationScheme> schemes = declarationService.getSchemeList(invitationId);
        return AjaxResult.success("获取成功", schemes);
    }

    /**
     * 查询交易申报列表（带分页和筛选）
     */
    @GetMapping("/list")
    @ApiOperation("查询交易申报列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名称", dataType = "string"),
            @ApiImplicitParam(name = "userAccount", value = "用户户号", dataType = "string"),
            @ApiImplicitParam(name = "userTypeCode", value = "用户类型编码", dataType = "string"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", dataType = "integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", dataType = "integer", defaultValue = "10")
    })
    public TableDataInfo list(
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) String userAccount,
            @RequestParam(required = false) String userTypeCode,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {

        Map<String, Object> params = new HashMap<>();
        params.put("userName", userName);
        params.put("userAccount", userAccount);
        params.put("userTypeCode", userTypeCode);

        // 计算分页偏移量
        int offset = (pageNum - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        List<VppInvitationDeclaration> list = declarationService.getDeclarationList(params);

        return getDataTable(list);
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected TableDataInfo getDataTable(List<?> list)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }
}