package com.vpp.chart.mapper;

import com.vpp.chart.domain.AvailableResource;
import com.vpp.chart.domain.ResourceProvince;
import com.vpp.chart.domain.ResourceStatics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface ChartMapper {
    @Select("SELECT count(1) FROM vpp_mgr_meter where meter_tye='02' and dept_id=#{dept_id};")
    int countChunentDevices(String dept_id);
    @Select("SELECT count(1) FROM vpp_mgr_meter where adjust_type='02' and dept_id=#{dept_id};")
    int countNotControlDevices(String dept_id);
    @Select("SELECT count(1) FROM vpp_mgr_meter where adjust_type='01' and dept_id=#{dept_id};")
    int countControlDevices(String dept_id);
    @Select("SELECT count(1) FROM vpp_mgr_meter where online='01' and dept_id=#{dept_id};")
    int countOnlineDevices(String dept_id);
    @Select("select a.offline/e.total*100 as offline,b.a/e.total*100 as chuneng,c.b/e.total*100 as fadian,d.c/e.total*100 as fuhe,e.total as total from (\n" +
            "select sum(power_rated) as `offline` from vpp_mgr_meter where dept_id=#{dept_id} and `online`='02') a ,\n" +
            "(\n" +
            "select sum(power_rated) as a from vpp_mgr_meter where dept_id=#{dept_id} and meter_tye='02') b, -- 储电类\n" +
            "(\n" +
            "select sum(power_rated) as b from vpp_mgr_meter where dept_id=#{dept_id} and meter_tye='03')c, -- 发电类\n" +
            "(\n" +
            "select sum(power_rated) as c from vpp_mgr_meter where dept_id=#{dept_id} and meter_tye='01')d, -- 负荷类\n" +
            "(\n" +
            "select sum(power_rated) as total FROM vpp_mgr_meter where dept_id=#{dept_id})e;")
    ResourceStatics resourceDistStatistics(String dept_id);
    @Select("select a.total as `online`,b.total as 'offline',a.total/(a.total+b.total)*100 as available,b.total/(a.total+b.total)*100 as `offlineRated` from (\n" +
            "SELECT count(1) total FROM fiotcp_vpp.vpp_mgr_meter where dept_id=#{dept_id} and `online`='01' ) a,\n" +
            "(\n" +
            "SELECT count(1) total FROM fiotcp_vpp.vpp_mgr_meter where dept_id=#{dept_id} and `online`='02')b")
    AvailableResource onlineStatis(String dept_id);
    @Select("select a.total,b.c as num,(select province_name from sys_addr_province where id=b.mount_addr) as province from (\n" +
            "select count(1) as total from fiotcp_vpp.vpp_mgr_meter where dept_id=#{dept_id})a,(\n" +
            "select mount_addr,count(1) as c from fiotcp_vpp.vpp_mgr_meter where dept_id=#{dept_id} group by mount_addr)b;")
    List<ResourceProvince> resourceProvince(String dept_id);
}
