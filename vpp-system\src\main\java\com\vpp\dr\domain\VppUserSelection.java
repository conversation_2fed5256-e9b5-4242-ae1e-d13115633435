package com.vpp.dr.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户选择关联实体（存储发布配置关联的用户类型或具体用户）
 */
@Data
public class VppUserSelection implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 关联发布配置表ID */
    private Long publishId;

    /** 用户类型编码（关联vpp_user_type.type_code，如：PV_USER） */
    private String userTypeCode;

    /** 是否选择“全部聚合用户”（0=否，1=是） */
    private Integer isAll;

    /** 已选用户ID（关联sys_user.user_id，可选） */
    private Long userId;

    /** 用户类型名称（非数据库字段，前端展示用） */
    private String userTypeName;

    /** 记录创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 记录更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}