<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.td.mapper.VppInvitationSchemeMapper">

    <resultMap id="schemeMap" type="com.vpp.td.domain.VppInvitationScheme">
        <id column="scheme_id" property="schemeId"/>          <!-- 主键映射 -->
        <result column="scheme_name" property="schemeName"/>   <!-- 方案名称 -->
        <result column="invitation_id" property="invitationId"/> <!-- 关联邀约ID -->
        <result column="user_type_distribution" property="userTypeDistribution"/> <!-- 用户类型分布 -->
        <result column="total_response_quantity" property="totalResponseQuantity"/> <!-- 总响应量 -->
        <result column="is_optimal" property="isOptimal"/>     <!-- 是否最优 -->
        <result column="create_time" property="createTime"/>   <!-- 创建时间 -->
        <result column="update_time" property="updateTime"/>   <!-- 更新时间 -->
    </resultMap>

    <!-- 新增邀约方案 -->
    <insert id="insertScheme" parameterType="com.vpp.td.domain.VppInvitationScheme">
        INSERT INTO vpp_invitation_scheme (
            scheme_name,
            invitation_id,
            user_type_distribution,
            total_response_quantity,
            is_optimal,
            create_time,
            update_time
        ) VALUES (
                     #{schemeName},
                     #{invitationId},
                     #{userTypeDistribution},
                     #{totalResponseQuantity},
                     #{is_optimal},
                     NOW(),
                     NOW()
                 )
    </insert>

    <!-- 校验方案名称唯一性 -->
    <select id="checkSchemeNameUnique" resultType="int">
        SELECT COUNT(*)
        FROM vpp_invitation_scheme
        WHERE invitation_id = #{invitationId}
          AND scheme_name = #{schemeName}
    </select>

    <!-- 根据邀约ID查询方案列表（按创建时间倒序） -->
    <select id="selectSchemeListByInvitationId" parameterType="long" resultType="com.vpp.td.domain.VppInvitationScheme">
        SELECT
            scheme_id AS schemeId,
            scheme_name AS schemeName,
            invitation_id AS invitationId,
            user_type_distribution AS userTypeDistribution,
            total_response_quantity AS totalResponseQuantity,
            create_time AS createTime,
            update_time AS updateTime
        FROM vpp_invitation_scheme
        WHERE invitation_id = #{invitationId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据方案ID删除方案 -->
    <delete id="deleteSchemeById" parameterType="long">
        DELETE FROM vpp_invitation_scheme
        WHERE scheme_id = #{schemeId}
    </delete>

    <!-- 根据条件查询方案列表（分页/模糊查询） -->
    <select id="selectSchemeList" parameterType="java.util.Map" resultType="com.vpp.td.domain.VppInvitationScheme">
        SELECT
        scheme_id AS schemeId,
        scheme_name AS schemeName,
        invitation_id AS invitationId,
        user_type_distribution AS userTypeDistribution,
        total_response_quantity AS totalResponseQuantity,
        create_time AS createTime,
        update_time AS updateTime
        FROM vpp_invitation_scheme
        <where>
            <!-- 动态条件：方案名称模糊查询 -->
            <if test="schemeName != null and schemeName != ''">
                AND scheme_name LIKE CONCAT('%', #{schemeName}, '%')
            </if>
            <!-- 动态条件：关联邀约ID -->
            <if test="invitationId != null">
                AND invitation_id = #{invitationId}
            </if>
        </where>
        <!-- 分页（可选，需配合 PageHelper 插件） -->
        ORDER BY create_time DESC
    </select>

    <!-- 根据条件统计方案数量 -->
    <select id="selectSchemeCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(*) AS count
        FROM vpp_invitation_scheme
        <where>
            <!-- 动态条件：方案名称模糊查询 -->
            <if test="schemeName != null and schemeName != ''">
                AND scheme_name LIKE CONCAT('%', #{schemeName}, '%')
            </if>
            <!-- 动态条件：关联邀约ID -->
            <if test="invitationId != null">
                AND invitation_id = #{invitationId}
            </if>
        </where>
    </select>

    <!-- 根据ID查询方案 -->
    <select id="selectById" parameterType="Long" resultMap="schemeMap">
        SELECT * FROM vpp_invitation_scheme WHERE scheme_id = #{schemeId}
    </select>

    <!-- 更新最优状态 -->
    <update id="updateIsOptimal" parameterType="com.vpp.td.domain.VppInvitationScheme">
        UPDATE vpp_invitation_scheme
        SET
            is_optimal = #{isOptimal},
            update_time = CURRENT_TIMESTAMP -- 自动更新最后修改时间
        WHERE scheme_id = #{schemeId}
    </update>
</mapper>