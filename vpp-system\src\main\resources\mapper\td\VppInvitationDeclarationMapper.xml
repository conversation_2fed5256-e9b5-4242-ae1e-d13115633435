<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.td.mapper.VppInvitationDeclarationMapper">
    <resultMap id="declarationMap" type="com.vpp.td.domain.VppInvitationDeclaration">
        <id column="declaration_id" property="declarationId"/>
        <result column="invitation_id" property="invitationId"/>
        <result column="user_type_code" property="userTypeCode"/>
        <result column="user_id" property="userId"/>
        <result column="quote_price" property="quotePrice"/>
        <result column="response_quantity" property="responseQuantity"/>
        <result column="max_adjust_power" property="maxAdjustPower"/>
        <result column="adjust_quantity" property="adjustQuantity"/>
        <result column="declaration_time" property="declarationTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectList" parameterType="map" resultMap="declarationMap">
        SELECT
        declaration_id,
        invitation_id,
        user_type_code,
        user_id,
        quote_price,
        response_quantity,
        max_adjust_power,
        adjust_quantity,
        declaration_time,
        create_time,
        update_time
        FROM vpp_invitation_declaration
        <where>
            <if test="userTypeCode != null and userTypeCode != ''">
                AND user_type_code = #{userTypeCode}
            </if>
        </where>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="selectCount" parameterType="map" resultType="int">
        SELECT COUNT(*)
        FROM vpp_invitation_declaration
        <where>
            <if test="userTypeCode != null and userTypeCode != ''">
                AND user_type_code = #{userTypeCode}
            </if>
        </where>
    </select>

    <!-- 根据申报反馈ID查询单条记录（根据id查找） -->
    <select id="selectById" parameterType="java.lang.Long" resultType="com.vpp.td.domain.VppInvitationDeclaration">
        SELECT
            declaration_id AS declarationId,
            invitation_id AS invitationId,
            user_type_code AS userTypeCode,
            user_id AS userId,
            quote_price AS quotePrice,
            response_quantity AS responseQuantity,
            max_adjust_power AS maxAdjustPower,
            adjust_quantity AS adjustQuantity,
            declaration_time AS declarationTime,
            create_time AS createTime,
            update_time AS updateTime
        FROM vpp_invitation_declaration
        WHERE declaration_id = #{declarationId}
    </select>

    <!-- 根据邀约计划ID查询申报反馈列表（可选，用于关联查询） -->
    <select id="selectByInvitationId" parameterType="java.lang.Long" resultType="com.vpp.td.domain.VppInvitationDeclaration">
        SELECT
            declaration_id AS declarationId,
            invitation_id AS invitationId,
            user_type_code AS userTypeCode,
            user_id AS userId,
            quote_price AS quotePrice,
            response_quantity AS responseQuantity,
            max_adjust_power AS maxAdjustPower,
            adjust_quantity AS adjustQuantity,
            declaration_time AS declarationTime,
            create_time AS createTime,
            update_time AS updateTime
        FROM vpp_invitation_declaration
        WHERE invitation_id = #{invitationId}
    </select>

    <!-- 新增申报反馈记录 -->
    <insert id="insert" parameterType="com.vpp.td.domain.VppInvitationDeclaration" useGeneratedKeys="true" keyProperty="declarationId">
        INSERT INTO vpp_invitation_declaration (
            invitation_id,
            user_type_code,
            user_id,
            quote_price,
            response_quantity,
            max_adjust_power,
            adjust_quantity,
            declaration_time,
            create_time,
            update_time
        ) VALUES (
                     #{invitationId},
                     #{userTypeCode},
                     #{userId},
                     #{quotePrice},
                     #{responseQuantity},
                     #{maxAdjustPower},
                     #{adjustQuantity},
                     #{declarationTime},
                     NOW(),
                     NOW()
                 )
    </insert>

    <!-- 根据ID更新申报反馈记录（全量更新） -->
    <update id="update" parameterType="com.vpp.td.domain.VppInvitationDeclaration">
        UPDATE vpp_invitation_declaration
        SET
            invitation_id = #{invitationId},
            user_type_code = #{userTypeCode},
            user_id = #{userId},
            quote_price = #{quotePrice},
            response_quantity = #{responseQuantity},
            max_adjust_power = #{maxAdjustPower},
            adjust_quantity = #{adjustQuantity},
            declaration_time = #{declarationTime},
            update_time = NOW()
        WHERE declaration_id = #{declarationId}
    </update>

    <!-- 根据ID删除申报反馈记录 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM vpp_invitation_declaration
        WHERE declaration_id = #{declarationId}
    </delete>
</mapper>