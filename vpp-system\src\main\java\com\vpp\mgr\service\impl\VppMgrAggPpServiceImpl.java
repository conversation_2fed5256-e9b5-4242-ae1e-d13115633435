package com.vpp.mgr.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.mgr.domain.VppMgrAggPp;
import com.vpp.mgr.mapper.VppMgrAggPpMapper;
import com.vpp.mgr.service.IVppMgrAggPpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 聚合商-电厂信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Service
public class VppMgrAggPpServiceImpl implements IVppMgrAggPpService {
    @Autowired
    private VppMgrAggPpMapper vppMgrAggPpMapper;

    /**
     * 查询聚合商-电厂信息
     *
     * @param userId 聚合商-电厂信息主键
     * @return 聚合商-电厂信息
     */
    @Override
    public VppMgrAggPp selectVppMgrAggPpByAggPpId(Long userId) {
        return vppMgrAggPpMapper.selectVppMgrAggPpByAggPpId(userId);
    }

    /**
     * 查询聚合商-电厂信息列表
     *
     * @param vppMgrAggPp 聚合商-电厂信息
     * @return 聚合商-电厂信息
     */
    @Override
    public List<VppMgrAggPp> selectVppMgrAggPpList(VppMgrAggPp vppMgrAggPp) {
        return vppMgrAggPpMapper.selectVppMgrAggPpList(vppMgrAggPp);
    }

    /**
     * 新增聚合商-电厂信息
     *
     * @param vppMgrAggPp 聚合商-电厂信息
     * @return 结果
     */
    @Override
    public int insertVppMgrAggPp(VppMgrAggPp vppMgrAggPp) {
        vppMgrAggPp.setCreateTime(DateUtils.getNowDate());
        return vppMgrAggPpMapper.insertVppMgrAggPp(vppMgrAggPp);
    }

    /**
     * 修改聚合商-电厂信息
     *
     * @param vppMgrAggPp 聚合商-电厂信息
     * @return 结果
     */
    @Override
    public int updateVppMgrAggPp(VppMgrAggPp vppMgrAggPp) {
        vppMgrAggPp.setUpdateTime(DateUtils.getNowDate());
        return vppMgrAggPpMapper.updateVppMgrAggPp(vppMgrAggPp);
    }

    /**
     * 批量删除聚合商-电厂信息
     *
     * @param aggPpIds 需要删除的聚合商-电厂信息主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrAggPpByAggPpIds(Long[] aggPpIds) {
        return vppMgrAggPpMapper.deleteVppMgrAggPpByAggPpIds(aggPpIds);
    }

    /**
     * 删除聚合商-电厂信息信息
     *
     * @param aggPpId 聚合商-电厂信息主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrAggPpByAggPpId(Long aggPpId) {
        return vppMgrAggPpMapper.deleteVppMgrAggPpByAggPpId(aggPpId);
    }
}