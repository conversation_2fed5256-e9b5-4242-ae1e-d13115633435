<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.plan.mapper.PlanGenDetailsMapper">

    <resultMap type="PlanGenDetails" id="PlanGenDetailsResult">
        <result property="planGenDetailsId"    column="plan_gen_details_id"    />
        <result property="attributeKey"    column="attribute_key"    />
        <result property="attributeValue"    column="attribute_value"    />
        <result property="planGenId"    column="plan_gen_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlanGenDetailsVo">
        select plan_gen_details_id, attribute_key, attribute_value, plan_gen_id, create_by, create_time, update_by, update_time, remark from plan_gen_details
    </sql>

    <select id="selectPlanGenDetailsList" parameterType="PlanGenDetails" resultMap="PlanGenDetailsResult">
        <include refid="selectPlanGenDetailsVo"/>
        <where>
            <if test="attributeKey != null  and attributeKey != ''"> and attribute_key = #{attributeKey}</if>
            <if test="attributeValue != null  and attributeValue != ''"> and attribute_value = #{attributeValue}</if>
            <if test="planGenId != null "> and plan_gen_id = #{planGenId}</if>
        </where>
    </select>

    <select id="selectPlanGenDetailsByPlanGenDetailsId" parameterType="Long" resultMap="PlanGenDetailsResult">
        <include refid="selectPlanGenDetailsVo"/>
        where plan_gen_details_id = #{planGenDetailsId}
    </select>

    <insert id="insertPlanGenDetails" parameterType="PlanGenDetails" useGeneratedKeys="true" keyProperty="planGenDetailsId">
        insert into plan_gen_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="attributeKey != null">attribute_key,</if>
            <if test="attributeValue != null">attribute_value,</if>
            <if test="planGenId != null">plan_gen_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="attributeKey != null">#{attributeKey},</if>
            <if test="attributeValue != null">#{attributeValue},</if>
            <if test="planGenId != null">#{planGenId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePlanGenDetails" parameterType="PlanGenDetails">
        update plan_gen_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="attributeKey != null">attribute_key = #{attributeKey},</if>
            <if test="attributeValue != null">attribute_value = #{attributeValue},</if>
            <if test="planGenId != null">plan_gen_id = #{planGenId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where plan_gen_details_id = #{planGenDetailsId}
    </update>

    <delete id="deletePlanGenDetailsByPlanGenDetailsId" parameterType="Long">
        delete from plan_gen_details where plan_gen_details_id = #{planGenDetailsId}
    </delete>

    <delete id="deletePlanGenDetailsByPlanGenDetailsIds" parameterType="String">
        delete from plan_gen_details where plan_gen_details_id in
        <foreach item="planGenDetailsId" collection="array" open="(" separator="," close=")">
            #{planGenDetailsId}
        </foreach>
    </delete>
</mapper>