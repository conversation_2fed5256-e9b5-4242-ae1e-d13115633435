package com.vpp.sm.domain;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
@Data
public class VppUagSettlement {
    private Long invitationId;
    private String invitationName;
    private String invitationCode;
    private String demandTime;
    private String directive;
    private String respondDate;//响应日，
    private String region;
    private LocalDateTime deadline;
    private Double amount;//结算金额
    private String mode;//模式，分成比例和固定价格
}
