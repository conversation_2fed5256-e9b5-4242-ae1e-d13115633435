package com.vpp.web.controller.mgr;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.enums.BusinessType;
import com.vpp.mgr.domain.VppMgrAggBase;
import com.vpp.mgr.service.IVppMgrAggBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 聚合商-基础信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Api(tags = "聚合商-基础信息")
@RestController
@RequestMapping("/mgr/agg/base")
public class VppMgrAggBaseController extends BaseController {
    @Autowired
    private IVppMgrAggBaseService vppMgrAggBaseService;

    /**
     * 查询聚合商-基础信息列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:base:list')")
    // @GetMapping("/list")
    // // @ApiOperation("查询聚合商-基础信息列表")
    // public TableDataInfo list(VppMgrAggBase vppMgrAggBase) {
    //     startPage();
    //     List<VppMgrAggBase> list = vppMgrAggBaseService.selectVppMgrAggBaseList(vppMgrAggBase);
    //     return getDataTable(list);
    // }

    /**
     * 导出聚合商-基础信息列表
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:base:export')")
    // @Log(title = "聚合商-基础信息", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, VppMgrAggBase vppMgrAggBase) {
    //     List<VppMgrAggBase> list = vppMgrAggBaseService.selectVppMgrAggBaseList(vppMgrAggBase);
    //     ExcelUtil<VppMgrAggBase> util = new ExcelUtil<VppMgrAggBase>(VppMgrAggBase.class);
    //     util.exportExcel(response, list, "聚合商-基础信息数据");
    // }

    /**
     * 获取聚合商-基础信息详细信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:base:query')")
    @GetMapping(value = "/{userId}")
    @ApiOperation("获取聚合商-基础信息详细信息")
    public AjaxResult getInfo(@PathVariable("userId") Long userId) {
        return success(vppMgrAggBaseService.selectVppMgrAggBaseByAggBaseId(userId));
    }
    @GetMapping(value = "/byuag/{uagid}")
    @ApiOperation("根据聚合用户获取聚合商信息,参数为聚合用户id,uagid")
    public AjaxResult getInfoByUagId(@PathVariable("uagid") Long uagid){
        VppMgrAggBase vppMgrAggBase = vppMgrAggBaseService.selectVppMgrAggbaseByUagid(uagid);
        if(vppMgrAggBase!=null)
        {
            return success(vppMgrAggBase);
        }
        return success("未与聚合商建立合约关系");
    }
    /**
     * 新增聚合商-基础信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:base:add')")
    @Log(title = "聚合商-基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增聚合商-基础信息")
    public AjaxResult add(@RequestBody VppMgrAggBase vppMgrAggBase) {
        return toAjax(vppMgrAggBaseService.insertVppMgrAggBase(vppMgrAggBase));
    }

    /**
     * 修改聚合商-基础信息
     */
    // @PreAuthorize("@ss.hasPermi('mgr:agg:base:edit')")
    @Log(title = "聚合商-基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改聚合商-基础信息")
    public AjaxResult edit(@RequestBody VppMgrAggBase vppMgrAggBase) {
        return toAjax(vppMgrAggBaseService.updateVppMgrAggBase(vppMgrAggBase));
    }

    /**
     * 删除聚合商-基础信息
     */
    // // @PreAuthorize("@ss.hasPermi('mgr:agg:base:remove')")
    // @Log(title = "聚合商-基础信息", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{aggBaseIds}")
    // // @ApiOperation("删除聚合商-基础信息")
    // public AjaxResult remove(@PathVariable Long[] aggBaseIds) {
    //     return toAjax(vppMgrAggBaseService.deleteVppMgrAggBaseByAggBaseIds(aggBaseIds));
    // }
}