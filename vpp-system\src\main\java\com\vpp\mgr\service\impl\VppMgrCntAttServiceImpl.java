package com.vpp.mgr.service.impl;

import com.vpp.mgr.domain.VppMgrCntAtt;
import com.vpp.mgr.mapper.VppMgrCntAttMapper;
import com.vpp.mgr.service.IVppMgrCntAttService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合同附件(聚合商-聚合用户)Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class VppMgrCntAttServiceImpl implements IVppMgrCntAttService {
    @Autowired
    private VppMgrCntAttMapper vppMgrCntAttMapper;

    /**
     * 查询合同附件(聚合商-聚合用户)
     *
     * @param uagCntId 合同附件(聚合商-聚合用户)主键
     * @return 合同附件(聚合商 - 聚合用户)
     */
    @Override
    public VppMgrCntAtt selectVppMgrCntAttByUagCntId(Long uagCntId) {
        return vppMgrCntAttMapper.selectVppMgrCntAttByUagCntId(uagCntId);
    }

    /**
     * 查询合同附件(聚合商-聚合用户)列表
     *
     * @param vppMgrCntAtt 合同附件(聚合商-聚合用户)
     * @return 合同附件(聚合商 - 聚合用户)
     */
    @Override
    public List<VppMgrCntAtt> selectVppMgrCntAttList(VppMgrCntAtt vppMgrCntAtt) {
        return vppMgrCntAttMapper.selectVppMgrCntAttList(vppMgrCntAtt);
    }

    /**
     * 新增合同附件(聚合商-聚合用户)
     *
     * @param vppMgrCntAtt 合同附件(聚合商-聚合用户)
     * @return 结果
     */
    @Override
    public int insertVppMgrCntAtt(VppMgrCntAtt vppMgrCntAtt) {
        return vppMgrCntAttMapper.insertVppMgrCntAtt(vppMgrCntAtt);
    }

    /**
     * 修改合同附件(聚合商-聚合用户)
     *
     * @param vppMgrCntAtt 合同附件(聚合商-聚合用户)
     * @return 结果
     */
    @Override
    public int updateVppMgrCntAtt(VppMgrCntAtt vppMgrCntAtt) {
        return vppMgrCntAttMapper.updateVppMgrCntAtt(vppMgrCntAtt);
    }

    /**
     * 批量删除合同附件(聚合商-聚合用户)
     *
     * @param uagCntIds 需要删除的合同附件(聚合商-聚合用户)主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrCntAttByUagCntIds(Long[] uagCntIds) {
        return vppMgrCntAttMapper.deleteVppMgrCntAttByUagCntIds(uagCntIds);
    }

    /**
     * 删除合同附件(聚合商-聚合用户)信息
     *
     * @param uagCntId 合同附件(聚合商-聚合用户)主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrCntAttByUagCntId(Long uagCntId) {
        return vppMgrCntAttMapper.deleteVppMgrCntAttByUagCntId(uagCntId);
    }
}