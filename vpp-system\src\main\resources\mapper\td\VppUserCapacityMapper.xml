<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.td.mapper.VppUserCapacityMapper">
    <resultMap id="vppUserCapacityMap" type="com.vpp.td.domain.VppUserCapacity">
        <result column="capacity_id" property="capacityId"/>
        <result column="invitation_id" property="invitationId"/>
        <result column="user_code" property="userCode"/>
        <result column="date" property="date"/>
        <result column="baseline" property="baseline"/>
        <result column="up_regulate_power" property="upRegulatePower"/>
        <result column="down_regulate_power" property="downRegulatePower"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <select id="selectList" parameterType="VppUserCapacity" resultMap="vppUserCapacityMap">
        SELECT * FROM vpp_user_capacity
        <where>
            <if test="invitationId != null">AND invitation_id = #{invitationId}</if>
            <if test="userCode != null and userCode != ''">AND user_code = #{userCode}</if>
            <if test="date != null">AND date = #{date}</if>
            <if test="delFlag == null or delFlag == ''">AND del_flag = '0'</if>
        </where>
        ORDER BY date ASC
    </select>

    <select id="selectById" parameterType="Long" resultMap="vppUserCapacityMap">
        SELECT * FROM vpp_user_capacity WHERE capacity_id = #{capacityId} AND del_flag = '0'
    </select>

    <insert id="insert" parameterType="VppUserCapacity" useGeneratedKeys="true" keyProperty="capacityId">
        INSERT INTO vpp_user_capacity (
            invitation_id, user_code, date, baseline, up_regulate_power, down_regulate_power,
            create_by, create_time, update_by, update_time, del_flag
        ) VALUES (
                     #{invitationId}, #{userCode}, #{date}, #{baseline}, #{upRegulatePower}, #{downRegulatePower},
                     #{createBy}, NOW(), #{updateBy}, NOW(), '0'
                 )
    </insert>

    <update id="update" parameterType="VppUserCapacity">
        UPDATE vpp_user_capacity
        <set>
            <if test="baseline != null">baseline = #{baseline},</if>
            <if test="upRegulatePower != null">up_regulate_power = #{upRegulatePower},</if>
            <if test="downRegulatePower != null">down_regulate_power = #{downRegulatePower},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE capacity_id = #{capacityId} AND del_flag = '0'
    </update>

    <update id="deleteById" parameterType="Long">
        UPDATE vpp_user_capacity SET del_flag = '2' WHERE capacity_id = #{capacityId} AND del_flag = '0'
    </update>
</mapper>