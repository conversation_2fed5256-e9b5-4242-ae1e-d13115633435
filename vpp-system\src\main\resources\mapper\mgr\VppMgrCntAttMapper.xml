<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.mgr.mapper.VppMgrCntAttMapper">

    <resultMap type="VppMgrCntAtt" id="VppMgrCntAttResult">
        <result property="uagCntId"    column="uag_cnt_id"    />
        <result property="attrUrl"    column="attr_url"    />
        <result property="attrName"    column="attr_name"    />
        <result property="uploadDate"    column="upload_date"    />
    </resultMap>

    <sql id="selectVppMgrCntAttVo">
        select uag_cnt_id, attr_url, attr_name, upload_date from vpp_mgr_cnt_att
    </sql>

    <select id="selectVppMgrCntAttList" parameterType="VppMgrCntAtt" resultMap="VppMgrCntAttResult">
        <include refid="selectVppMgrCntAttVo"/>
        <where>
            <if test="uagCntId != null  and uagCntId != ''"> and uag_cnt_id = #{uagCntId}</if>
            <if test="attrUrl != null  and attrUrl != ''"> and attr_url = #{attrUrl}</if>
            <if test="attrName != null  and attrName != ''"> and attr_name like concat('%', #{attrName}, '%')</if>
            <if test="uploadDate != null "> and upload_date = #{uploadDate}</if>
        </where>
    </select>

    <select id="selectVppMgrCntAttByUagCntId" parameterType="Long" resultMap="VppMgrCntAttResult">
        <include refid="selectVppMgrCntAttVo"/>
        where uag_cnt_id = #{uagCntId}
    </select>


    <insert id="insertVppMgrCntAtt" parameterType="VppMgrCntAtt">
        insert into vpp_mgr_cnt_att
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uagCntId != null">uag_cnt_id,</if>
            <if test="attrUrl != null and attrUrl != ''">attr_url,</if>
            <if test="attrName != null and attrName != ''">attr_name,</if>
            <if test="uploadDate != null">upload_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uagCntId != null">#{uagCntId},</if>
            <if test="attrUrl != null and attrUrl != ''">#{attrUrl},</if>
            <if test="attrName != null and attrName != ''">#{attrName},</if>
            <if test="uploadDate != null">#{uploadDate},</if>
        </trim>
    </insert>

    <update id="updateVppMgrCntAtt" parameterType="VppMgrCntAtt">
        update vpp_mgr_cnt_att
        <trim prefix="SET" suffixOverrides=",">
            <if test="attrUrl != null and attrUrl != ''">attr_url = #{attrUrl},</if>
            <if test="attrName != null and attrName != ''">attr_name = #{attrName},</if>
            <if test="uploadDate != null">upload_date = #{uploadDate},</if>
        </trim>
        where uag_cnt_id = #{uagCntId}
    </update>

    <delete id="deleteVppMgrCntAttByUagCntId" parameterType="Long">
        delete from vpp_mgr_cnt_att where uag_cnt_id = #{uagCntId}
    </delete>

    <delete id="deleteVppMgrCntAttByUagCntIds" parameterType="String">
        delete from vpp_mgr_cnt_att where uag_cnt_id in
        <foreach item="uagCntId" collection="array" open="(" separator="," close=")">
            #{uagCntId}
        </foreach>
    </delete>

<!--   ============================================================== -->
    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        insert into vpp_mgr_cnt_att(uag_cnt_id, attr_url, attr_name, upload_date)
        values
        <foreach collection="list" item="vppMgrCntAtt" separator=",">
            (#{vppMgrCntAtt.uagCntId}, #{vppMgrCntAtt.attrUrl},#{vppMgrCntAtt.attrName},#{vppMgrCntAtt.uploadDate})
        </foreach>
        on duplicate key
        update uag_cnt_id=values(uag_cnt_id), attr_url=values(attr_url), attr_name=values(attr_name), upload_date=values(upload_date);
    </insert>

    <select id="selectByCntId" parameterType="Long" resultMap="VppMgrCntAttResult">
        select uag_cnt_id, attr_url,attr_name, upload_date from vpp_mgr_cnt_att where uag_cnt_id = #{uagCntId}
    </select>
</mapper>