package com.vpp.plan.service.impl;


import com.vpp.common.utils.DateUtils;
import com.vpp.plan.domain.PlanGenDevices;
import com.vpp.plan.mapper.PlanGenDevicesMapper;
import com.vpp.plan.service.IPlanGenDevicesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 自动生成计划设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class PlanGenDevicesServiceImpl implements IPlanGenDevicesService {
    @Autowired
    private PlanGenDevicesMapper planGenDevicesMapper;

    /**
     * 查询自动生成计划设备
     *
     * @param planGenDevicesId 自动生成计划设备主键
     * @return 自动生成计划设备
     */
    @Override
    public PlanGenDevices selectPlanGenDevicesByPlanGenDevicesId(Long planGenDevicesId) {
        return planGenDevicesMapper.selectPlanGenDevicesByPlanGenDevicesId(planGenDevicesId);
    }

    /**
     * 查询自动生成计划设备列表
     *
     * @param planGenDevices 自动生成计划设备
     * @return 自动生成计划设备
     */
    @Override
    public List<PlanGenDevices> selectPlanGenDevicesList(PlanGenDevices planGenDevices) {
        return planGenDevicesMapper.selectPlanGenDevicesList(planGenDevices);
    }

    /**
     * 新增自动生成计划设备
     *
     * @param planGenDevices 自动生成计划设备
     * @return 结果
     */
    @Override
    public int insertPlanGenDevices(PlanGenDevices planGenDevices) {
        planGenDevices.setCreateTime(DateUtils.getNowDate());
        return planGenDevicesMapper.insertPlanGenDevices(planGenDevices);
    }

    /**
     * 修改自动生成计划设备
     *
     * @param planGenDevices 自动生成计划设备
     * @return 结果
     */
    @Override
    public int updatePlanGenDevices(PlanGenDevices planGenDevices) {
        planGenDevices.setUpdateTime(DateUtils.getNowDate());
        return planGenDevicesMapper.updatePlanGenDevices(planGenDevices);
    }

    /**
     * 批量删除自动生成计划设备
     *
     * @param planGenDevicesIds 需要删除的自动生成计划设备主键
     * @return 结果
     */
    @Override
    public int deletePlanGenDevicesByPlanGenDevicesIds(Long[] planGenDevicesIds) {
        return planGenDevicesMapper.deletePlanGenDevicesByPlanGenDevicesIds(planGenDevicesIds);
    }

    /**
     * 删除自动生成计划设备信息
     *
     * @param planGenDevicesId 自动生成计划设备主键
     * @return 结果
     */
    @Override
    public int deletePlanGenDevicesByPlanGenDevicesId(Long planGenDevicesId) {
        return planGenDevicesMapper.deletePlanGenDevicesByPlanGenDevicesId(planGenDevicesId);
    }
}