package com.vpp.web.controller.re;

import com.vpp.common.core.domain.AjaxResult;
import com.vpp.re.domain.VppEventScore;
import com.vpp.re.service.VppEventScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评分综合与明细表 Controller
 */
@RestController
@RequestMapping("/vpp/eventScore")
@Api(tags = "响应评价接口", description = "提供评分记录的增删改查服务")
public class VppEventScoreController {

    @Resource
    private VppEventScoreService scoreService;

    /**
     * 插入评分记录
     * @param score 评分实体（需包含所有必填字段）
     * @return 成功/失败（1-成功，0-失败）
     */
    @PostMapping("/insert")
    @ApiOperation(value = "插入评分记录", notes = "插入一条新的评分记录")
    public AjaxResult insertScore(@RequestBody VppEventScore score) {
        int result = scoreService.insertScore(score);
        return result > 0 ? AjaxResult.success(result) : AjaxResult.error("插入失败，请检查参数");
    }

    /**
     * 更新评分记录
     * @param score 评分实体（需包含itemId和其他更新字段）
     * @return 成功/失败（1-成功，0-失败）
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新评分记录", notes = "更新一条已存在的评分记录")
    public AjaxResult updateScore(@RequestBody VppEventScore score) {
        int result = scoreService.updateScore(score);
        return result > 0 ? AjaxResult.success(result) : AjaxResult.error("更新失败，请检查参数或记录是否存在");
    }

    /**
     * 逻辑删除评分记录
     * @param itemId 评分项目ID
     * @return 成功/失败（1-成功，0-失败）
     */
    @DeleteMapping("/delete/{itemId}")
    @ApiOperation(value = "逻辑删除评分记录", notes = "逻辑删除指定ID的评分记录（非物理删除）")
    public AjaxResult deleteScore(
            @ApiParam(value = "评分项目ID", required = true, example = "1") @PathVariable Long itemId
    ) {
        int result = scoreService.deleteScore(itemId);
        return result > 0 ? AjaxResult.success(result) : AjaxResult.error("删除失败，记录不存在或已删除");
    }

    /**
     * 根据ID查询评分记录
     * @param itemId 评分项目ID
     * @return 评分记录详情
     */
    @GetMapping("/get/{itemId}")
    @ApiOperation(value = "根据ID查询评分记录", notes = "返回指定ID的评分记录详情")
    public AjaxResult getScoreById(
            @ApiParam(value = "评分项目ID", required = true, example = "1") @PathVariable Long itemId
    ) {
        VppEventScore score = scoreService.getScoreById(itemId);
        return score != null ? AjaxResult.success(score) : AjaxResult.error("记录不存在或已删除");
    }

    /**
     * 根据邀约计划ID分页查询评分记录
     * @param invitationId 邀约计划ID
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页数量
     * @return 评分记录列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "根据邀约计划ID分页查询评分记录", notes = "返回评分记录的分页数据")
    public AjaxResult getScoreList(
            @ApiParam(value = "邀约计划ID", required = true, example = "44") @RequestParam Long invitationId,
            @ApiParam(value = "页码（从1开始）", defaultValue = "1", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页数量", defaultValue = "10", example = "10") @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        List<VppEventScore> scoreList = scoreService.getScoreListByInvitationId(invitationId, pageNum, pageSize);
        return AjaxResult.success(scoreList);
    }
}