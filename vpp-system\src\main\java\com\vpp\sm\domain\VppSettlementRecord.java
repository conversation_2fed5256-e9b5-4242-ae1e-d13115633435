package com.vpp.sm.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 虚拟电厂结算记录实体类
 * 实现Serializable接口用于序列化，支持分布式传输
 */
@Data
@ToString(exclude = {"createBy", "updateBy"}) // 排除创建/更新者字段（非必要展示）
public class VppSettlementRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 结算记录主键ID
     */
    private Long settlementId;

    /**
     * 关联邀约计划ID（外键）
     */
    private Long invitationId;

    /**
     * 结算时间（精确到分钟）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime settlementTime;

    /**
     * 实际电量（MW）
     */
    private BigDecimal actualElectricity;

    /**
     * 补贴总价（元）
     */
    private BigDecimal subsidyTotal;

    /**
     * 结算总价（元）
     */
    private BigDecimal settlementTotal;

    /**
     * 偏差考核总价（元，默认0）
     */
    private BigDecimal deviationPenalty = BigDecimal.ZERO;

    /**
     * 结算点单价（元/WMh）
     */
    private BigDecimal settlementUnitPrice;

    /**
     * 创建者（系统用户账号）
     */
    private String createBy;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者（系统用户账号）
     */
    private String updateBy;

    /**
     * 记录最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}