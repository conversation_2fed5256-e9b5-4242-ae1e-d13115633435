package com.vpp.web.controller.dr;

import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.dr.domain.VppMarketClearanceRecord;
import com.vpp.dr.service.IVppMarketClearanceRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dr/market/clearance/record")
@Api(tags = "市场出清记录管理", description = "虚拟电厂系统平台-市场出清记录管理接口")
public class VppMarketClearanceRecordController extends BaseController {

    @Autowired
    private IVppMarketClearanceRecordService recordService;

    /**
     * 查询清算记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询清算记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "transactionId", value = "清算交易ID", dataType = "string"),
            @ApiImplicitParam(name = "publishId", value = "发布配置ID", dataType = "long"),
            @ApiImplicitParam(name = "status", value = "清算状态（0=待清算，1=已清算，2=失败）", dataType = "integer"),
            // 新增时间区间查询参数
            @ApiImplicitParam(name = "startTime", value = "开始时间（格式：yyyy-MM-dd HH:mm:ss）", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间（格式：yyyy-MM-dd HH:mm:ss）", dataType = "string"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", dataType = "integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", dataType = "integer", defaultValue = "10")
    })
    public TableDataInfo list(
            @RequestParam(required = false) String transactionId,
            @RequestParam(required = false) Long publishId,
            @RequestParam(required = false) Integer status,
            // 新增时间参数
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        Map<String, Object> params = new HashMap<>();
        params.put("transactionId", transactionId);
        params.put("publishId", publishId);
        params.put("status", status);

        // 添加时间区间参数
        if (startTime != null) params.put("startTime", startTime);
        if (endTime != null) params.put("endTime", endTime);

        List<VppMarketClearanceRecord> list = recordService.getRecordList(params);
        return getDataTable(list);
    }
}