# SM4加密故障排除指南

## 问题：登录API报错 "pad block corrupted"

### 可能原因及解决方案

#### 1. 前后端密钥不一致
**症状：** 前端加密成功，但后端解密失败
**解决方案：**
- 检查前端配置文件 `.env.development` 或 `.env.production` 中的 `VUE_APP_SM4_KEY`
- 确保后端使用相同的32位十六进制密钥
- 密钥格式：`0123456789abcdeffedcba9876543210`（示例）

#### 2. 加密模式不匹配
**症状：** 加密解密都成功，但结果不正确
**解决方案：**
- 前端现在使用CBC模式，与后端保持一致
- 确保前后端都使用CBC模式
- CBC模式需要初始化向量(IV)，确保前后端IV一致

#### 3. 填充方式不匹配
**症状：** 解密时出现填充错误
**解决方案：**
- 前端使用PKCS#7填充
- 确保后端也使用PKCS#7填充
- 避免使用PKCS#5或其他填充方式

#### 4. 编码格式问题
**症状：** 数据传输过程中出现乱码
**解决方案：**
- 前端输出十六进制字符串
- 确保后端接收十六进制格式
- 检查HTTP请求头的Content-Type

### 调试步骤

#### 步骤1：验证前端加密
1. 打开浏览器开发者工具
2. 在控制台运行：`testSM4()`
3. 检查加密解密是否正常

#### 步骤2：检查网络请求
1. 在Network标签页查看登录请求
2. 检查请求体中的password字段是否为十六进制字符串
3. 确认密码长度合理（通常32-64位十六进制字符）

#### 步骤3：验证后端配置
1. 检查后端SM4密钥配置
2. 确认后端使用的SM4库和参数
3. 测试后端独立的SM4加密解密功能

#### 步骤4：对比加密结果
1. 使用相同的明文和密钥
2. 分别在前端和后端进行加密
3. 对比加密结果是否一致

### 常用测试命令

```javascript
// 在浏览器控制台运行
// 测试SM4加密解密
testSM4()

// 手动测试特定密码
import { encrypt, decrypt } from '@/utils/jsencrypt'
const password = 'admin123'
const encrypted = encrypt(password)
console.log('加密结果:', encrypted)
const decrypted = decrypt(encrypted)
console.log('解密结果:', decrypted)
console.log('是否一致:', password === decrypted)
```

### 配置检查清单

- [ ] 前端SM4密钥配置正确（32位十六进制）
- [ ] 后端SM4密钥与前端一致
- [ ] 前后端都使用ECB模式
- [ ] 前后端都使用PKCS#7填充
- [ ] 前端输出十六进制格式
- [ ] 后端接收十六进制格式
- [ ] 网络传输过程中数据未被修改
- [ ] 前端加密测试通过

### 联系支持

如果以上步骤都无法解决问题，请提供以下信息：
1. 前端加密测试结果
2. 网络请求详情（去除敏感信息）
3. 后端错误日志
4. 前后端SM4配置信息
