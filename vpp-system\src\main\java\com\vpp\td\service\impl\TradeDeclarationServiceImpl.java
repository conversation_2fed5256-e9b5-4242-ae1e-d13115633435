package com.vpp.td.service.impl;

import com.vpp.dr.domain.VppExchangeInvitation;
import com.vpp.td.domain.VppInvitationDeclaration;
import com.vpp.td.domain.VppInvitationScheme;
import com.vpp.dr.mapper.VppExchangeInvitationMapper;
import com.vpp.td.mapper.VppInvitationDeclarationMapper;
import com.vpp.td.mapper.VppInvitationSchemeMapper;
import com.vpp.td.service.ITradeDeclarationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class TradeDeclarationServiceImpl implements ITradeDeclarationService {

    @Autowired
    private VppExchangeInvitationMapper exchangeInvitationMapper;

    @Autowired
    private VppInvitationSchemeMapper schemeMapper;

    @Autowired
    private VppInvitationDeclarationMapper declarationMapper;

    @Override
    public VppExchangeInvitation getEventInfo(Long invitationId) {
        return exchangeInvitationMapper.selectInvitationById(invitationId);
    }

    @Override
    public List<VppInvitationScheme> getSchemeList(Long invitationId) {
        return schemeMapper.selectSchemeListByInvitationId(invitationId);
    }

    @Override
    public List<VppInvitationDeclaration> getDeclarationList(Map<String, Object> params) {
        return declarationMapper.selectList(params);
    }

    @Override
    public int getDeclarationCount(Map<String, Object> params) {
        return declarationMapper.selectCount(params);
    }
}