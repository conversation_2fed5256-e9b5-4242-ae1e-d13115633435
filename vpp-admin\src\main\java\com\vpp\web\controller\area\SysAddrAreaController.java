package com.vpp.web.controller.area;

import com.vpp.area.domain.SysAddrArea;
import com.vpp.area.service.ISysAddrAreaService;
import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 地区设置Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/system/area")
public class SysAddrAreaController extends BaseController {
    @Autowired
    private ISysAddrAreaService sysAddrAreaService;

    /**
     * 查询地区设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAddrArea sysAddrArea) {
        startPage();
        List<SysAddrArea> list = sysAddrAreaService.selectSysAddrAreaList(sysAddrArea);
        return getDataTable(list);
    }

    /**
     * 导出地区设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:export')")
    @Log(title = "地区设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAddrArea sysAddrArea) {
        List<SysAddrArea> list = sysAddrAreaService.selectSysAddrAreaList(sysAddrArea);
        ExcelUtil<SysAddrArea> util = new ExcelUtil<SysAddrArea>(SysAddrArea.class);
        util.exportExcel(response, list, "地区设置数据");
    }

    /**
     * 获取地区设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:area:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(sysAddrAreaService.selectSysAddrAreaById(id));
    }

    /**
     * 新增地区设置
     */
    @PreAuthorize("@ss.hasPermi('system:area:add')")
    @Log(title = "地区设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAddrArea sysAddrArea) {
        return toAjax(sysAddrAreaService.insertSysAddrArea(sysAddrArea));
    }

    /**
     * 修改地区设置
     */
    @PreAuthorize("@ss.hasPermi('system:area:edit')")
    @Log(title = "地区设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAddrArea sysAddrArea) {
        return toAjax(sysAddrAreaService.updateSysAddrArea(sysAddrArea));
    }

    /**
     * 删除地区设置
     */
    @PreAuthorize("@ss.hasPermi('system:area:remove')")
    @Log(title = "地区设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(sysAddrAreaService.deleteSysAddrAreaByIds(ids));
    }
}