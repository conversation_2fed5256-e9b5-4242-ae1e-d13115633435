package com.vpp.web.controller.sm;

import com.vpp.common.core.domain.AjaxResult;
import com.vpp.sm.service.VppSettlementUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/settlement/user")
@Api(tags = "结算管理-用户结算接口", description = "用户结算接口")
public class VppSettlementUserControlelr {
    @Autowired
    VppSettlementUserService service;
    /**
     * 用户结算
     *
     */
    @GetMapping("/all/{uagid}")
    @ApiOperation(value = "获取用户下的所有结算记录",tags = {"结算管理-用户结算接口"})
    public AjaxResult getAllSettlementUserRecords(@PathVariable("uagid") Long uagid){

        return service.allSettlementUserRecords(uagid);
    }
    @ApiOperation(value = "结算用户列表",tags = {"结算管理-用户结算接口"})
    @GetMapping("/list/{dept}")
    public AjaxResult getUserBaseSettlement(@PathVariable("dept") Long dept){
        return service.listVppUag(dept);
    }

}
