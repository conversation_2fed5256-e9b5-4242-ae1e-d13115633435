package com.vpp.web.controller.re;

import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.re.service.IVppAggregateResponseExecutionTrackingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 聚合响应执行追踪
 */
@RestController
@RequestMapping("/re/ResponseTrack")
@Api(tags = "聚合响应执行追踪", description = "聚合响应执行追踪-事件追踪")
public class VppAggregateResponseExecutionTrackingCintroller extends BaseController {

    @Autowired
    private IVppAggregateResponseExecutionTrackingService service;

    /**
     * 根据邀约计划ID查询运行概览列表
     */
    @GetMapping("/byInvitationId")
    @ApiOperation("根据邀约计划ID查询运行概览列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invitationId", value = "关联的邀约计划ID（必填）", required = true, dataType = "long"),
    })
    public AjaxResult getByInvitationId(
            @RequestParam Long invitationId
    ) {
        // 调用 Service 分页查询
        return AjaxResult.success(service.getByInvitationId(invitationId));
    }

    /**
     * 根据邀约计划ID查询基本信息表
     */
    @GetMapping("/selectBasicInfoByInvitationId")
    @ApiOperation("根据邀约计划ID查询基本信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invitationId", value = "关联的邀约计划ID（必填）", required = true, dataType = "long"),
    })
    public AjaxResult selectBasicInfoByInvitationId(
            @RequestParam Long invitationId
    ) {
        // 调用 Service 分页查询
        return AjaxResult.success(service.selectBasicInfoByInvitationId(invitationId));
    }

    /**
     * 根据邀约计划ID查询运行概览列表
     */
    @GetMapping("/selectOperationByInvitationId")
    @ApiOperation("根据邀约计划ID查询运行概览列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invitationId", value = "关联的邀约计划ID（必填）", required = true, dataType = "long"),
    })
    public AjaxResult selectOperationByInvitationId(
            @RequestParam Long invitationId
    ) {
        // 调用 Service 分页查询
        return AjaxResult.success(service.selectOperationByInvitationId(invitationId));
    }
}
