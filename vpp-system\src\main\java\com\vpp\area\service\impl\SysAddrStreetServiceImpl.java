package com.vpp.area.service.impl;

import com.vpp.area.domain.SysAddrStreet;
import com.vpp.area.mapper.SysAddrStreetMapper;
import com.vpp.area.service.ISysAddrStreetService;
import com.vpp.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 街道设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class SysAddrStreetServiceImpl implements ISysAddrStreetService {
    @Autowired
    private SysAddrStreetMapper sysAddrStreetMapper;

    /**
     * 查询街道设置
     *
     * @param id 街道设置主键
     * @return 街道设置
     */
    @Override
    public SysAddrStreet selectSysAddrStreetById(Long id) {
        return sysAddrStreetMapper.selectSysAddrStreetById(id);
    }

    /**
     * 查询街道设置列表
     *
     * @param sysAddrStreet 街道设置
     * @return 街道设置
     */
    @Override
    public List<SysAddrStreet> selectSysAddrStreetList(SysAddrStreet sysAddrStreet) {
        return sysAddrStreetMapper.selectSysAddrStreetList(sysAddrStreet);
    }

    /**
     * 新增街道设置
     *
     * @param sysAddrStreet 街道设置
     * @return 结果
     */
    @Override
    public int insertSysAddrStreet(SysAddrStreet sysAddrStreet) {
        sysAddrStreet.setCreateTime(DateUtils.getNowDate());
        return sysAddrStreetMapper.insertSysAddrStreet(sysAddrStreet);
    }

    /**
     * 修改街道设置
     *
     * @param sysAddrStreet 街道设置
     * @return 结果
     */
    @Override
    public int updateSysAddrStreet(SysAddrStreet sysAddrStreet) {
        sysAddrStreet.setUpdateTime(DateUtils.getNowDate());
        return sysAddrStreetMapper.updateSysAddrStreet(sysAddrStreet);
    }

    /**
     * 批量删除街道设置
     *
     * @param ids 需要删除的街道设置主键
     * @return 结果
     */
    @Override
    public int deleteSysAddrStreetByIds(Long[] ids) {
        return sysAddrStreetMapper.deleteSysAddrStreetByIds(ids);
    }

    /**
     * 删除街道设置信息
     *
     * @param id 街道设置主键
     * @return 结果
     */
    @Override
    public int deleteSysAddrStreetById(Long id) {
        return sysAddrStreetMapper.deleteSysAddrStreetById(id);
    }
}