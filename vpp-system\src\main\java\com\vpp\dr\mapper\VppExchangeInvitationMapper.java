package com.vpp.dr.mapper;

import com.vpp.dr.domain.VppExchangeInvitation;
import java.util.List;
import java.util.Map;

/**
 * 邀约计划Mapper接口（MyBatis）
 */
public interface VppExchangeInvitationMapper {

    /**
     * 查询邀约计划列表（带条件过滤）
     * @param params 查询参数（邀约计划名称、响应日、需求地区、事件状态等）
     * @return 邀约计划集合
     */
    List<VppExchangeInvitation> selectInvitationList(Map<String, Object> params);

    /**
     * 根据ID查询邀约计划详情
     * @param invitationId 邀约计划ID
     * @return 邀约计划对象
     */
    VppExchangeInvitation selectInvitationById(Long invitationId);

    /**
     * 新增邀约计划
     * @param invitation 邀约计划信息
     * @return 结果（影响行数）
     */
    int insertInvitation(VppExchangeInvitation invitation);

    /**
     * 修改邀约计划
     * @param invitation 邀约计划信息
     * @return 结果（影响行数）
     */
    int updateInvitation(VppExchangeInvitation invitation);

    /**
     * 批量删除邀约计划（根据ID数组）
     * @param invitationIds 邀约计划ID数组
     * @return 结果（影响行数）
     */
    int deleteInvitationByIds(Long[] invitationIds);

    /**
     * 批量新增邀约计划（导入用）
     * @param invitations 邀约计划集合
     * @return 结果（影响行数）
     */
    int insertBatchInvitation(List<VppExchangeInvitation> invitations);

    /**
     * 根据ID修改邀约计划发布情况
     * @param invitation 邀约计划
     * @return 邀约计划对象
     */
    int updateById(VppExchangeInvitation invitation);

    /**
     * 查询邀约计划列表（带条件过滤）
     * @param params 查询参数（邀约计划名称、响应日、需求地区、事件状态等）
     * @return 邀约计划集合
     */
    List<VppExchangeInvitation> queryByNameOrNo(Map<String, Object> params);

    /**
     * 更新锁单状态和时间
     */
    int updateLockStatus(VppExchangeInvitation invitation);
}