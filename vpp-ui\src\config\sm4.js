// SM4加密配置文件

// SM4密钥配置
export const SM4_CONFIG = {
  // 密钥 - 32位十六进制字符串（128位密钥）
  // 生产环境建议从环境变量或配置服务获取
  key: process.env.VUE_APP_SM4_KEY || '0103338b0b62198a7b957a011020f03e',

  // 加密模式 - 与后端保持一致使用CBC模式
  mode: 'cbc', // CBC模式，与后端一致

  // 初始化向量 - CBC模式需要IV，与后端保持一致
  iv: process.env.VUE_APP_SM4_IV || '0103338b0b62198a7b957a011020f03e',

  // 填充方式
  padding: 'pkcs#7', // PKCS#7填充

  // 输入输出编码
  inputEncoding: 'utf8',
  outputEncoding: 'hex',

  // 调试模式
  debug: process.env.NODE_ENV === 'development'
}

// 验证SM4密钥格式
export function validateSM4Key(key) {
  if (!key) {
    throw new Error('SM4密钥不能为空')
  }

  if (typeof key !== 'string') {
    throw new Error('SM4密钥必须是字符串')
  }

  if (key.length !== 32) {
    throw new Error('SM4密钥长度必须是32位十六进制字符串（128位）')
  }

  if (!/^[0-9a-fA-F]{32}$/.test(key)) {
    throw new Error('SM4密钥必须是有效的十六进制字符串')
  }

  return true
}

// 获取SM4配置
export function getSM4Config() {
  validateSM4Key(SM4_CONFIG.key)
  return SM4_CONFIG
}

// 日志输出函数
export function logSM4Debug(operation, data) {
  if (SM4_CONFIG.debug) {
    console.log(`[SM4-${operation}]`, data)
  }
}
