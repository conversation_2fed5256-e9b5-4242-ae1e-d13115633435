package com.vpp.history.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface HistoryMapper {
    List<Map<String,Object>> findMoreHistory(@Param("invitation_id") long invitation_id,@Param("dept_id") long dept_id);
    @Select("SELECT invitation_id,invitation_name,invitation_no,response_date,demand_time_slots,demand_region,\n" +
            "response_type,load_direction,\n" +
            "invited_user_count,\n" +
            "aggregated_user_count,unreplied_user_count,\n" +
            "non_participating_user_count,\n" +
            "deadline_time,\n" +
            "market_capacity,\n" +
            "bid_response_capacity,\n" +
            "actual_response_capacity,\n" +
            "disclosure_time,\n" +
            "publish_time,\n" +
            "source_release,aggregator_status,activity_status,publish_status,event_status,lock_status FROM fiotcp_vpp.vpp_exchange_invitation where invitation_id=#{invitation_id};\n")
    Map<String,Object> findHistory(@Param("invitation_id") Long invitationId,@Param("dept_id") Long deptid);
}
