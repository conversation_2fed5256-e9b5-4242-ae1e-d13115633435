package com.vpp.execute.mapper;

import com.vpp.execute.domain.ExecutePlanDevices;

import java.util.List;

/**
 * 执行计划关联的设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ExecutePlanDevicesMapper {
    /**
     * 查询执行计划关联的设备
     *
     * @param executePlanDevicesId 执行计划关联的设备主键
     * @return 执行计划关联的设备
     */
    public ExecutePlanDevices selectExecutePlanDevicesByExecutePlanDevicesId(Long executePlanDevicesId);

    /**
     * 查询执行计划关联的设备列表
     *
     * @param executePlanDevices 执行计划关联的设备
     * @return 执行计划关联的设备集合
     */
    public List<ExecutePlanDevices> selectExecutePlanDevicesList(ExecutePlanDevices executePlanDevices);

    /**
     * 新增执行计划关联的设备
     *
     * @param executePlanDevices 执行计划关联的设备
     * @return 结果
     */
    public int insertExecutePlanDevices(ExecutePlanDevices executePlanDevices);

    /**
     * 修改执行计划关联的设备
     *
     * @param executePlanDevices 执行计划关联的设备
     * @return 结果
     */
    public int updateExecutePlanDevices(ExecutePlanDevices executePlanDevices);

    /**
     * 删除执行计划关联的设备
     *
     * @param executePlanDevicesId 执行计划关联的设备主键
     * @return 结果
     */
    public int deleteExecutePlanDevicesByExecutePlanDevicesId(Long executePlanDevicesId);

    /**
     * 批量删除执行计划关联的设备
     *
     * @param executePlanDevicesIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExecutePlanDevicesByExecutePlanDevicesIds(Long[] executePlanDevicesIds);
}