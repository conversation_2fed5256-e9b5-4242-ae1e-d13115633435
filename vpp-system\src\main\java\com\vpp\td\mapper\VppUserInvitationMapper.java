package com.vpp.td.mapper;

import com.vpp.td.domain.VppUserInvitation;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface VppUserInvitationMapper {
    /**
     * 查询聚合用户邀约列表（带筛选条件）
     */
    List<VppUserInvitation> selectList(VppUserInvitation invitation);

    /**
     * 根据ID查询用户邀约详情
     */
    VppUserInvitation selectById(Long userInvitationId);

    /**
     * 新增用户邀约
     */
    int insert(VppUserInvitation invitation);

    /**
     * 修改用户邀约
     */
    int update(VppUserInvitation invitation);

    /**
     * 逻辑删除用户邀约
     */
    int deleteById(Long userInvitationId);
}
