package com.vpp.mgr.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 聚合商-电厂信息对象 vpp_mgr_agg_pp
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VppMgrAggPp extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long aggPpId;

    /**
     * 虚拟电厂名称
     */
    @Excel(name = "虚拟电厂名称")
    private String vppName;

    /**
     * 虚拟电厂运营商编码，唯一标识
     */
    @Excel(name = "虚拟电厂运营商编码，唯一标识")
    private String vppCode;

    /**
     * 虚拟电厂类型(字典-单选:01-发电类虚拟电厂,02-负荷类虚拟电厂,0201-负荷类日前响应型虚拟电厂,0202-负荷类小时响应型虚拟电厂,0203-负荷类直控型虚拟电厂)
     */
    @Excel(name = "虚拟电厂类型(字典-单选:01-发电类虚拟电厂,02-负荷类虚拟电厂,0201-负荷类日前响应型虚拟电厂,0202-负荷类小时响应型虚拟电厂,0203-负荷类直控型虚拟电厂)")
    private String vppType;

    /**
     * 参与交易品种(字典-多选:01-电能量市场,02-需求响应市场,03-辅助服务市场)
     */
    @Excel(name = "参与交易品种(字典-多选:01-电能量市场,02-需求响应市场,03-辅助服务市场)")
    private String txnVarieties;

    /**
     * 聚合资源类型(字典-多选:01-自备电源,02-用户侧储能,03-电动汽车,04-充电站,05-换电站,06-楼宇空调,07-工商业可调节负荷,08-分布式光伏,09-分散式风电,10-分布式独立储能,99-其他)
     */
    @Excel(name = "聚合资源类型(字典-多选:01-自备电源,02-用户侧储能,03-电动汽车,04-充电站,05-换电站,06-楼宇空调,07-工商业可调节负荷,08-分布式光伏,09-分散式风电,10-分布式独立储能,99-其他)")
    private String resType;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "注册时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date regTime;

    /**
     * 投运时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "投运时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date comTime;

    /**
     * 入市时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入市时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entryTime;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contact;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String phone;

    /**
     * 运行状态(字典:01-运行中,02-撤销,03-退市,04-待运行)
     */
    @Excel(name = "运行状态(字典:01-运行中,02-撤销,03-退市,04-待运行)")
    private String status;

    /**
     * 部门(机构)ID
     */
    @Excel(name = "部门(机构)ID")
    private Long deptId;

    /**
     * 用户ID(sys_user)
     */
    @Excel(name = "用户ID(sys_user)")
    private Long userId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

}