package com.vpp.td.service;

import com.vpp.td.domain.VppInvitationDeclaration;
import java.util.List;
import java.util.Map;

public interface IVppInvitationDeclarationService {
    List<VppInvitationDeclaration> selectList(Map<String, Object> params);
    VppInvitationDeclaration selectById(Long declarationId);
    int insert(VppInvitationDeclaration declaration);
    int update(VppInvitationDeclaration declaration);
    int deleteById(Long declarationId);
}