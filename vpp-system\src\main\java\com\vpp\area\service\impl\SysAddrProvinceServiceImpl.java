package com.vpp.area.service.impl;

import com.vpp.area.domain.SysAddrProvince;
import com.vpp.area.mapper.SysAddrProvinceMapper;
import com.vpp.area.service.ISysAddrProvinceService;
import com.vpp.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 省份Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class SysAddrProvinceServiceImpl implements ISysAddrProvinceService
{
    @Autowired
    private SysAddrProvinceMapper sysAddrProvinceMapper;

    /**
     * 查询省份
     *
     * @param id 省份主键
     * @return 省份
     */
    @Override
    public SysAddrProvince selectSysAddrProvinceById(String id)
    {
        return sysAddrProvinceMapper.selectSysAddrProvinceById(id);
    }

    /**
     * 查询省份列表
     *
     * @param sysAddrProvince 省份
     * @return 省份
     */
    @Override
    public List<SysAddrProvince> selectSysAddrProvinceList(SysAddrProvince sysAddrProvince)
    {
        return sysAddrProvinceMapper.selectSysAddrProvinceList(sysAddrProvince);
    }

    /**
     * 新增省份
     *
     * @param sysAddrProvince 省份
     * @return 结果
     */
    @Override
    public int insertSysAddrProvince(SysAddrProvince sysAddrProvince)
    {
        sysAddrProvince.setCreateTime(DateUtils.getNowDate());
        return sysAddrProvinceMapper.insertSysAddrProvince(sysAddrProvince);
    }

    /**
     * 修改省份
     *
     * @param sysAddrProvince 省份
     * @return 结果
     */
    @Override
    public int updateSysAddrProvince(SysAddrProvince sysAddrProvince)
    {
        sysAddrProvince.setUpdateTime(DateUtils.getNowDate());
        return sysAddrProvinceMapper.updateSysAddrProvince(sysAddrProvince);
    }

    /**
     * 批量删除省份
     *
     * @param ids 需要删除的省份主键
     * @return 结果
     */
    @Override
    public int deleteSysAddrProvinceByIds(String[] ids)
    {
        return sysAddrProvinceMapper.deleteSysAddrProvinceByIds(ids);
    }

    /**
     * 删除省份信息
     *
     * @param id 省份主键
     * @return 结果
     */
    @Override
    public int deleteSysAddrProvinceById(String id)
    {
        return sysAddrProvinceMapper.deleteSysAddrProvinceById(id);
    }
}