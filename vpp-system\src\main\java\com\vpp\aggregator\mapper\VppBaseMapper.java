package com.vpp.aggregator.mapper;

import com.vpp.aggregator.domain.VppBase;

import java.util.List;

/**
 * 虚拟电厂聚合商Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface VppBaseMapper {
    /**
     * 查询虚拟电厂聚合商
     *
     * @param vppId 虚拟电厂聚合商主键
     * @return 虚拟电厂聚合商
     */
    public VppBase selectVppBaseByVppId(Long vppId);

    /**
     * 查询虚拟电厂聚合商列表
     *
     * @param vppBase 虚拟电厂聚合商
     * @return 虚拟电厂聚合商集合
     */
    public List<VppBase> selectVppBaseList(VppBase vppBase);

    /**
     * 新增虚拟电厂聚合商
     *
     * @param vppBase 虚拟电厂聚合商
     * @return 结果
     */
    public int insertVppBase(VppBase vppBase);

    /**
     * 修改虚拟电厂聚合商
     *
     * @param vppBase 虚拟电厂聚合商
     * @return 结果
     */
    public int updateVppBase(VppBase vppBase);

    /**
     * 删除虚拟电厂聚合商
     *
     * @param vppId 虚拟电厂聚合商主键
     * @return 结果
     */
    public int deleteVppBaseByVppId(Long vppId);

    /**
     * 批量删除虚拟电厂聚合商
     *
     * @param vppIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVppBaseByVppIds(Long[] vppIds);
}
