package com.vpp.td.service.impl;

import com.vpp.td.domain.VppUserCapacity;
import com.vpp.td.mapper.VppUserCapacityMapper;
import com.vpp.td.service.IVppUserCapacityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VppUserCapacityServiceImpl implements IVppUserCapacityService {
    @Autowired
    private VppUserCapacityMapper userCapacityMapper;

    @Override
    public List<VppUserCapacity> selectList(VppUserCapacity capacity) {
        return userCapacityMapper.selectList(capacity);
    }

    @Override
    public VppUserCapacity selectById(Long capacityId) {
        return userCapacityMapper.selectById(capacityId);
    }

    @Override
    public int insert(VppUserCapacity capacity) {
        return userCapacityMapper.insert(capacity);
    }

    @Override
    public int update(VppUserCapacity capacity) {
        return userCapacityMapper.update(capacity);
    }

    @Override
    public int deleteById(Long capacityId) {
        return userCapacityMapper.deleteById(capacityId);
    }
}
