package com.vpp.aggregator.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 交易中心下发活动对象 vpp_base_activity
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public class VppBaseActivity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long activityId;

    /**
     * 交易中心下发活动名称
     */
    @Excel(name = "交易中心下发活动名称")
    private String activityName;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date activityStartTime;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date activityEndTime;

    /**
     * 活动状态，
     */
    @Excel(name = "活动状态，")
    private Long activityStatus;

    /**
     * 活动需要的容量
     */
    @Excel(name = "活动需要的容量")
    private Long activityCapacity;

    /**
     * 活动编号
     */
    @Excel(name = "活动编号")
    private String activityCode;

    /**
     * 活动所在省
     */
    @Excel(name = "活动所在省")
    private Long activityProvince;

    /**
     * 活动所在市
     */
    @Excel(name = "活动所在市")
    private Long activityMunicipality;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long activityRegional;

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityStartTime(Date activityStartTime) {
        this.activityStartTime = activityStartTime;
    }

    public Date getActivityStartTime() {
        return activityStartTime;
    }

    public void setActivityEndTime(Date activityEndTime) {
        this.activityEndTime = activityEndTime;
    }

    public Date getActivityEndTime() {
        return activityEndTime;
    }

    public void setActivityStatus(Long activityStatus) {
        this.activityStatus = activityStatus;
    }

    public Long getActivityStatus() {
        return activityStatus;
    }

    public void setActivityCapacity(Long activityCapacity) {
        this.activityCapacity = activityCapacity;
    }

    public Long getActivityCapacity() {
        return activityCapacity;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityProvince(Long activityProvince) {
        this.activityProvince = activityProvince;
    }

    public Long getActivityProvince() {
        return activityProvince;
    }

    public void setActivityMunicipality(Long activityMunicipality) {
        this.activityMunicipality = activityMunicipality;
    }

    public Long getActivityMunicipality() {
        return activityMunicipality;
    }

    public void setActivityRegional(Long activityRegional) {
        this.activityRegional = activityRegional;
    }

    public Long getActivityRegional() {
        return activityRegional;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("activityId", getActivityId())
                .append("activityName", getActivityName())
                .append("activityStartTime", getActivityStartTime())
                .append("activityEndTime", getActivityEndTime())
                .append("activityStatus", getActivityStatus())
                .append("activityCapacity", getActivityCapacity())
                .append("activityCode", getActivityCode())
                .append("activityProvince", getActivityProvince())
                .append("activityMunicipality", getActivityMunicipality())
                .append("activityRegional", getActivityRegional())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}