package com.vpp.re.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 运行监控表实体类（实现Serializable）
 */
@Data
@ToString
@ApiModel(value = "运行监控表实体", description = "聚合响应执行追踪对应的实体类")
public class VppOperationMonitoring implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "监控记录主键ID", example = "1")
    private Long monitoringId;

    @ApiModelProperty(value = "关联邀约计划ID", example = "44")
    private Long invitationId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "监控时间", example = "2025-07-27 10:15:00")
    private LocalDateTime monitoringTime;

    @ApiModelProperty(value = "实时负荷 (MW)", example = "3.5")
    private BigDecimal actualLoad;

    @ApiModelProperty(value = "有效响应负荷上限 (MW)", example = "4.0")
    private BigDecimal effectiveResponseUpperLimit;

    @ApiModelProperty(value = "有效响应负荷下限 (MW)", example = "2.0")
    private BigDecimal effectiveResponseLowerLimit;

    @ApiModelProperty(value = "负荷方向（削峰响应/填谷响应）", example = "削峰响应")
    private String loadDirection;

    @ApiModelProperty(value = "响应类型（日前响应/日内响应）", example = "日前响应")
    private String responseType;

    @ApiModelProperty(value = "监控状态（进行中/已完成/数据异常）", example = "进行中")
    private String monitoringStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "2025-07-27 10:15:00")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间", example = "2025-07-27 10:30:00")
    private LocalDateTime updateTime;
}