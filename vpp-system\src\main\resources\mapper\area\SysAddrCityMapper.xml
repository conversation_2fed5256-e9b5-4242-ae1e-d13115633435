<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.area.mapper.SysAddrCityMapper">

    <resultMap type="SysAddrCity" id="SysAddrCityResult">
        <result property="id"    column="id"    />
        <result property="cityCode"    column="city_code"    />
        <result property="cityName"    column="city_name"    />
        <result property="shortName"    column="short_name"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="lng"    column="lng"    />
        <result property="lat"    column="lat"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysAddrCityVo">
        select id, city_code, city_name, short_name, province_code, lng, lat, sort, create_time, update_time, create_by, update_by, remark from sys_addr_city
    </sql>

    <select id="selectSysAddrCityList" parameterType="SysAddrCity" resultMap="SysAddrCityResult">
        <include refid="selectSysAddrCityVo"/>
        <where>
            <if test="cityCode != null  and cityCode != ''"> and city_code = #{cityCode}</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="shortName != null  and shortName != ''"> and short_name like concat('%', #{shortName}, '%')</if>
            <if test="provinceCode != null  and provinceCode != ''"> and province_code = #{provinceCode}</if>
            <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectSysAddrCityById" parameterType="String" resultMap="SysAddrCityResult">
        <include refid="selectSysAddrCityVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysAddrCity" parameterType="SysAddrCity">
        insert into sys_addr_city
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="cityCode != null and cityCode != ''">city_code,</if>
            <if test="cityName != null and cityName != ''">city_name,</if>
            <if test="shortName != null and shortName != ''">short_name,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="cityCode != null and cityCode != ''">#{cityCode},</if>
            <if test="cityName != null and cityName != ''">#{cityName},</if>
            <if test="shortName != null and shortName != ''">#{shortName},</if>
            <if test="provinceCode != null">#{provinceCode},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSysAddrCity" parameterType="SysAddrCity">
        update sys_addr_city
        <trim prefix="SET" suffixOverrides=",">
            <if test="cityCode != null and cityCode != ''">city_code = #{cityCode},</if>
            <if test="cityName != null and cityName != ''">city_name = #{cityName},</if>
            <if test="shortName != null and shortName != ''">short_name = #{shortName},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAddrCityById" parameterType="String">
        delete from sys_addr_city where id = #{id}
    </delete>

    <delete id="deleteSysAddrCityByIds" parameterType="String">
        delete from sys_addr_city where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>