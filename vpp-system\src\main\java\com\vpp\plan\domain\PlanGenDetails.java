package com.vpp.plan.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 生成计划的详细信息，key-value形式对象 plan_gen_details
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public class PlanGenDetails extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long planGenDetailsId;

    /**
     * 生成的计划额外属性名
     */
    @Excel(name = "生成的计划额外属性名")
    private String attributeKey;

    /**
     * 生成的计划额外的值
     */
    @Excel(name = "生成的计划额外的值")
    private String attributeValue;

    /**
     * 关联plan_gen id
     */
    @Excel(name = "关联plan_gen id")
    private Long planGenId;

    public void setPlanGenDetailsId(Long planGenDetailsId) {
        this.planGenDetailsId = planGenDetailsId;
    }

    public Long getPlanGenDetailsId() {
        return planGenDetailsId;
    }

    public void setAttributeKey(String attributeKey) {
        this.attributeKey = attributeKey;
    }

    public String getAttributeKey() {
        return attributeKey;
    }

    public void setAttributeValue(String attributeValue) {
        this.attributeValue = attributeValue;
    }

    public String getAttributeValue() {
        return attributeValue;
    }

    public void setPlanGenId(Long planGenId) {
        this.planGenId = planGenId;
    }

    public Long getPlanGenId() {
        return planGenId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("planGenDetailsId", getPlanGenDetailsId())
                .append("attributeKey", getAttributeKey())
                .append("attributeValue", getAttributeValue())
                .append("planGenId", getPlanGenId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}