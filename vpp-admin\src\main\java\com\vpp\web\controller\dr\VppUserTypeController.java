package com.vpp.web.controller.dr;

import com.vpp.common.annotation.Log;
import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.common.enums.BusinessType;
import com.vpp.dr.domain.VppUserType;
import com.vpp.dr.service.IVppUserTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/dr/user/type")
@Api(tags = "用户类型管理", description = "虚拟电厂系统平台-用户类型管理接口")
public class VppUserTypeController extends BaseController {

    @Autowired
    private IVppUserTypeService userTypeService;

    /**
     * 查询所有用户类型（含层级结构）
     */
    @GetMapping("/list")
    @ApiOperation("查询所有用户类型")
    public TableDataInfo list() {
        List<VppUserType> list = userTypeService.getAllUserTypes();
        return getDataTable(list);
    }

    /**
     * 根据ID获取用户类型详情
     */
    @GetMapping("/{id}")
    @ApiOperation("获取用户类型详情")
    public AjaxResult getInfo(@PathVariable Long id) {
        VppUserType userType = userTypeService.getUserTypeById(id);
        return AjaxResult.success(userType);
    }

    /**
     * 新增用户类型
     */
    @Log(title = "用户类型", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增用户类型")
    public AjaxResult add(@RequestBody VppUserType userType) {
        return toAjax(userTypeService.addUserType(userType));
    }

    /**
     * 修改用户类型
     */
    @Log(title = "用户类型", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改用户类型")
    public AjaxResult edit(@RequestBody VppUserType userType) {
        return toAjax(userTypeService.updateUserType(userType));
    }

    /**
     * 删除用户类型
     */
    @Log(title = "用户类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @ApiOperation("删除用户类型")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(userTypeService.deleteUserType(id));
    }
    /**
     * 根据用户类型编码查询用户类型及关联用户
     * @param userId
     * @return 用户类型对象（包含用户列表）
     */
    @GetMapping("/queryUsersByUserType/{userId}")
    public AjaxResult queryUsersByUserType(@PathVariable Long userId) {
        // 调用 Service 查询
        VppUserType userType = userTypeService.queryUsersByUserType(userId);
        // 返回成功结果（若用户类型不存在，Service 会抛异常，此处可捕获并返回错误）
        return AjaxResult.success(userType);
    }
}