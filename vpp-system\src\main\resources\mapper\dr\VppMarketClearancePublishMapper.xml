<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.dr.mapper.VppMarketClearancePublishMapper">

    <!-- 基础映射 -->
    <resultMap id="PublishBaseMap" type="VppMarketClearancePublish">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="send_user_type_code" property="sendUserTypeCode" jdbcType="VARCHAR"/>
        <result column="permission_status" property="permissionStatus" jdbcType="INTEGER"/>
        <result column="is_deleted" property="isDeleted" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分页查询发布配置列表 -->
    <select id="selectPublishList" parameterType="map" resultMap="PublishBaseMap">
        SELECT * FROM vpp_market_clearance_publish
        <where>
            <if test="publishTimeStart != null">
                AND publish_time &gt;= #{publishTimeStart}
            </if>
            <if test="publishTimeEnd != null">
                AND publish_time &lt;= #{publishTimeEnd}
            </if>
            <if test="sendUserTypeCodes != null and sendUserTypeCodes.size() > 0">
                AND send_user_type_code IN (
                <foreach collection="sendUserTypeCodes" item="code" separator=",">
                    #{code}
                </foreach>
                )
            </if>
            <if test="permissionStatus != null">
                AND permission_status = #{permissionStatus}
            </if>
            <!-- 排除已删除数据 -->
            AND is_deleted = '0'
        </where>
        ORDER BY publish_time DESC
    </select>

    <!-- 查询发布配置及关联的用户选择 -->
    <select id="selectPublishWithUserSelection" parameterType="Long" resultMap="PublishBaseMap">
        SELECT
            p.*,
            us.user_type_code,
            us.is_all,
            us.user_id
        FROM vpp_market_clearance_publish p
                 LEFT JOIN vpp_user_selection us ON p.id = us.publish_id
        WHERE p.id = #{id}
    </select>

    <!-- 插入发布配置 -->
    <insert id="insertPublish" parameterType="VppMarketClearancePublish" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO vpp_market_clearance_publish
        (publish_time, send_user_type_code, permission_status, is_deleted, create_by, create_time, update_time)
        VALUES
            (#{publishTime}, #{sendUserTypeCode}, #{permissionStatus}, #{isDeleted}, #{createBy}, NOW(), NOW())
    </insert>

    <!-- 更新发布配置 -->
    <update id="updatePublish" parameterType="VppMarketClearancePublish">
        UPDATE vpp_market_clearance_publish
        SET
            send_user_type_code = #{sendUserTypeCode},
            permission_status = #{permissionStatus},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 删除发布配置（逻辑删除） -->
    <update id="deletePublish" parameterType="Long">
        UPDATE vpp_market_clearance_publish
        SET is_deleted = 1
        WHERE id = #{id}
    </update>

</mapper>