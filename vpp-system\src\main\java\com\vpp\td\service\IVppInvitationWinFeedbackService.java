package com.vpp.td.service;

import com.vpp.td.domain.VppInvitationWinFeedback;
import java.util.List;
import java.util.Map;

public interface IVppInvitationWinFeedbackService {
    List<VppInvitationWinFeedback> selectList(Map<String, Object> params);
    VppInvitationWinFeedback selectById(Long winFeedbackId);
    int insert(VppInvitationWinFeedback winFeedback);
    int update(VppInvitationWinFeedback winFeedback);
    int deleteById(Long winFeedbackId);
}