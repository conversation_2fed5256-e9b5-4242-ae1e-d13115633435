package com.vpp.mgr.service.impl;

import com.vpp.common.utils.DateUtils;
import com.vpp.common.utils.SecurityUtils;
import com.vpp.mgr.domain.VppMgrCnt;
import com.vpp.mgr.domain.VppMgrCntAtt;
import com.vpp.mgr.domain.VppMgrUag;
import com.vpp.mgr.mapper.VppMgrCntAttMapper;
import com.vpp.mgr.mapper.VppMgrCntMapper;
import com.vpp.mgr.mapper.VppMgrUagMapper;
import com.vpp.mgr.service.IVppMgrCntService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 合同(聚合商-聚合用户)Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class VppMgrCntServiceImpl implements IVppMgrCntService {
    @Autowired
    private VppMgrCntMapper vppMgrCntMapper;
    @Autowired
    private VppMgrCntAttMapper vppMgrCntAttMapper;
    @Autowired
    private VppMgrUagMapper uagMapper;
    /**
     * 查询合同(聚合商-聚合用户)
     *
     * @param cntId 合同(聚合商-聚合用户)主键
     * @return 合同(聚合商 - 聚合用户)
     */
    @Override
    public VppMgrCnt selectVppMgrCntByCntId(Long cntId) {
        VppMgrCnt vppMgrCnt = vppMgrCntMapper.selectVppMgrCntByCntId(cntId);
        List<VppMgrCntAtt> vppMgrCntAttList = vppMgrCntAttMapper.selectByCntId(vppMgrCnt.getCntId());
        vppMgrCnt.setVppMgrCntAttList(vppMgrCntAttList);
        return vppMgrCnt;
    }

    /**
     * 查询合同(聚合商-聚合用户)列表
     *
     * @param vppMgrCnt 合同(聚合商-聚合用户)
     * @return 合同(聚合商 - 聚合用户)
     */
    @Override
    public List<VppMgrCnt> selectVppMgrCntList(VppMgrCnt vppMgrCnt) {
        List<VppMgrCnt> vppMgrCnts = vppMgrCntMapper.selectVppMgrCntList(vppMgrCnt);
        if(!CollectionUtils.isEmpty(vppMgrCnts)){
            vppMgrCnts.forEach(vppMgrCnt1 -> {
                List<VppMgrCntAtt> vppMgrCntAttList = vppMgrCntAttMapper.selectByCntId(vppMgrCnt1.getCntId());
                vppMgrCnt1.setVppMgrCntAttList(vppMgrCntAttList);
            });
        }
        return vppMgrCnts;
    }

    /**
     * 新增合同(聚合商-聚合用户)
     *
     * @param vppMgrCnt 合同(聚合商-聚合用户)
     * @return 结果
     */
    @Override
    public int insertVppMgrCnt(VppMgrCnt vppMgrCnt) {
        vppMgrCnt.setCreateTime(DateUtils.getNowDate());
        vppMgrCnt.setProxyUser(uagMapper.selectVppMgrUagByUagId(vppMgrCnt.getUagId()).getUagName());
        List<VppMgrCntAtt> vppMgrCntAttList = vppMgrCnt.getVppMgrCntAttList();
        int id = vppMgrCntMapper.insertVppMgrCnt(vppMgrCnt);
        if(!CollectionUtils.isEmpty(vppMgrCntAttList)){
            vppMgrCntAttList.forEach(vppMgrCntAtt -> vppMgrCntAtt.setUagCntId((long) id));
            vppMgrCntAttMapper.saveOrUpdateBatch(vppMgrCntAttList);
        }
        return id;
    }

    /**
     * 修改合同(聚合商-聚合用户)
     *
     * @param vppMgrCnt 合同(聚合商-聚合用户)
     * @return 结果
     */
    @Override
    public int updateVppMgrCnt(VppMgrCnt vppMgrCnt) {
        vppMgrCnt.setUpdateTime(DateUtils.getNowDate());
        int i = vppMgrCntMapper.updateVppMgrCnt(vppMgrCnt);
        vppMgrCntAttMapper.deleteVppMgrCntAttByUagCntId(vppMgrCnt.getCntId());
        List<VppMgrCntAtt> vppMgrCntAttList = vppMgrCnt.getVppMgrCntAttList();
        if(!CollectionUtils.isEmpty(vppMgrCntAttList)){
            vppMgrCntAttList.forEach(vppMgrCntAtt -> vppMgrCntAtt.setUagCntId(vppMgrCnt.getCntId()));
            vppMgrCntAttMapper.saveOrUpdateBatch(vppMgrCntAttList);
        }
        return i;
    }

    /**
     * 批量删除合同(聚合商-聚合用户)
     *
     * @param cntIds 需要删除的合同(聚合商-聚合用户)主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrCntByCntIds(Long[] cntIds) {
        vppMgrCntAttMapper.deleteVppMgrCntAttByUagCntIds(cntIds);
        return vppMgrCntMapper.deleteVppMgrCntByCntIds(cntIds);
    }

    /**
     * 删除合同(聚合商-聚合用户)信息
     *
     * @param cntId 合同(聚合商-聚合用户)主键
     * @return 结果
     */
    @Override
    public int deleteVppMgrCntByCntId(Long cntId) {
        vppMgrCntAttMapper.deleteVppMgrCntAttByUagCntId(cntId);
        return vppMgrCntMapper.deleteVppMgrCntByCntId(cntId);
    }

    //===============================================================

    @Override
    public List<VppMgrCnt> listByUserId(Long userId) {
        return vppMgrCntMapper.listByUserId(userId);
    }

    @Override
    public List<VppMgrCnt> listByDeptId(Long deptId) {
        return vppMgrCntMapper.listByDeptId(deptId);
    }

    @Override
    public List<VppMgrUag> noCntUag() {
        //列出所有未签订合约的用户

        return uagMapper.selectVppMgrUagNoCnt(SecurityUtils.getDeptId());
    }
}