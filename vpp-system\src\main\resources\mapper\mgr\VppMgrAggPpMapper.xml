<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vpp.mgr.mapper.VppMgrAggPpMapper">

    <resultMap type="VppMgrAggPp" id="VppMgrAggPpResult">
        <result property="aggPpId"    column="agg_pp_id"    />
        <result property="vppName"    column="vpp_name"    />
        <result property="vppCode"    column="vpp_code"    />
        <result property="vppType"    column="vpp_type"    />
        <result property="txnVarieties"    column="txn_varieties"    />
        <result property="resType"    column="res_type"    />
        <result property="regTime"    column="reg_time"    />
        <result property="comTime"    column="com_time"    />
        <result property="entryTime"    column="entry_time"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
        <result property="status"    column="status"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVppMgrAggPpVo">
        select agg_pp_id, vpp_name, vpp_code, vpp_type, txn_varieties, res_type, reg_time, com_time, entry_time, contact, phone, status, dept_id, user_id, del_flag, create_by, create_time, update_by, update_time, remark from vpp_mgr_agg_pp
    </sql>

    <select id="selectVppMgrAggPpList" parameterType="VppMgrAggPp" resultMap="VppMgrAggPpResult">
        <include refid="selectVppMgrAggPpVo"/>
        <where>
            <if test="vppName != null  and vppName != ''"> and vpp_name like concat('%', #{vppName}, '%')</if>
            <if test="vppCode != null  and vppCode != ''"> and vpp_code = #{vppCode}</if>
            <if test="vppType != null  and vppType != ''"> and vpp_type = #{vppType}</if>
            <if test="txnVarieties != null  and txnVarieties != ''"> and txn_varieties = #{txnVarieties}</if>
            <if test="resType != null  and resType != ''"> and res_type = #{resType}</if>
            <if test="regTime != null "> and reg_time = #{regTime}</if>
            <if test="comTime != null "> and com_time = #{comTime}</if>
            <if test="entryTime != null "> and entry_time = #{entryTime}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectVppMgrAggPpByAggPpId" parameterType="Long" resultMap="VppMgrAggPpResult">
        <include refid="selectVppMgrAggPpVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertVppMgrAggPp" parameterType="VppMgrAggPp" useGeneratedKeys="true" keyProperty="aggPpId">
        insert into vpp_mgr_agg_pp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vppName != null and vppName != ''">vpp_name,</if>
            <if test="vppCode != null and vppCode != ''">vpp_code,</if>
            <if test="vppType != null and vppType != ''">vpp_type,</if>
            <if test="txnVarieties != null and txnVarieties != ''">txn_varieties,</if>
            <if test="resType != null and resType != ''">res_type,</if>
            <if test="regTime != null">reg_time,</if>
            <if test="comTime != null">com_time,</if>
            <if test="entryTime != null">entry_time,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vppName != null and vppName != ''">#{vppName},</if>
            <if test="vppCode != null and vppCode != ''">#{vppCode},</if>
            <if test="vppType != null and vppType != ''">#{vppType},</if>
            <if test="txnVarieties != null and txnVarieties != ''">#{txnVarieties},</if>
            <if test="resType != null and resType != ''">#{resType},</if>
            <if test="regTime != null">#{regTime},</if>
            <if test="comTime != null">#{comTime},</if>
            <if test="entryTime != null">#{entryTime},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVppMgrAggPp" parameterType="VppMgrAggPp">
        update vpp_mgr_agg_pp
        <trim prefix="SET" suffixOverrides=",">
            <if test="vppName != null and vppName != ''">vpp_name = #{vppName},</if>
            <if test="vppCode != null and vppCode != ''">vpp_code = #{vppCode},</if>
            <if test="vppType != null and vppType != ''">vpp_type = #{vppType},</if>
            <if test="txnVarieties != null and txnVarieties != ''">txn_varieties = #{txnVarieties},</if>
            <if test="resType != null and resType != ''">res_type = #{resType},</if>
            <if test="regTime != null">reg_time = #{regTime},</if>
            <if test="comTime != null">com_time = #{comTime},</if>
            <if test="entryTime != null">entry_time = #{entryTime},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where agg_pp_id = #{aggPpId}
    </update>

    <delete id="deleteVppMgrAggPpByAggPpId" parameterType="Long">
        delete from vpp_mgr_agg_pp where agg_pp_id = #{aggPpId}
    </delete>

    <delete id="deleteVppMgrAggPpByAggPpIds" parameterType="String">
        delete from vpp_mgr_agg_pp where agg_pp_id in
        <foreach item="aggPpId" collection="array" open="(" separator="," close=")">
            #{aggPpId}
        </foreach>
    </delete>
</mapper>