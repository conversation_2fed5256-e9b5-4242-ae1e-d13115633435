package com.vpp.plan.service;

import com.vpp.plan.domain.PlanGenPrep;

import java.util.List;

/**
 * 预备设备，当出现异常时，使用预备设备Service接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface IPlanGenPrepService {
    /**
     * 查询预备设备，当出现异常时，使用预备设备
     *
     * @param planGenPrepId 预备设备，当出现异常时，使用预备设备主键
     * @return 预备设备，当出现异常时，使用预备设备
     */
    public PlanGenPrep selectPlanGenPrepByPlanGenPrepId(Long planGenPrepId);

    /**
     * 查询预备设备，当出现异常时，使用预备设备列表
     *
     * @param planGenPrep 预备设备，当出现异常时，使用预备设备
     * @return 预备设备，当出现异常时，使用预备设备集合
     */
    public List<PlanGenPrep> selectPlanGenPrepList(PlanGenPrep planGenPrep);

    /**
     * 新增预备设备，当出现异常时，使用预备设备
     *
     * @param planGenPrep 预备设备，当出现异常时，使用预备设备
     * @return 结果
     */
    public int insertPlanGenPrep(PlanGenPrep planGenPrep);

    /**
     * 修改预备设备，当出现异常时，使用预备设备
     *
     * @param planGenPrep 预备设备，当出现异常时，使用预备设备
     * @return 结果
     */
    public int updatePlanGenPrep(PlanGenPrep planGenPrep);

    /**
     * 批量删除预备设备，当出现异常时，使用预备设备
     *
     * @param planGenPrepIds 需要删除的预备设备，当出现异常时，使用预备设备主键集合
     * @return 结果
     */
    public int deletePlanGenPrepByPlanGenPrepIds(Long[] planGenPrepIds);

    /**
     * 删除预备设备，当出现异常时，使用预备设备信息
     *
     * @param planGenPrepId 预备设备，当出现异常时，使用预备设备主键
     * @return 结果
     */
    public int deletePlanGenPrepByPlanGenPrepId(Long planGenPrepId);
}
