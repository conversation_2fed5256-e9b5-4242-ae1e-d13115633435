package com.vpp.aggregator.mapper;

import com.vpp.aggregator.domain.VppBaseDevice;

import java.util.List;

/**
 * 聚合用户设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface VppBaseDeviceMapper {
    /**
     * 查询聚合用户设备
     *
     * @param deviceId 聚合用户设备主键
     * @return 聚合用户设备
     */
    public VppBaseDevice selectVppBaseDeviceByDeviceId(Long deviceId);

    /**
     * 查询聚合用户设备列表
     *
     * @param vppBaseDevice 聚合用户设备
     * @return 聚合用户设备集合
     */
    public List<VppBaseDevice> selectVppBaseDeviceList(VppBaseDevice vppBaseDevice);

    /**
     * 新增聚合用户设备
     *
     * @param vppBaseDevice 聚合用户设备
     * @return 结果
     */
    public int insertVppBaseDevice(VppBaseDevice vppBaseDevice);

    /**
     * 修改聚合用户设备
     *
     * @param vppBaseDevice 聚合用户设备
     * @return 结果
     */
    public int updateVppBaseDevice(VppBaseDevice vppBaseDevice);

    /**
     * 删除聚合用户设备
     *
     * @param deviceId 聚合用户设备主键
     * @return 结果
     */
    public int deleteVppBaseDeviceByDeviceId(Long deviceId);

    /**
     * 批量删除聚合用户设备
     *
     * @param deviceIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVppBaseDeviceByDeviceIds(Long[] deviceIds);
}