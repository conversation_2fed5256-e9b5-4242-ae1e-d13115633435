package com.vpp.execute.service;

import com.vpp.execute.domain.ExecuteDeviceStatus;

import java.util.Date;
import java.util.List;

/**
 * 设备实时检测状态Service接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface IExecuteDeviceStatusService {
    /**
     * 查询设备实时检测状态
     *
     * @param timestamp 设备实时检测状态主键
     * @return 设备实时检测状态
     */
    public ExecuteDeviceStatus selectExecuteDeviceStatusByTimestamp(Date timestamp);

    /**
     * 查询设备实时检测状态列表
     *
     * @param executeDeviceStatus 设备实时检测状态
     * @return 设备实时检测状态集合
     */
    public List<ExecuteDeviceStatus> selectExecuteDeviceStatusList(ExecuteDeviceStatus executeDeviceStatus);

    /**
     * 新增设备实时检测状态
     *
     * @param executeDeviceStatus 设备实时检测状态
     * @return 结果
     */
    public int insertExecuteDeviceStatus(ExecuteDeviceStatus executeDeviceStatus);

    /**
     * 修改设备实时检测状态
     *
     * @param executeDeviceStatus 设备实时检测状态
     * @return 结果
     */
    public int updateExecuteDeviceStatus(ExecuteDeviceStatus executeDeviceStatus);

    /**
     * 批量删除设备实时检测状态
     *
     * @param timestamps 需要删除的设备实时检测状态主键集合
     * @return 结果
     */
    public int deleteExecuteDeviceStatusByTimestamps(Date[] timestamps);

    /**
     * 删除设备实时检测状态信息
     *
     * @param timestamp 设备实时检测状态主键
     * @return 结果
     */
    public int deleteExecuteDeviceStatusByTimestamp(Date timestamp);
}
