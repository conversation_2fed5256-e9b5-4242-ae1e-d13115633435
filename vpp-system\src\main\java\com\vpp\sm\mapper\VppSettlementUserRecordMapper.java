package com.vpp.sm.mapper;

import com.vpp.sm.domain.VppSettlementUag;
import com.vpp.sm.domain.VppUagSettlement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface VppSettlementUserRecordMapper {
    @Select("select a.invitation_id invitationId,(select invitation_name from vpp_exchange_invitation where invitation_id=a.invitation_id) as invitationName,\n" +
            "(select invitation_no from vpp_exchange_invitation where invitation_id=a.invitation_id) as invitationCode,\n" +
            "(select demand_time_slots from vpp_exchange_invitation where invitation_id=a.invitation_id) as demandTime,\n" +
            "(select load_direction from vpp_exchange_invitation where invitation_id=a.invitation_id) as directive,\n" +
            "(select response_date from vpp_exchange_invitation where invitation_id=a.invitation_id) as respondDate,\n" +
            "(select demand_region from vpp_exchange_invitation where invitation_id=a.invitation_id) as region,\n" +
            "(select deadline_time from vpp_exchange_invitation where invitation_id=a.invitation_id) as deadline,\n" +
            "a.pricing_mode as mode,a.amount as amount from vpp_user_settlement a ,(select b.*,a.uag_account_num from vpp_mgr_uag_account_num a right join vpp_mgr_uag b on a.dept_id=b.dept_id where a.uag_account_num='*********')b\n" +
            "where a.uag_id=b.uag_id and a.uag_id=#{uagid}")
    List<VppUagSettlement> allVppSettlementUserRecord(Long uagid);

    @Select("select b.uag_id id,b.uag_name name,b.uag_code code,b.uag_type type,a.uag_account_num accountNumber,b.contact contact,b.* from vpp_mgr_uag_account_num a right join vpp_mgr_uag b on a.dept_id=b.dept_id left join sys_user c on c.user_id=b.user_id where c.dept_id=#{dept}")
    List<VppSettlementUag> listVppUag(Long dept);
}
