package com.vpp.mgr.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vpp.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 合同附件(聚合商-聚合用户)对象 vpp_mgr_cnt_att
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VppMgrCntAtt implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 合同id
     */
    private Long uagCntId;

    /**
     * 附件url
     */
    @Excel(name = "附件url")
    private String attrUrl;

    /** 附件名称 */
    @Excel(name = "附件名称")
    private String attrName;

    /** 附件上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "附件上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uploadDate;
}