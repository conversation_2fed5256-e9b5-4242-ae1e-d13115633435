package com.vpp.plan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 自动生成的计划对象 plan_gen
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public class PlanGen extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long planGenId;

    /**
     * 生成的计划名称
     */
    @Excel(name = "生成的计划名称")
    private String planGenName;

    /**
     * 生成计划状态，0未使用，1已使用并申报，2,中标，中标后自动在执行计划里添加
     */
    @Excel(name = "生成计划状态，0未使用，1已使用并申报，2,中标，中标后自动在执行计划里添加")
    private String planGenStatus;

    /**
     * 关联的虚拟电厂编号
     */
    @Excel(name = "关联的虚拟电厂编号")
    private String planGenVpp;

    /**
     * 关联的活动
     */
    @Excel(name = "关联的活动")
    private String planGenActivity;

    /**
     * 关联设备总数
     */
    @Excel(name = "关联设备总数")
    private Long planGenDevicesTotal;

    /**
     * 生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date planGenTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date planGenExpireTime;

    /**
     * 计划总额定功率
     */
    @Excel(name = "计划总额定功率")
    private Long planGenRatedPower;

    /**
     * 计划额定总电量，KWh
     */
    @Excel(name = "计划额定总电量，KWh")
    private Long planGenRatedElectricity;

    public void setPlanGenId(Long planGenId) {
        this.planGenId = planGenId;
    }

    public Long getPlanGenId() {
        return planGenId;
    }

    public void setPlanGenName(String planGenName) {
        this.planGenName = planGenName;
    }

    public String getPlanGenName() {
        return planGenName;
    }

    public void setPlanGenStatus(String planGenStatus) {
        this.planGenStatus = planGenStatus;
    }

    public String getPlanGenStatus() {
        return planGenStatus;
    }

    public void setPlanGenVpp(String planGenVpp) {
        this.planGenVpp = planGenVpp;
    }

    public String getPlanGenVpp() {
        return planGenVpp;
    }

    public void setPlanGenActivity(String planGenActivity) {
        this.planGenActivity = planGenActivity;
    }

    public String getPlanGenActivity() {
        return planGenActivity;
    }

    public void setPlanGenDevicesTotal(Long planGenDevicesTotal) {
        this.planGenDevicesTotal = planGenDevicesTotal;
    }

    public Long getPlanGenDevicesTotal() {
        return planGenDevicesTotal;
    }

    public void setPlanGenTime(Date planGenTime) {
        this.planGenTime = planGenTime;
    }

    public Date getPlanGenTime() {
        return planGenTime;
    }

    public void setPlanGenExpireTime(Date planGenExpireTime) {
        this.planGenExpireTime = planGenExpireTime;
    }

    public Date getPlanGenExpireTime() {
        return planGenExpireTime;
    }

    public void setPlanGenRatedPower(Long planGenRatedPower) {
        this.planGenRatedPower = planGenRatedPower;
    }

    public Long getPlanGenRatedPower() {
        return planGenRatedPower;
    }

    public void setPlanGenRatedElectricity(Long planGenRatedElectricity) {
        this.planGenRatedElectricity = planGenRatedElectricity;
    }

    public Long getPlanGenRatedElectricity() {
        return planGenRatedElectricity;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("planGenId", getPlanGenId())
                .append("planGenName", getPlanGenName())
                .append("planGenStatus", getPlanGenStatus())
                .append("planGenVpp", getPlanGenVpp())
                .append("planGenActivity", getPlanGenActivity())
                .append("planGenDevicesTotal", getPlanGenDevicesTotal())
                .append("planGenTime", getPlanGenTime())
                .append("planGenExpireTime", getPlanGenExpireTime())
                .append("planGenRatedPower", getPlanGenRatedPower())
                .append("planGenRatedElectricity", getPlanGenRatedElectricity())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}