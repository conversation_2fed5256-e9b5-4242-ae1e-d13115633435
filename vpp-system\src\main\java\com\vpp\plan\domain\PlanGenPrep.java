package com.vpp.plan.domain;

import com.vpp.common.annotation.Excel;
import com.vpp.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 预备设备，当出现异常时，使用预备设备对象 plan_gen_prep
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public class PlanGenPrep extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 预备设备id
     */
    private Long planGenPrepId;

    /**
     * 自动生成计划表
     */
    @Excel(name = "自动生成计划表")
    private Long planGenId;

    /**
     * 设备id
     */
    @Excel(name = "设备id")
    private Long deviceId;

    /**
     * 额定电流
     */
    @Excel(name = "额定电流")
    private Long deviceRatedCurrent;

    /**
     * 额定功率
     */
    @Excel(name = "额定功率")
    private Long deviceRatedPower;

    /**
     * 额定电流
     */
    @Excel(name = "额定电流")
    private Long deviceRatedVoltage;

    public void setPlanGenPrepId(Long planGenPrepId) {
        this.planGenPrepId = planGenPrepId;
    }

    public Long getPlanGenPrepId() {
        return planGenPrepId;
    }

    public void setPlanGenId(Long planGenId) {
        this.planGenId = planGenId;
    }

    public Long getPlanGenId() {
        return planGenId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceRatedCurrent(Long deviceRatedCurrent) {
        this.deviceRatedCurrent = deviceRatedCurrent;
    }

    public Long getDeviceRatedCurrent() {
        return deviceRatedCurrent;
    }

    public void setDeviceRatedPower(Long deviceRatedPower) {
        this.deviceRatedPower = deviceRatedPower;
    }

    public Long getDeviceRatedPower() {
        return deviceRatedPower;
    }

    public void setDeviceRatedVoltage(Long deviceRatedVoltage) {
        this.deviceRatedVoltage = deviceRatedVoltage;
    }

    public Long getDeviceRatedVoltage() {
        return deviceRatedVoltage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("planGenPrepId", getPlanGenPrepId())
                .append("planGenId", getPlanGenId())
                .append("deviceId", getDeviceId())
                .append("deviceRatedCurrent", getDeviceRatedCurrent())
                .append("deviceRatedPower", getDeviceRatedPower())
                .append("deviceRatedVoltage", getDeviceRatedVoltage())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}