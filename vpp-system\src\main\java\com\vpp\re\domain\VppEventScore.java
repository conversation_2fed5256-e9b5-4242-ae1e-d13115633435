package com.vpp.re.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评分综合与明细表（合并后）
 * 对应数据库表：vpp_event_score
 */
@Data // Lombok 自动生成 getter/setter/toString 等方法
@ApiModel(value = "VppEventScore", description = "评分综合与明细表实体")
public class VppEventScore implements Serializable {

    private static final long serialVersionUID = 1L; // 序列化版本号（分布式系统必需）

    /**
     * 主键ID（自增）
     */
    @ApiModelProperty(value = "主键ID", example = "1", required = true)
    private Long itemId;

    /**
     * 关联邀约计划ID（外键）
     */
    @ApiModelProperty(value = "关联邀约计划ID", example = "44", required = true)
    private Long invitationId;

    /**
     * 综合评定等级（A/B/C/D）
     */
    @ApiModelProperty(value = "综合评定等级（A/B/C/D）", example = "A", required = true)
    private String compositeGrade;

    /**
     * 综合得分（如：98.50）
     */
    @ApiModelProperty(value = "综合得分", example = "98.50", required = true)
    private BigDecimal compositeScore;

    /**
     * 评分时间（综合评分时间）
     */
    @ApiModelProperty(value = "评分时间（综合评分时间）", example = "2025-07-30 10:00:00", required = true)
    private LocalDateTime scoreTime;

    /**
     * 评分操作人ID（关联sys_user表）
     */
    @ApiModelProperty(value = "评分操作人ID", example = "101")
    private Long operatorId;

    /**
     * 评分操作人姓名
     */
    @ApiModelProperty(value = "评分操作人姓名", example = "张三")
    private String operatorName;

    /**
     * 评分项目名称（如：响应电量）
     */
    @ApiModelProperty(value = "评分项目名称（如：响应电量）", example = "响应电量", required = true)
    private String itemName;

    /**
     * 单位（KW/KWH/%等）
     */
    @ApiModelProperty(value = "单位（KW/KWH/%等）", example = "KWH", required = true)
    private String unit;

    /**
     * 目标量（纯数字）
     */
    @ApiModelProperty(value = "目标量（纯数字）", example = "1000.00", required = true)
    private BigDecimal targetValue;

    /**
     * 实际量（纯数字）
     */
    @ApiModelProperty(value = "实际量（纯数字）", example = "950.00", required = true)
    private BigDecimal actualValue;

    /**
     * 完成率（%）
     */
    @ApiModelProperty(value = "完成率（%）", example = "95.00", required = true)
    private BigDecimal completionRate;

    /**
     * 评分等级（A/B/C/D）
     */
    @ApiModelProperty(value = "评分等级（A/B/C/D）", example = "A", required = true)
    private String grade;

    /**
     * 评分规则（如：A:完整率≥95%）
     */
    @ApiModelProperty(value = "评分规则（如：A:完整率≥95%）", example = "A:完成率≥95%", required = true)
    private String gradeRule;

    /**
     * 评分时间（明细评分时间）
     */
    @ApiModelProperty(value = "评分时间（明细评分时间）", example = "2025-07-30 10:05:00", required = true)
    private LocalDateTime itemTime;

    /**
     * 创建者（系统用户账号）
     */
    @ApiModelProperty(value = "创建者（系统用户账号）", example = "admin")
    private String createBy;

    /**
     * 创建时间（自动填充，默认当前时间）
     */
    @ApiModelProperty(value = "创建时间", example = "2025-07-30 10:00:00", required = true)
    private LocalDateTime createTime;

    /**
     * 更新者（系统用户账号）
     */
    @ApiModelProperty(value = "更新者（系统用户账号）", example = "admin")
    private String updateBy;

    /**
     * 最后更新时间（自动填充，默认当前时间）
     */
    @ApiModelProperty(value = "最后更新时间", example = "2025-07-30 10:00:00", required = true)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0-未删除，2-已删除）
     */
    @ApiModelProperty(value = "删除标志（0-未删除，2-已删除）", example = "0", required = true)
    private String delFlag;
}