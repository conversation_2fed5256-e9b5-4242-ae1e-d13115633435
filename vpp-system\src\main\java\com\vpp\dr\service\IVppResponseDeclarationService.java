package com.vpp.dr.service;

import com.vpp.dr.domain.VppResponseDeclaration;

import java.util.List;
import java.util.Map;

public interface IVppResponseDeclarationService {
    /**
     * 分页查询响应申报列表（含邀约计划信息）
     * @param params 查询条件
     * @return 列表
     */
    List<VppResponseDeclaration> queryList(Map<String, Object> params);

    /**
     * 查询总记录数
     * @param params 查询条件
     * @return 总数
     */
    int queryTotal(Map<String, Object> params);

    /**
     * 根据ID查询详情（含邀约计划信息）
     * @param declarationId 申报ID
     * @return 实体
     */
    VppResponseDeclaration getById(Long declarationId);

    /**
     * 根据邀约计划ID查询申报记录
     * @param invitationId 邀约计划ID
     * @return 实体
     */
    VppResponseDeclaration getByInvitationId(Long invitationId);

    /**
     * 新增响应申报
     * @param declaration 实体
     * @return 结果
     */
    boolean save(VppResponseDeclaration declaration);

    /**
     * 更新响应申报
     * @param declaration 实体
     * @return 结果
     */
    boolean update(VppResponseDeclaration declaration);

    /**
     * 删除响应申报（逻辑删除）
     * @param declarationId 申报ID
     * @return 结果
     */
    boolean remove(Long declarationId);

    /**
     * 修改申报状态
     * @param declarationId 申报ID
     * @return 结果
     */
    boolean updateDeclarationStatus(Long declarationId);

    /**
     * 批量导入响应申报
     * @param list 数据列表
     * @return 结果
     */
    boolean importData(List<VppResponseDeclaration> list);
}