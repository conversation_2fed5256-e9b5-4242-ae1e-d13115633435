package com.vpp.web.controller.mgr;

import com.vpp.common.core.controller.BaseController;
import com.vpp.common.core.domain.AjaxResult;
import com.vpp.common.core.page.TableDataInfo;
import com.vpp.mgr.domain.VppMgrUagAccountNum;
import com.vpp.mgr.service.IVppMgrUagAccountNumService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 聚合商用户-户号 关联Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/mrg/uagAccountNum")
@Api(tags = "聚合商用户-户号管理")
public class VppMgrUagAccountNumController extends BaseController {
    @Autowired
    private IVppMgrUagAccountNumService vppMgrUagAccountNumService;

    /**
     * 查询聚合商用户-户号 关联列表
     */
    // @PreAuthorize("@ss.hasPermi('mrg:uagAccountNum:list')")
    @GetMapping("/list")
    @ApiOperation("查询聚合商用户-户号 列表")
    public TableDataInfo list(VppMgrUagAccountNum vppMgrUagAccountNum) {
        startPage();
        List<VppMgrUagAccountNum> list = vppMgrUagAccountNumService.selectVppMgrUagAccountNumList(vppMgrUagAccountNum);
        return getDataTable(list);
    }

    /**
     * 导出聚合商用户-户号 关联列表
     */
    // @PreAuthorize("@ss.hasPermi('mrg:uagAccountNum:export')")
    // @Log(title = "聚合商用户-户号 关联", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, VppMgrUagAccountNum vppMgrUagAccountNum) {
    //     List<VppMgrUagAccountNum> list = vppMgrUagAccountNumService.selectVppMgrUagAccountNumList(vppMgrUagAccountNum);
    //     ExcelUtil<VppMgrUagAccountNum> util = new ExcelUtil<VppMgrUagAccountNum>(VppMgrUagAccountNum.class);
    //     util.exportExcel(response, list, "聚合商用户-户号 关联数据");
    // }

    /**
     * 获取聚合商用户-户号 关联详细信息
     */
    // @PreAuthorize("@ss.hasPermi('mrg:uagAccountNum:query')")
    // @GetMapping(value = "/{userId}")
    // public AjaxResult getInfo(@PathVariable("userId") Long userId) {
    //     return success(vppMgrUagAccountNumService.selectVppMgrUagAccountNumByUserId(userId));
    // }

    /**
     * 新增聚合商用户-户号 关联
     */
    // @PreAuthorize("@ss.hasPermi('mrg:uagAccountNum:add')")
    // @Log(title = "聚合商用户-户号 关联", businessType = BusinessType.INSERT)
    // @PostMapping
    // public AjaxResult add(@RequestBody VppMgrUagAccountNum vppMgrUagAccountNum) {
    //     return toAjax(vppMgrUagAccountNumService.insertVppMgrUagAccountNum(vppMgrUagAccountNum));
    // }

    /**
     * 修改聚合商用户-户号 关联
     */
    // @PreAuthorize("@ss.hasPermi('mrg:uagAccountNum:edit')")
    // @Log(title = "聚合商用户-户号 关联", businessType = BusinessType.UPDATE)
    // @PutMapping
    // public AjaxResult edit(@RequestBody VppMgrUagAccountNum vppMgrUagAccountNum) {
    //     return toAjax(vppMgrUagAccountNumService.updateVppMgrUagAccountNum(vppMgrUagAccountNum));
    // }

    /**
     * 删除聚合商用户-户号 关联
     */
    // @PreAuthorize("@ss.hasPermi('mrg:uagAccountNum:remove')")
    // @Log(title = "聚合商用户-户号 关联", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{userIds}")
    // public AjaxResult remove(@PathVariable Long[] userIds) {
    //     return toAjax(vppMgrUagAccountNumService.deleteVppMgrUagAccountNumByUserIds(userIds));
    // }

    // 根据聚合商用户的机构id 查询所属用户账号列表
    @GetMapping("/selectByDeptId/{deptId}")
    @ApiOperation("根据聚合商用户的机构id 获取用户账号列表")
    public AjaxResult selectByDeptId(@PathVariable("deptId") Long deptId) {
        return success(vppMgrUagAccountNumService.selectByDeptId(deptId));
    }
}