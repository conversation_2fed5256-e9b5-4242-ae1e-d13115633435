package com.vpp.sm.service.impl;

import com.vpp.sm.domain.VppSettlementRecord;
import com.vpp.sm.mapper.VppSettlementRecordMapper;
import com.vpp.sm.service.IVppSettlementRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 结算记录服务实现类
 */
@Service
public class VppSettlementRecordServiceImpl implements IVppSettlementRecordService {

    @Autowired
    private VppSettlementRecordMapper settlementRecordMapper;

    /**
     * 根据邀约计划ID查询结算记录列表
     * @param invitationId 邀约计划ID
     * @return 结算记录列表
     */
    @Override
    public List<VppSettlementRecord> getRecordsByInvitationId(Long invitationId) {
        return settlementRecordMapper.selectByInvitationId(invitationId);
    }

    /**
     * 插入新的结算记录
     * @param record 结算记录实体（需包含invitationId、settlementTime等必填字段）
     * @return 成功插入的记录数（正常为1）
     */
    @Override
    public int insertRecord(VppSettlementRecord record) {
        // 可添加业务校验（如时间格式、数值合法性）
        return settlementRecordMapper.insert(record);
    }

    /**
     * 获取所有结算记录（用于统计概览）
     * @return 结算记录列表
     */
    @Override
    public List<VppSettlementRecord> getAllRecords() {
        return settlementRecordMapper.selectAll();
    }
}