import { sm4 } from 'sm-crypto'
import { getSM4Config, logSM4Debug } from '@/config/sm4'

/**
 * SM4加密
 * @param {string} txt 要加密的文本
 * @returns {string} 加密后的十六进制字符串
 */
export function encrypt(txt) {
  try {
    if (!txt) return ''

    const config = getSM4Config()

    // 记录加密信息用于调试
    logSM4Debug('加密', {
      输入: txt,
      密钥: config.key,
      输入长度: txt.length,
      模式: config.mode,
      填充: config.padding,
      IV: config.iv || 'N/A'
    })

    // 使用SM4加密
    const encrypted = config.mode === 'cbc'
      ? sm4.encrypt(txt, config.key, { mode: 'cbc', iv: config.iv })
      : sm4.encrypt(txt, config.key)

    logSM4Debug('加密', {
      输出: encrypted,
      输出长度: encrypted.length
    })

    return encrypted
  } catch (error) {
    console.error('[SM4加密] 失败:', error)
    console.error('[SM4加密] 错误详情:', {
      input: txt,
      error: error.message,
      stack: error.stack
    })
    return ''
  }
}

/**
 * SM4解密
 * @param {string} txt 要解密的十六进制字符串
 * @returns {string} 解密后的明文
 */
export function decrypt(txt) {
  try {
    if (!txt) return ''

    const config = getSM4Config()

    // 记录解密信息用于调试
    logSM4Debug('解密', {
      输入: txt,
      密钥: config.key,
      输入长度: txt.length,
      模式: config.mode,
      填充: config.padding,
      IV: config.iv || 'N/A'
    })

    // 使用SM4解密
    const decrypted = config.mode === 'cbc'
      ? sm4.decrypt(txt, config.key, { mode: 'cbc', iv: config.iv })
      : sm4.decrypt(txt, config.key)

    logSM4Debug('解密', {
      输出: decrypted,
      输出长度: decrypted.length
    })

    return decrypted
  } catch (error) {
    console.error('[SM4解密] 失败:', error)
    console.error('[SM4解密] 错误详情:', {
      input: txt,
      error: error.message,
      stack: error.stack
    })
    return ''
  }
}

