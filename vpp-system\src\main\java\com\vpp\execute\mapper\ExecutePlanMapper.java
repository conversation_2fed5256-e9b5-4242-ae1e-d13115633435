package com.vpp.execute.mapper;

import com.vpp.execute.domain.ExecutePlan;

import java.util.List;

/**
 * 执行计划，中标后的执行计划Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ExecutePlanMapper {
    /**
     * 查询执行计划，中标后的执行计划
     *
     * @param executePlanId 执行计划，中标后的执行计划主键
     * @return 执行计划，中标后的执行计划
     */
    public ExecutePlan selectExecutePlanByExecutePlanId(Long executePlanId);

    /**
     * 查询执行计划，中标后的执行计划列表
     *
     * @param executePlan 执行计划，中标后的执行计划
     * @return 执行计划，中标后的执行计划集合
     */
    public List<ExecutePlan> selectExecutePlanList(ExecutePlan executePlan);

    /**
     * 新增执行计划，中标后的执行计划
     *
     * @param executePlan 执行计划，中标后的执行计划
     * @return 结果
     */
    public int insertExecutePlan(ExecutePlan executePlan);

    /**
     * 修改执行计划，中标后的执行计划
     *
     * @param executePlan 执行计划，中标后的执行计划
     * @return 结果
     */
    public int updateExecutePlan(ExecutePlan executePlan);

    /**
     * 删除执行计划，中标后的执行计划
     *
     * @param executePlanId 执行计划，中标后的执行计划主键
     * @return 结果
     */
    public int deleteExecutePlanByExecutePlanId(Long executePlanId);

    /**
     * 批量删除执行计划，中标后的执行计划
     *
     * @param executePlanIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExecutePlanByExecutePlanIds(Long[] executePlanIds);
}
